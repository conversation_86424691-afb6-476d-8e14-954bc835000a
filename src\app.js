/**
 * التطبيق الرئيسي - App
 * نقطة الدخول الرئيسية لتطبيق المحادثات المطور
 * يدير تهيئة وتشغيل جميع مكونات التطبيق
 */

import { APP_INFO } from './config/constants.js';
import eventBus from './core/EventBus.js';
import stateManager from './core/StateManager.js';
import errorHandler from './core/ErrorHandler.js';
import databaseManager from './core/DatabaseManager.js';
import apiClient from './core/ApiClient.js';
import authService from './services/AuthService.js';
import chatService from './services/ChatService.js';
import LoadingComponent from './components/Common/LoadingComponent.js';
import NotificationComponent from './components/Common/NotificationComponent.js';

/**
 * كلاس التطبيق الرئيسي
 */
class App {
  constructor() {
    // حالة التطبيق
    this.isInitialized = false;
    this.isStarted = false;
    this.startTime = null;
    
    // مكونات التطبيق
    this.components = new Map();
    this.services = new Map();
    
    // إعدادات التطبيق
    this.config = {
      debug: false,
      enableServiceWorker: true,
      enableNotifications: true,
      autoStart: true
    };
    
    // مؤشر التحميل الرئيسي
    this.loadingIndicator = null;
    
    console.log(`🚀 تطبيق ${APP_INFO.NAME} v${APP_INFO.VERSION}`);
    console.log(`📅 تاريخ البناء: ${APP_INFO.BUILD}`);
  }

  /**
   * تهيئة التطبيق
   * @param {Object} options - خيارات التهيئة
   * @returns {Promise<boolean>} نجحت التهيئة أم لا
   */
  async initialize(options = {}) {
    if (this.isInitialized) {
      console.warn('⚠️ التطبيق مهيأ مسبقاً');
      return true;
    }

    try {
      console.log('🔧 بدء تهيئة التطبيق...');
      
      // دمج الخيارات
      this.config = { ...this.config, ...options };
      
      // إظهار مؤشر التحميل
      this._showLoadingIndicator('تهيئة التطبيق...');
      
      // تهيئة معالج الأخطاء
      this._initializeErrorHandler();
      
      // تهيئة مدير الحالة
      this._initializeStateManager();
      
      // تهيئة قاعدة البيانات
      await this._initializeDatabase();
      
      // تهيئة عميل API
      this._initializeApiClient();
      
      // تهيئة الخدمات
      await this._initializeServices();
      
      // تهيئة مستمعي الأحداث
      this._initializeEventListeners();
      
      // تهيئة Service Worker
      if (this.config.enableServiceWorker) {
        await this._initializeServiceWorker();
      }
      
      // تهيئة الإشعارات
      if (this.config.enableNotifications) {
        await this._initializeNotifications();
      }
      
      this.isInitialized = true;
      
      // إخفاء مؤشر التحميل
      this._hideLoadingIndicator();
      
      // إطلاق حدث التهيئة
      eventBus.emit('app:initialized', {
        version: APP_INFO.VERSION,
        timestamp: new Date()
      });
      
      console.log('✅ تم تهيئة التطبيق بنجاح');
      
      // بدء التطبيق تلقائياً إذا كان مفعلاً
      if (this.config.autoStart) {
        await this.start();
      }
      
      return true;

    } catch (error) {
      console.error('❌ فشل في تهيئة التطبيق:', error);
      
      this._hideLoadingIndicator();
      
      // إظهار رسالة خطأ للمستخدم
      this._showErrorNotification('فشل في تهيئة التطبيق', error.message);
      
      errorHandler.handleError(error, {
        type: 'system',
        operation: 'app:initialize',
        critical: true
      });
      
      return false;
    }
  }

  /**
   * بدء تشغيل التطبيق
   * @returns {Promise<boolean>} نجح البدء أم لا
   */
  async start() {
    if (!this.isInitialized) {
      console.error('❌ يجب تهيئة التطبيق أولاً');
      return false;
    }

    if (this.isStarted) {
      console.warn('⚠️ التطبيق يعمل بالفعل');
      return true;
    }

    try {
      console.log('🚀 بدء تشغيل التطبيق...');
      
      this.startTime = new Date();
      
      // بدء الخدمات
      await this._startServices();
      
      // تحميل واجهة المستخدم
      await this._loadUserInterface();
      
      // التحقق من المصادقة
      await this._checkAuthentication();
      
      this.isStarted = true;
      
      // إطلاق حدث البدء
      eventBus.emit('app:started', {
        startTime: this.startTime,
        timestamp: new Date()
      });
      
      console.log('✅ تم بدء تشغيل التطبيق بنجاح');
      
      // إظهار إشعار الترحيب
      this._showWelcomeNotification();
      
      return true;

    } catch (error) {
      console.error('❌ فشل في بدء تشغيل التطبيق:', error);
      
      errorHandler.handleError(error, {
        type: 'system',
        operation: 'app:start',
        critical: true
      });
      
      return false;
    }
  }

  /**
   * إيقاف التطبيق
   * @returns {Promise<boolean>} نجح الإيقاف أم لا
   */
  async stop() {
    if (!this.isStarted) {
      return true;
    }

    try {
      console.log('🛑 إيقاف التطبيق...');
      
      // إيقاف الخدمات
      await this._stopServices();
      
      // تنظيف المكونات
      this._cleanupComponents();
      
      // إغلاق قاعدة البيانات
      databaseManager.close();
      
      this.isStarted = false;
      
      // إطلاق حدث الإيقاف
      eventBus.emit('app:stopped', {
        stopTime: new Date(),
        uptime: Date.now() - this.startTime?.getTime()
      });
      
      console.log('✅ تم إيقاف التطبيق بنجاح');
      
      return true;

    } catch (error) {
      console.error('❌ فشل في إيقاف التطبيق:', error);
      
      errorHandler.handleError(error, {
        type: 'system',
        operation: 'app:stop'
      });
      
      return false;
    }
  }

  /**
   * إعادة تشغيل التطبيق
   * @returns {Promise<boolean>} نجحت الإعادة أم لا
   */
  async restart() {
    console.log('🔄 إعادة تشغيل التطبيق...');
    
    const stopped = await this.stop();
    if (stopped) {
      return await this.start();
    }
    
    return false;
  }

  /**
   * الحصول على معلومات التطبيق
   * @returns {Object} معلومات التطبيق
   */
  getInfo() {
    return {
      ...APP_INFO,
      isInitialized: this.isInitialized,
      isStarted: this.isStarted,
      startTime: this.startTime,
      uptime: this.startTime ? Date.now() - this.startTime.getTime() : 0,
      config: this.config
    };
  }

  /**
   * الحصول على إحصائيات التطبيق
   * @returns {Object} الإحصائيات
   */
  getStats() {
    return {
      app: this.getInfo(),
      database: databaseManager.getStats(),
      api: apiClient.getStats(),
      auth: authService.isUserAuthenticated(),
      chat: chatService.getStats(),
      errors: errorHandler.getStats(),
      state: stateManager.getStats(),
      events: eventBus.getStats()
    };
  }

  /**
   * تحديث إعدادات التطبيق
   * @param {Object} newConfig - الإعدادات الجديدة
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    
    // تطبيق الإعدادات على المكونات
    if (newConfig.debug !== undefined) {
      this._setDebugMode(newConfig.debug);
    }
    
    eventBus.emit('app:config:updated', {
      config: this.config,
      timestamp: new Date()
    });
  }

  /**
   * تفعيل/إلغاء وضع التشخيص
   * @param {boolean} enabled - تفعيل التشخيص
   */
  setDebugMode(enabled) {
    this.config.debug = enabled;
    this._setDebugMode(enabled);
  }

  // دوال خاصة

  /**
   * تهيئة معالج الأخطاء
   * @private
   */
  _initializeErrorHandler() {
    errorHandler.setDebug(this.config.debug);
    
    // تسجيل معالجات مخصصة
    errorHandler.registerHandler('system', (error) => {
      if (error.severity === 'critical') {
        this._showErrorNotification('خطأ حرج', error.message);
      }
    });
    
    console.log('🛡️ تم تهيئة معالج الأخطاء');
  }

  /**
   * تهيئة مدير الحالة
   * @private
   */
  _initializeStateManager() {
    stateManager.setDebug(this.config.debug);
    
    // تعيين الحالة الأولية
    stateManager.set('app', {
      name: APP_INFO.NAME,
      version: APP_INFO.VERSION,
      isInitialized: false,
      isStarted: false
    });
    
    console.log('🗃️ تم تهيئة مدير الحالة');
  }

  /**
   * تهيئة قاعدة البيانات
   * @private
   */
  async _initializeDatabase() {
    databaseManager.setDebug(this.config.debug);
    
    const connected = await databaseManager.initialize();
    if (!connected) {
      throw new Error('فشل في الاتصال بقاعدة البيانات');
    }
    
    console.log('🗄️ تم تهيئة قاعدة البيانات');
  }

  /**
   * تهيئة عميل API
   * @private
   */
  _initializeApiClient() {
    apiClient.setDebug(this.config.debug);
    
    // تعيين URL الأساسي من متغيرات البيئة أو الإعدادات
    const baseURL = process.env.API_BASE_URL || 'https://api.example.com';
    apiClient.baseURL = baseURL;
    
    console.log('🌐 تم تهيئة عميل API');
  }

  /**
   * تهيئة الخدمات
   * @private
   */
  async _initializeServices() {
    // تسجيل الخدمات
    this.services.set('auth', authService);
    this.services.set('chat', chatService);
    
    // تهيئة خدمة المصادقة
    authService.setDebug(this.config.debug);
    await authService.initialize();
    
    // تهيئة خدمة المحادثات
    chatService.setDebug(this.config.debug);
    await chatService.initialize();
    
    console.log('🔧 تم تهيئة الخدمات');
  }

  /**
   * تهيئة مستمعي الأحداث
   * @private
   */
  _initializeEventListeners() {
    // أحداث النظام
    eventBus.on('error:occurred', (error) => {
      if (error.severity === 'critical') {
        console.error('💥 خطأ حرج في النظام:', error);
      }
    });
    
    // أحداث المصادقة
    eventBus.on('auth:session:expired', () => {
      this._handleSessionExpired();
    });
    
    // أحداث الشبكة
    window.addEventListener('online', () => {
      this._handleNetworkStatusChange(true);
    });
    
    window.addEventListener('offline', () => {
      this._handleNetworkStatusChange(false);
    });
    
    // أحداث النافذة
    window.addEventListener('beforeunload', () => {
      this.stop();
    });
    
    console.log('👂 تم تهيئة مستمعي الأحداث');
  }

  /**
   * تهيئة Service Worker
   * @private
   */
  async _initializeServiceWorker() {
    if ('serviceWorker' in navigator) {
      try {
        const registration = await navigator.serviceWorker.register('/sw.js');
        console.log('👷 تم تسجيل Service Worker:', registration);
        
        eventBus.emit('app:service-worker:registered', { registration });
      } catch (error) {
        console.warn('⚠️ فشل في تسجيل Service Worker:', error);
      }
    }
  }

  /**
   * تهيئة الإشعارات
   * @private
   */
  async _initializeNotifications() {
    if ('Notification' in window) {
      if (Notification.permission === 'default') {
        await Notification.requestPermission();
      }
      
      console.log('🔔 تم تهيئة الإشعارات');
    }
  }

  /**
   * بدء الخدمات
   * @private
   */
  async _startServices() {
    // الخدمات تبدأ تلقائياً عند التهيئة
    console.log('🔧 تم بدء الخدمات');
  }

  /**
   * تحميل واجهة المستخدم
   * @private
   */
  async _loadUserInterface() {
    // تحميل المكونات الأساسية
    // هذا سيتم تطويره في ملفات منفصلة
    
    console.log('🎨 تم تحميل واجهة المستخدم');
  }

  /**
   * التحقق من المصادقة
   * @private
   */
  async _checkAuthentication() {
    const isAuthenticated = authService.isUserAuthenticated();
    
    if (isAuthenticated) {
      // التحقق من صحة الجلسة
      const isValid = await authService.validateSession();
      if (!isValid) {
        await authService.logout();
      }
    }
    
    // تحديث الحالة
    stateManager.set('auth.isAuthenticated', authService.isUserAuthenticated());
    stateManager.set('auth.currentUser', authService.getCurrentUser()?.toObject());
  }

  /**
   * إيقاف الخدمات
   * @private
   */
  async _stopServices() {
    // إيقاف الخدمات إذا لزم الأمر
    console.log('🛑 تم إيقاف الخدمات');
  }

  /**
   * تنظيف المكونات
   * @private
   */
  _cleanupComponents() {
    for (const [name, component] of this.components) {
      if (component.destroy) {
        component.destroy();
      }
    }
    
    this.components.clear();
    console.log('🧹 تم تنظيف المكونات');
  }

  /**
   * إظهار مؤشر التحميل
   * @private
   */
  _showLoadingIndicator(message = 'جاري التحميل...') {
    if (!this.loadingIndicator) {
      this.loadingIndicator = LoadingComponent.create({
        message,
        overlay: true,
        type: 'spinner'
      });
    } else {
      this.loadingIndicator.updateMessage(message);
    }
    
    this.loadingIndicator.show();
  }

  /**
   * إخفاء مؤشر التحميل
   * @private
   */
  _hideLoadingIndicator() {
    if (this.loadingIndicator) {
      this.loadingIndicator.hide();
    }
  }

  /**
   * إظهار إشعار خطأ
   * @private
   */
  _showErrorNotification(title, message) {
    NotificationComponent.error(message, {
      title,
      persistent: true,
      actions: [
        {
          id: 'reload',
          label: 'إعادة تحميل',
          handler: () => window.location.reload()
        }
      ]
    }).show();
  }

  /**
   * إظهار إشعار الترحيب
   * @private
   */
  _showWelcomeNotification() {
    const user = authService.getCurrentUser();
    
    if (user) {
      NotificationComponent.success(`مرحباً ${user.getDisplayName()}!`, {
        title: 'تم تسجيل الدخول بنجاح',
        duration: 3000
      }).show();
    }
  }

  /**
   * معالجة انتهاء الجلسة
   * @private
   */
  _handleSessionExpired() {
    NotificationComponent.warning('انتهت صلاحية الجلسة', {
      title: 'تسجيل الدخول مطلوب',
      persistent: true,
      actions: [
        {
          id: 'login',
          label: 'تسجيل الدخول',
          handler: () => {
            // توجيه إلى صفحة تسجيل الدخول
            window.location.href = '/login';
          }
        }
      ]
    }).show();
  }

  /**
   * معالجة تغيير حالة الشبكة
   * @private
   */
  _handleNetworkStatusChange(isOnline) {
    const message = isOnline ? 'تم استعادة الاتصال' : 'انقطع الاتصال بالإنترنت';
    const type = isOnline ? 'success' : 'warning';
    
    NotificationComponent.create(type, message, {
      duration: 3000
    }).show();
    
    // تحديث الحالة
    stateManager.set('app.isOnline', isOnline);
    
    // إطلاق حدث
    eventBus.emit('app:network:status:changed', { isOnline });
  }

  /**
   * تطبيق وضع التشخيص
   * @private
   */
  _setDebugMode(enabled) {
    // تطبيق على جميع المكونات
    eventBus.setDebug(enabled);
    stateManager.setDebug(enabled);
    errorHandler.setDebug(enabled);
    databaseManager.setDebug(enabled);
    apiClient.setDebug(enabled);
    authService.setDebug(enabled);
    chatService.setDebug(enabled);
    
    console.log(`🔍 وضع التشخيص: ${enabled ? 'مفعل' : 'معطل'}`);
  }
}

// إنشاء مثيل التطبيق الرئيسي
const app = new App();

// تصدير المثيل والكلاس
export default app;
export { App };

// بدء التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', async () => {
  try {
    await app.initialize();
    console.log('🎉 تم تشغيل التطبيق بنجاح!');
  } catch (error) {
    console.error('💥 فشل في تشغيل التطبيق:', error);
  }
});
