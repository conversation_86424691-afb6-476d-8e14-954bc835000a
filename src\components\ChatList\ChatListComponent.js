/**
 * مكون قائمة المحادثات - ChatListComponent
 * يعرض قائمة جميع المحادثات مع إمكانيات البحث والفلترة
 * يدير التفاعل مع المحادثات والتنقل بينها
 */

import BaseComponent from '../Common/BaseComponent.js';
import ChatListItem from './ChatListItem.js';
import ChatListManager from './ChatListManager.js';
import eventBus from '../../core/EventBus.js';
import stateManager from '../../core/StateManager.js';
import { DOMUtils } from '../../utils/DOMUtils.js';
import { DateUtils } from '../../utils/DateUtils.js';

/**
 * كلاس مكون قائمة المحادثات
 */
class ChatListComponent extends BaseComponent {
  constructor(options = {}) {
    super({
      className: 'chat-list-component',
      template: 'chat-list-template',
      ...options
    });

    // إعدادات المكون
    this.config = {
      enableSearch: true,
      enableFilter: true,
      enableVirtualScrolling: true,
      itemHeight: 80,
      loadMoreThreshold: 10,
      searchDebounceTime: 300,
      ...options.config
    };

    // حالة المكون
    this.state = {
      chats: [],
      filteredChats: [],
      selectedChatId: null,
      searchQuery: '',
      filterType: 'all', // all, unread, archived, favorites
      isLoading: false,
      hasMore: true,
      currentPage: 1
    };

    // مراجع العناصر
    this.elements = {
      container: null,
      searchInput: null,
      filterButtons: null,
      chatsList: null,
      loadingIndicator: null,
      emptyState: null,
      scrollContainer: null
    };

    // مدير قائمة المحادثات
    this.chatListManager = new ChatListManager({
      component: this,
      enableVirtualScrolling: this.config.enableVirtualScrolling
    });

    // عناصر المحادثات المعروضة
    this.chatItems = new Map();

    // مؤقتات
    this.searchDebounceTimer = null;
    this.scrollThrottleTimer = null;

    console.log('💬 تم إنشاء مكون قائمة المحادثات');
  }

  /**
   * الحصول على قالب HTML للمكون
   * @returns {string} قالب HTML
   */
  getTemplate() {
    return `
      <div class="chat-list-component" data-component="chat-list">
        <!-- شريط البحث والفلترة -->
        <div class="chat-list-header">
          ${this._getSearchTemplate()}
          ${this._getFilterTemplate()}
        </div>

        <!-- قائمة المحادثات -->
        <div class="chat-list-content">
          <div class="chat-list-scroll-container" data-scroll="chat-list">
            <div class="chat-list-items" data-list="chats">
              <!-- عناصر المحادثات ستُدرج هنا -->
            </div>
            
            <!-- مؤشر التحميل -->
            <div class="chat-list-loading" data-loading="chat-list" style="display: none;">
              <div class="loading-spinner"></div>
              <span>جاري تحميل المحادثات...</span>
            </div>
          </div>

          <!-- حالة فارغة -->
          <div class="chat-list-empty" data-empty="chat-list" style="display: none;">
            <div class="empty-icon">💬</div>
            <h3>لا توجد محادثات</h3>
            <p>ابدأ محادثة جديدة للبدء</p>
            <button class="btn btn-primary" data-action="new-chat">
              <i class="icon-plus"></i>
              محادثة جديدة
            </button>
          </div>

          <!-- حالة عدم وجود نتائج بحث -->
          <div class="chat-list-no-results" data-no-results="chat-list" style="display: none;">
            <div class="no-results-icon">🔍</div>
            <h3>لا توجد نتائج</h3>
            <p>لم يتم العثور على محادثات تطابق البحث</p>
            <button class="btn btn-secondary" data-action="clear-search">
              مسح البحث
            </button>
          </div>
        </div>

        <!-- شريط الإجراءات السفلي -->
        <div class="chat-list-footer">
          <button class="btn btn-primary btn-new-chat" data-action="new-chat">
            <i class="icon-plus"></i>
            <span>محادثة جديدة</span>
          </button>
        </div>
      </div>
    `;
  }

  /**
   * تهيئة المكون بعد العرض
   */
  async onRender() {
    try {
      // الحصول على مراجع العناصر
      this._getElementReferences();

      // تهيئة الأحداث
      this._setupEventListeners();

      // تهيئة مدير قائمة المحادثات
      await this.chatListManager.initialize();

      // تحميل المحادثات الأولية
      await this._loadInitialChats();

      // تهيئة التمرير الافتراضي إذا كان مفعلاً
      if (this.config.enableVirtualScrolling) {
        this._setupVirtualScrolling();
      }

      console.log('✅ تم تهيئة مكون قائمة المحادثات');

    } catch (error) {
      console.error('❌ فشل في تهيئة مكون قائمة المحادثات:', error);
      this._showError('فشل في تحميل المحادثات');
    }
  }

  /**
   * تنظيف المكون عند الإزالة
   */
  onDestroy() {
    // إزالة المؤقتات
    if (this.searchDebounceTimer) {
      clearTimeout(this.searchDebounceTimer);
    }
    if (this.scrollThrottleTimer) {
      clearTimeout(this.scrollThrottleTimer);
    }

    // تنظيف عناصر المحادثات
    this.chatItems.clear();

    // تنظيف مدير قائمة المحادثات
    this.chatListManager.destroy();

    console.log('🧹 تم تنظيف مكون قائمة المحادثات');
  }

  // ==================== طرق عامة ====================

  /**
   * تحديث قائمة المحادثات
   * @param {Array} chats - قائمة المحادثات الجديدة
   */
  updateChats(chats) {
    this.state.chats = chats || [];
    this._applyFiltersAndSearch();
    this._renderChatItems();
  }

  /**
   * إضافة محادثة جديدة
   * @param {Object} chat - بيانات المحادثة
   */
  addChat(chat) {
    if (!chat || !chat.id) return;

    // التحقق من عدم وجود المحادثة مسبقاً
    const existingIndex = this.state.chats.findIndex(c => c.id === chat.id);
    
    if (existingIndex === -1) {
      // إضافة في المقدمة
      this.state.chats.unshift(chat);
    } else {
      // تحديث المحادثة الموجودة
      this.state.chats[existingIndex] = chat;
    }

    this._applyFiltersAndSearch();
    this._renderChatItems();
  }

  /**
   * تحديث محادثة موجودة
   * @param {Object} updatedChat - بيانات المحادثة المحدثة
   */
  updateChat(updatedChat) {
    if (!updatedChat || !updatedChat.id) return;

    const index = this.state.chats.findIndex(c => c.id === updatedChat.id);
    
    if (index !== -1) {
      this.state.chats[index] = { ...this.state.chats[index], ...updatedChat };
      
      // إعادة ترتيب حسب آخر نشاط
      this._sortChatsByActivity();
      
      this._applyFiltersAndSearch();
      this._renderChatItems();
    }
  }

  /**
   * حذف محادثة
   * @param {string} chatId - معرف المحادثة
   */
  removeChat(chatId) {
    this.state.chats = this.state.chats.filter(c => c.id !== chatId);
    
    // إزالة عنصر المحادثة من DOM
    if (this.chatItems.has(chatId)) {
      const chatItem = this.chatItems.get(chatId);
      chatItem.destroy();
      this.chatItems.delete(chatId);
    }

    this._applyFiltersAndSearch();
    this._renderChatItems();
  }

  /**
   * تحديد محادثة
   * @param {string} chatId - معرف المحادثة
   */
  selectChat(chatId) {
    // إلغاء تحديد المحادثة السابقة
    if (this.state.selectedChatId) {
      const prevItem = this.chatItems.get(this.state.selectedChatId);
      if (prevItem) {
        prevItem.setSelected(false);
      }
    }

    // تحديد المحادثة الجديدة
    this.state.selectedChatId = chatId;
    
    if (chatId) {
      const currentItem = this.chatItems.get(chatId);
      if (currentItem) {
        currentItem.setSelected(true);
        currentItem.scrollIntoView();
      }
    }

    // إطلاق حدث تغيير التحديد
    this.emit('chat:selected', { chatId });
  }

  /**
   * البحث في المحادثات
   * @param {string} query - نص البحث
   */
  search(query) {
    this.state.searchQuery = query.trim();
    this._applyFiltersAndSearch();
    this._renderChatItems();
  }

  /**
   * تطبيق فلتر
   * @param {string} filterType - نوع الفلتر
   */
  applyFilter(filterType) {
    this.state.filterType = filterType;
    this._applyFiltersAndSearch();
    this._renderChatItems();
    
    // تحديث أزرار الفلتر
    this._updateFilterButtons();
  }

  /**
   * تحديث عداد الرسائل غير المقروءة
   * @param {string} chatId - معرف المحادثة
   * @param {number} count - عدد الرسائل غير المقروءة
   */
  updateUnreadCount(chatId, count) {
    const chatItem = this.chatItems.get(chatId);
    if (chatItem) {
      chatItem.updateUnreadCount(count);
    }

    // تحديث بيانات المحادثة
    const chat = this.state.chats.find(c => c.id === chatId);
    if (chat) {
      chat.unreadCount = count;
    }
  }

  // ==================== دوال خاصة ====================

  /**
   * الحصول على قالب البحث
   * @private
   */
  _getSearchTemplate() {
    if (!this.config.enableSearch) return '';

    return `
      <div class="chat-search">
        <div class="search-input-container">
          <input 
            type="text" 
            class="search-input" 
            placeholder="البحث في المحادثات..."
            data-search="chat-list"
          >
          <i class="search-icon icon-search"></i>
          <button class="search-clear" data-action="clear-search" style="display: none;">
            <i class="icon-x"></i>
          </button>
        </div>
      </div>
    `;
  }

  /**
   * الحصول على قالب الفلترة
   * @private
   */
  _getFilterTemplate() {
    if (!this.config.enableFilter) return '';

    return `
      <div class="chat-filters">
        <button class="filter-btn active" data-filter="all">
          <span>الكل</span>
        </button>
        <button class="filter-btn" data-filter="unread">
          <span>غير مقروءة</span>
          <span class="filter-count" data-count="unread">0</span>
        </button>
        <button class="filter-btn" data-filter="favorites">
          <span>المفضلة</span>
        </button>
        <button class="filter-btn" data-filter="archived">
          <span>المؤرشفة</span>
        </button>
      </div>
    `;
  }

  /**
   * الحصول على مراجع العناصر
   * @private
   */
  _getElementReferences() {
    const container = this.getElement();
    
    this.elements = {
      container,
      searchInput: container.querySelector('[data-search="chat-list"]'),
      filterButtons: container.querySelectorAll('[data-filter]'),
      chatsList: container.querySelector('[data-list="chats"]'),
      loadingIndicator: container.querySelector('[data-loading="chat-list"]'),
      emptyState: container.querySelector('[data-empty="chat-list"]'),
      noResultsState: container.querySelector('[data-no-results="chat-list"]'),
      scrollContainer: container.querySelector('[data-scroll="chat-list"]')
    };
  }

  /**
   * تهيئة مستمعي الأحداث
   * @private
   */
  _setupEventListeners() {
    // أحداث البحث
    if (this.elements.searchInput) {
      this.elements.searchInput.addEventListener('input', (e) => {
        this._handleSearchInput(e.target.value);
      });

      this.elements.searchInput.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
          this._clearSearch();
        }
      });
    }

    // أحداث الفلترة
    this.elements.filterButtons.forEach(btn => {
      btn.addEventListener('click', (e) => {
        const filterType = e.currentTarget.dataset.filter;
        this.applyFilter(filterType);
      });
    });

    // أحداث الإجراءات
    this.elements.container.addEventListener('click', (e) => {
      const action = e.target.closest('[data-action]')?.dataset.action;
      
      switch (action) {
        case 'new-chat':
          this._handleNewChat();
          break;
        case 'clear-search':
          this._clearSearch();
          break;
      }
    });

    // أحداث التمرير
    if (this.elements.scrollContainer) {
      this.elements.scrollContainer.addEventListener('scroll', (e) => {
        this._handleScroll(e);
      });
    }

    // أحداث النظام
    eventBus.on('chat:updated', (data) => {
      this.updateChat(data.chat);
    });

    eventBus.on('chat:deleted', (data) => {
      this.removeChat(data.chatId);
    });

    eventBus.on('message:received', (data) => {
      this._handleNewMessage(data.message);
    });

    eventBus.on('message:status:updated', (data) => {
      this._handleMessageStatusUpdate(data);
    });

    // أحداث الحالة العامة
    stateManager.subscribe('chats.list', (chats) => {
      this.updateChats(chats);
    });

    stateManager.subscribe('chats.selected', (chatId) => {
      this.selectChat(chatId);
    });
  }

  /**
   * تحميل المحادثات الأولية
   * @private
   */
  async _loadInitialChats() {
    try {
      this._showLoading(true);

      // الحصول على المحادثات من الحالة العامة
      const chats = stateManager.get('chats.list') || [];
      
      if (chats.length === 0) {
        // تحميل من الخدمة إذا لم تكن موجودة
        await this.chatListManager.loadChats();
      } else {
        this.updateChats(chats);
      }

      this._showLoading(false);

    } catch (error) {
      console.error('❌ فشل في تحميل المحادثات الأولية:', error);
      this._showLoading(false);
      this._showError('فشل في تحميل المحادثات');
    }
  }

  /**
   * تطبيق الفلاتر والبحث
   * @private
   */
  _applyFiltersAndSearch() {
    let filteredChats = [...this.state.chats];

    // تطبيق البحث
    if (this.state.searchQuery) {
      const query = this.state.searchQuery.toLowerCase();
      filteredChats = filteredChats.filter(chat => 
        chat.name?.toLowerCase().includes(query) ||
        chat.lastMessage?.toLowerCase().includes(query)
      );
    }

    // تطبيق الفلتر
    switch (this.state.filterType) {
      case 'unread':
        filteredChats = filteredChats.filter(chat => chat.unreadCount > 0);
        break;
      case 'favorites':
        filteredChats = filteredChats.filter(chat => chat.isFavorite);
        break;
      case 'archived':
        filteredChats = filteredChats.filter(chat => chat.isArchived);
        break;
      case 'all':
      default:
        filteredChats = filteredChats.filter(chat => !chat.isArchived);
        break;
    }

    this.state.filteredChats = filteredChats;
    
    // تحديث عدادات الفلاتر
    this._updateFilterCounts();
  }

  /**
   * عرض عناصر المحادثات
   * @private
   */
  _renderChatItems() {
    if (!this.elements.chatsList) return;

    // مسح العناصر الموجودة
    this.elements.chatsList.innerHTML = '';
    this.chatItems.clear();

    const chats = this.state.filteredChats;

    // إظهار الحالة المناسبة
    if (chats.length === 0) {
      if (this.state.searchQuery) {
        this._showNoResults(true);
        this._showEmptyState(false);
      } else {
        this._showEmptyState(true);
        this._showNoResults(false);
      }
      return;
    }

    this._showEmptyState(false);
    this._showNoResults(false);

    // إنشاء عناصر المحادثات
    chats.forEach((chat, index) => {
      this._createChatItem(chat, index);
    });
  }

  /**
   * إنشاء عنصر محادثة
   * @private
   */
  _createChatItem(chat, index) {
    const chatItem = new ChatListItem({
      chat,
      index,
      isSelected: chat.id === this.state.selectedChatId,
      onClick: (chatId) => this._handleChatClick(chatId),
      onContextMenu: (chatId, event) => this._handleChatContextMenu(chatId, event)
    });

    chatItem.render();
    this.elements.chatsList.appendChild(chatItem.getElement());
    this.chatItems.set(chat.id, chatItem);
  }

  /**
   * ترتيب المحادثات حسب النشاط
   * @private
   */
  _sortChatsByActivity() {
    this.state.chats.sort((a, b) => {
      const dateA = new Date(a.lastActivity || a.lastMessageDate || a.createdDate);
      const dateB = new Date(b.lastActivity || b.lastMessageDate || b.createdDate);
      return dateB - dateA;
    });
  }

  /**
   * تحديث عدادات الفلاتر
   * @private
   */
  _updateFilterCounts() {
    const unreadCount = this.state.chats.filter(c => c.unreadCount > 0 && !c.isArchived).length;
    
    const unreadCountElement = this.elements.container.querySelector('[data-count="unread"]');
    if (unreadCountElement) {
      unreadCountElement.textContent = unreadCount;
      unreadCountElement.style.display = unreadCount > 0 ? 'inline' : 'none';
    }
  }

  /**
   * تحديث أزرار الفلتر
   * @private
   */
  _updateFilterButtons() {
    this.elements.filterButtons.forEach(btn => {
      const isActive = btn.dataset.filter === this.state.filterType;
      btn.classList.toggle('active', isActive);
    });
  }

  /**
   * معالجة إدخال البحث
   * @private
   */
  _handleSearchInput(value) {
    // إلغاء المؤقت السابق
    if (this.searchDebounceTimer) {
      clearTimeout(this.searchDebounceTimer);
    }

    // تأخير البحث لتحسين الأداء
    this.searchDebounceTimer = setTimeout(() => {
      this.search(value);
      
      // إظهار/إخفاء زر المسح
      const clearBtn = this.elements.container.querySelector('[data-action="clear-search"]');
      if (clearBtn) {
        clearBtn.style.display = value ? 'block' : 'none';
      }
    }, this.config.searchDebounceTime);
  }

  /**
   * مسح البحث
   * @private
   */
  _clearSearch() {
    if (this.elements.searchInput) {
      this.elements.searchInput.value = '';
    }
    
    this.search('');
    
    const clearBtn = this.elements.container.querySelector('[data-action="clear-search"]');
    if (clearBtn) {
      clearBtn.style.display = 'none';
    }
  }

  /**
   * معالجة النقر على محادثة
   * @private
   */
  _handleChatClick(chatId) {
    this.selectChat(chatId);
    
    // تحديث الحالة العامة
    stateManager.set('chats.selected', chatId);
    
    // إطلاق حدث
    eventBus.emit('chat:selected', { chatId });
  }

  /**
   * معالجة القائمة السياقية للمحادثة
   * @private
   */
  _handleChatContextMenu(chatId, event) {
    event.preventDefault();
    
    // إطلاق حدث القائمة السياقية
    this.emit('chat:context-menu', {
      chatId,
      x: event.clientX,
      y: event.clientY
    });
  }

  /**
   * معالجة محادثة جديدة
   * @private
   */
  _handleNewChat() {
    this.emit('chat:new');
    eventBus.emit('ui:show:new-chat-dialog');
  }

  /**
   * معالجة رسالة جديدة
   * @private
   */
  _handleNewMessage(message) {
    const chat = this.state.chats.find(c => c.id === message.chatID);
    
    if (chat) {
      // تحديث آخر رسالة
      chat.lastMessage = message.messageText;
      chat.lastMessageDate = message.createdDate;
      chat.lastActivity = new Date();
      
      // زيادة عداد الرسائل غير المقروءة إذا لم تكن المحادثة محددة
      if (message.direction === 'Incoming' && this.state.selectedChatId !== chat.id) {
        chat.unreadCount = (chat.unreadCount || 0) + 1;
      }
      
      // إعادة ترتيب وعرض
      this._sortChatsByActivity();
      this._applyFiltersAndSearch();
      this._renderChatItems();
    }
  }

  /**
   * معالجة تحديث حالة الرسالة
   * @private
   */
  _handleMessageStatusUpdate(data) {
    const chatItem = this.chatItems.get(data.chatId);
    if (chatItem) {
      chatItem.updateMessageStatus(data.messageId, data.status);
    }
  }

  /**
   * معالجة التمرير
   * @private
   */
  _handleScroll(event) {
    if (this.scrollThrottleTimer) return;

    this.scrollThrottleTimer = setTimeout(() => {
      const container = event.target;
      const scrollTop = container.scrollTop;
      const scrollHeight = container.scrollHeight;
      const clientHeight = container.clientHeight;

      // تحميل المزيد عند الوصول للنهاية
      if (scrollHeight - scrollTop - clientHeight < this.config.loadMoreThreshold) {
        this._loadMoreChats();
      }

      this.scrollThrottleTimer = null;
    }, 100);
  }

  /**
   * تحميل المزيد من المحادثات
   * @private
   */
  async _loadMoreChats() {
    if (this.state.isLoading || !this.state.hasMore) return;

    try {
      this.state.isLoading = true;
      this.state.currentPage++;

      const moreChats = await this.chatListManager.loadChats({
        page: this.state.currentPage,
        append: true
      });

      if (moreChats.length === 0) {
        this.state.hasMore = false;
      }

      this.state.isLoading = false;

    } catch (error) {
      console.error('❌ فشل في تحميل المزيد من المحادثات:', error);
      this.state.isLoading = false;
    }
  }

  /**
   * تهيئة التمرير الافتراضي
   * @private
   */
  _setupVirtualScrolling() {
    // تنفيذ التمرير الافتراضي لتحسين الأداء مع قوائم كبيرة
    // يمكن تطوير هذا لاحقاً حسب الحاجة
  }

  /**
   * إظهار/إخفاء مؤشر التحميل
   * @private
   */
  _showLoading(show) {
    if (this.elements.loadingIndicator) {
      this.elements.loadingIndicator.style.display = show ? 'block' : 'none';
    }
  }

  /**
   * إظهار/إخفاء الحالة الفارغة
   * @private
   */
  _showEmptyState(show) {
    if (this.elements.emptyState) {
      this.elements.emptyState.style.display = show ? 'block' : 'none';
    }
  }

  /**
   * إظهار/إخفاء حالة عدم وجود نتائج
   * @private
   */
  _showNoResults(show) {
    if (this.elements.noResultsState) {
      this.elements.noResultsState.style.display = show ? 'block' : 'none';
    }
  }

  /**
   * إظهار رسالة خطأ
   * @private
   */
  _showError(message) {
    // يمكن تطوير نظام إظهار الأخطاء هنا
    console.error('خطأ في مكون قائمة المحادثات:', message);
  }
}

// تصدير الكلاس
export default ChatListComponent;
