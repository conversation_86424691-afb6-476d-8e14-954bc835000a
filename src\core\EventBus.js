/**
 * نظام الأحداث المركزي - EventBus
 * يوفر نمط الناشر/المشترك للتواصل بين الوحدات المختلفة
 * يقلل من الاقتران بين المكونات ويسهل إدارة الأحداث
 */

class EventBus {
  constructor() {
    // خريطة الأحداث والمستمعين
    this.events = new Map();
    // معرف فريد لكل مستمع لسهولة الإلغاء
    this.listenerIdCounter = 0;
    // تسجيل الأحداث للتتبع والتشخيص
    this.eventLog = [];
    this.maxLogSize = 1000;
  }

  /**
   * الاشتراك في حدث معين
   * @param {string} eventName - اسم الحدث
   * @param {Function} callback - دالة المعالجة
   * @param {Object} options - خيارات إضافية
   * @returns {number} معرف المستمع لإمكانية الإلغاء
   */
  on(eventName, callback, options = {}) {
    try {
      // التحقق من صحة المعاملات
      if (!eventName || typeof eventName !== 'string') {
        throw new Error('اسم الحدث يجب أن يكون نص غير فارغ');
      }
      
      if (!callback || typeof callback !== 'function') {
        throw new Error('دالة المعالجة يجب أن تكون دالة صحيحة');
      }

      // إنشاء معرف فريد للمستمع
      const listenerId = ++this.listenerIdCounter;
      
      // إنشاء كائن المستمع
      const listener = {
        id: listenerId,
        callback,
        once: options.once || false,
        priority: options.priority || 0,
        context: options.context || null,
        createdAt: new Date()
      };

      // إضافة المستمع إلى قائمة الأحداث
      if (!this.events.has(eventName)) {
        this.events.set(eventName, []);
      }
      
      const listeners = this.events.get(eventName);
      listeners.push(listener);
      
      // ترتيب المستمعين حسب الأولوية (الأعلى أولاً)
      listeners.sort((a, b) => b.priority - a.priority);

      // تسجيل الحدث
      this._logEvent('LISTENER_ADDED', eventName, { listenerId, priority: listener.priority });

      return listenerId;
    } catch (error) {
      console.error('خطأ في إضافة مستمع الحدث:', error);
      throw error;
    }
  }

  /**
   * الاشتراك في حدث لمرة واحدة فقط
   * @param {string} eventName - اسم الحدث
   * @param {Function} callback - دالة المعالجة
   * @param {Object} options - خيارات إضافية
   * @returns {number} معرف المستمع
   */
  once(eventName, callback, options = {}) {
    return this.on(eventName, callback, { ...options, once: true });
  }

  /**
   * إلغاء الاشتراك في حدث
   * @param {string} eventName - اسم الحدث
   * @param {number} listenerId - معرف المستمع
   * @returns {boolean} نجح الإلغاء أم لا
   */
  off(eventName, listenerId) {
    try {
      if (!this.events.has(eventName)) {
        return false;
      }

      const listeners = this.events.get(eventName);
      const index = listeners.findIndex(listener => listener.id === listenerId);
      
      if (index === -1) {
        return false;
      }

      // إزالة المستمع
      listeners.splice(index, 1);
      
      // إزالة الحدث إذا لم يعد له مستمعين
      if (listeners.length === 0) {
        this.events.delete(eventName);
      }

      // تسجيل الحدث
      this._logEvent('LISTENER_REMOVED', eventName, { listenerId });

      return true;
    } catch (error) {
      console.error('خطأ في إزالة مستمع الحدث:', error);
      return false;
    }
  }

  /**
   * إطلاق حدث معين
   * @param {string} eventName - اسم الحدث
   * @param {*} data - البيانات المرسلة مع الحدث
   * @param {Object} options - خيارات إضافية
   * @returns {Promise<Array>} نتائج تنفيذ جميع المستمعين
   */
  async emit(eventName, data = null, options = {}) {
    try {
      // تسجيل إطلاق الحدث
      this._logEvent('EVENT_EMITTED', eventName, { data: typeof data });

      if (!this.events.has(eventName)) {
        return [];
      }

      const listeners = this.events.get(eventName);
      const results = [];
      const listenersToRemove = [];

      // تنفيذ جميع المستمعين
      for (const listener of listeners) {
        try {
          let result;
          
          // تنفيذ المستمع مع السياق المحدد
          if (listener.context) {
            result = await listener.callback.call(listener.context, data, eventName);
          } else {
            result = await listener.callback(data, eventName);
          }
          
          results.push({
            listenerId: listener.id,
            result,
            success: true
          });

          // إضافة المستمعين المؤقتين للإزالة
          if (listener.once) {
            listenersToRemove.push(listener.id);
          }

        } catch (error) {
          console.error(`خطأ في تنفيذ مستمع الحدث ${eventName}:`, error);
          results.push({
            listenerId: listener.id,
            error,
            success: false
          });
        }
      }

      // إزالة المستمعين المؤقتين
      listenersToRemove.forEach(listenerId => {
        this.off(eventName, listenerId);
      });

      return results;
    } catch (error) {
      console.error('خطأ في إطلاق الحدث:', error);
      throw error;
    }
  }

  /**
   * إطلاق حدث بشكل متزامن
   * @param {string} eventName - اسم الحدث
   * @param {*} data - البيانات المرسلة
   * @returns {Array} نتائج التنفيذ
   */
  emitSync(eventName, data = null) {
    try {
      this._logEvent('EVENT_EMITTED_SYNC', eventName, { data: typeof data });

      if (!this.events.has(eventName)) {
        return [];
      }

      const listeners = this.events.get(eventName);
      const results = [];
      const listenersToRemove = [];

      for (const listener of listeners) {
        try {
          let result;
          
          if (listener.context) {
            result = listener.callback.call(listener.context, data, eventName);
          } else {
            result = listener.callback(data, eventName);
          }
          
          results.push({
            listenerId: listener.id,
            result,
            success: true
          });

          if (listener.once) {
            listenersToRemove.push(listener.id);
          }

        } catch (error) {
          console.error(`خطأ في تنفيذ مستمع الحدث ${eventName}:`, error);
          results.push({
            listenerId: listener.id,
            error,
            success: false
          });
        }
      }

      // إزالة المستمعين المؤقتين
      listenersToRemove.forEach(listenerId => {
        this.off(eventName, listenerId);
      });

      return results;
    } catch (error) {
      console.error('خطأ في إطلاق الحدث المتزامن:', error);
      throw error;
    }
  }

  /**
   * الحصول على قائمة بجميع الأحداث المسجلة
   * @returns {Array} قائمة أسماء الأحداث
   */
  getEventNames() {
    return Array.from(this.events.keys());
  }

  /**
   * الحصول على عدد المستمعين لحدث معين
   * @param {string} eventName - اسم الحدث
   * @returns {number} عدد المستمعين
   */
  getListenerCount(eventName) {
    return this.events.has(eventName) ? this.events.get(eventName).length : 0;
  }

  /**
   * إزالة جميع المستمعين لحدث معين
   * @param {string} eventName - اسم الحدث
   * @returns {boolean} نجح الإزالة أم لا
   */
  removeAllListeners(eventName) {
    if (eventName) {
      const removed = this.events.has(eventName);
      this.events.delete(eventName);
      if (removed) {
        this._logEvent('ALL_LISTENERS_REMOVED', eventName);
      }
      return removed;
    } else {
      // إزالة جميع المستمعين لجميع الأحداث
      const eventCount = this.events.size;
      this.events.clear();
      this._logEvent('ALL_EVENTS_CLEARED', null, { eventCount });
      return eventCount > 0;
    }
  }

  /**
   * الحصول على سجل الأحداث
   * @param {number} limit - عدد الأحداث المطلوبة
   * @returns {Array} سجل الأحداث
   */
  getEventLog(limit = 100) {
    return this.eventLog.slice(-limit);
  }

  /**
   * مسح سجل الأحداث
   */
  clearEventLog() {
    this.eventLog = [];
    console.log('تم مسح سجل الأحداث');
  }

  /**
   * تسجيل حدث في السجل
   * @private
   * @param {string} type - نوع الحدث
   * @param {string} eventName - اسم الحدث
   * @param {Object} details - تفاصيل إضافية
   */
  _logEvent(type, eventName, details = {}) {
    const logEntry = {
      type,
      eventName,
      details,
      timestamp: new Date(),
      id: Date.now() + Math.random()
    };

    this.eventLog.push(logEntry);

    // الحفاظ على حجم السجل ضمن الحد المسموح
    if (this.eventLog.length > this.maxLogSize) {
      this.eventLog = this.eventLog.slice(-this.maxLogSize);
    }
  }

  /**
   * إنشاء مجموعة أحداث مترابطة
   * @param {Array} eventNames - أسماء الأحداث
   * @param {Function} callback - دالة المعالجة
   * @returns {Array} معرفات المستمعين
   */
  onGroup(eventNames, callback) {
    const listenerIds = [];
    
    eventNames.forEach(eventName => {
      const listenerId = this.on(eventName, callback);
      listenerIds.push({ eventName, listenerId });
    });

    return listenerIds;
  }

  /**
   * إلغاء مجموعة أحداث
   * @param {Array} listenerIds - معرفات المستمعين
   */
  offGroup(listenerIds) {
    listenerIds.forEach(({ eventName, listenerId }) => {
      this.off(eventName, listenerId);
    });
  }
}

// إنشاء مثيل عام للاستخدام في التطبيق
const eventBus = new EventBus();

// تصدير الكلاس والمثيل
export { EventBus, eventBus };
export default eventBus;
