/**
 * عنصر محادثة في القائمة - ChatListItem
 * يمثل محادثة واحدة في قائمة المحادثات
 * يعرض معلومات المحادثة والرسالة الأخيرة والحالة
 */

import BaseComponent from '../Common/BaseComponent.js';
import { DateUtils } from '../../utils/DateUtils.js';
import { DOMUtils } from '../../utils/DOMUtils.js';

/**
 * كلاس عنصر محادثة في القائمة
 */
class ChatListItem extends BaseComponent {
  constructor(options = {}) {
    super({
      className: 'chat-list-item',
      ...options
    });

    // بيانات المحادثة
    this.chat = options.chat || {};
    this.index = options.index || 0;
    
    // حالة العنصر
    this.state = {
      isSelected: options.isSelected || false,
      isOnline: false,
      isTyping: false,
      unreadCount: this.chat.unreadCount || 0,
      lastMessageStatus: null
    };

    // معالجات الأحداث
    this.onClick = options.onClick || (() => {});
    this.onContextMenu = options.onContextMenu || (() => {});

    // مراجع العناصر
    this.elements = {
      container: null,
      avatar: null,
      name: null,
      lastMessage: null,
      timestamp: null,
      unreadBadge: null,
      statusIndicator: null,
      typingIndicator: null,
      messageStatus: null
    };

    console.log(`💬 تم إنشاء عنصر محادثة: ${this.chat.name}`);
  }

  /**
   * الحصول على قالب HTML للعنصر
   * @returns {string} قالب HTML
   */
  getTemplate() {
    const chat = this.chat;
    const isGroup = chat.type !== 'Private';
    const avatarUrl = chat.avatarUrl || this._getDefaultAvatar();
    const lastMessageTime = this._formatLastMessageTime();
    const lastMessagePreview = this._getLastMessagePreview();

    return `
      <div class="chat-list-item ${this.state.isSelected ? 'selected' : ''}" 
           data-chat-id="${chat.id}"
           data-chat-type="${chat.type}"
           tabindex="0"
           role="button"
           aria-label="محادثة ${chat.name}">
        
        <!-- صورة المحادثة -->
        <div class="chat-avatar-container">
          <div class="chat-avatar" data-element="avatar">
            <img src="${avatarUrl}" alt="${chat.name}" loading="lazy">
            ${isGroup ? this._getGroupIndicator() : ''}
          </div>
          
          <!-- مؤشر الحالة (متصل/غير متصل) -->
          <div class="chat-status-indicator ${this.state.isOnline ? 'online' : 'offline'}" 
               data-element="status-indicator"
               title="${this.state.isOnline ? 'متصل' : 'غير متصل'}">
          </div>
        </div>

        <!-- معلومات المحادثة -->
        <div class="chat-info">
          <!-- الصف الأول: الاسم والوقت -->
          <div class="chat-header">
            <div class="chat-name" data-element="name" title="${chat.name}">
              ${chat.name}
              ${chat.isMuted ? '<i class="chat-muted-icon icon-bell-off" title="مكتومة"></i>' : ''}
              ${chat.isPinned ? '<i class="chat-pinned-icon icon-pin" title="مثبتة"></i>' : ''}
            </div>
            
            <div class="chat-timestamp" data-element="timestamp" title="${this._getFullTimestamp()}">
              ${lastMessageTime}
            </div>
          </div>

          <!-- الصف الثاني: آخر رسالة والعدادات -->
          <div class="chat-footer">
            <div class="chat-last-message" data-element="last-message">
              <!-- مؤشر الكتابة -->
              <div class="chat-typing-indicator" 
                   data-element="typing-indicator" 
                   style="display: ${this.state.isTyping ? 'flex' : 'none'}">
                <span class="typing-text">يكتب...</span>
                <div class="typing-dots">
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
              </div>

              <!-- آخر رسالة -->
              <div class="last-message-content" 
                   style="display: ${this.state.isTyping ? 'none' : 'block'}">
                ${this._getMessageStatusIcon()}
                <span class="message-text">${lastMessagePreview}</span>
              </div>
            </div>

            <!-- العدادات والمؤشرات -->
            <div class="chat-indicators">
              <!-- عداد الرسائل غير المقروءة -->
              <div class="chat-unread-badge" 
                   data-element="unread-badge"
                   style="display: ${this.state.unreadCount > 0 ? 'flex' : 'none'}">
                ${this.state.unreadCount > 99 ? '99+' : this.state.unreadCount}
              </div>

              <!-- مؤشرات إضافية -->
              ${chat.isFavorite ? '<i class="chat-favorite-icon icon-heart" title="مفضلة"></i>' : ''}
              ${chat.isArchived ? '<i class="chat-archived-icon icon-archive" title="مؤرشفة"></i>' : ''}
            </div>
          </div>
        </div>

        <!-- خط الفصل -->
        <div class="chat-separator"></div>
      </div>
    `;
  }

  /**
   * تهيئة العنصر بعد العرض
   */
  async onRender() {
    // الحصول على مراجع العناصر
    this._getElementReferences();

    // تهيئة الأحداث
    this._setupEventListeners();

    // تحديث الحالة الأولية
    this._updateOnlineStatus();
    this._updateUnreadCount();

    console.log(`✅ تم تهيئة عنصر محادثة: ${this.chat.name}`);
  }

  /**
   * تنظيف العنصر عند الإزالة
   */
  onDestroy() {
    // إزالة مستمعي الأحداث
    if (this.elements.container) {
      this.elements.container.removeEventListener('click', this._handleClick);
      this.elements.container.removeEventListener('contextmenu', this._handleContextMenu);
      this.elements.container.removeEventListener('keydown', this._handleKeyDown);
    }

    console.log(`🧹 تم تنظيف عنصر محادثة: ${this.chat.name}`);
  }

  // ==================== طرق عامة ====================

  /**
   * تحديد/إلغاء تحديد العنصر
   * @param {boolean} selected - هل العنصر محدد
   */
  setSelected(selected) {
    this.state.isSelected = selected;
    
    if (this.elements.container) {
      this.elements.container.classList.toggle('selected', selected);
      this.elements.container.setAttribute('aria-selected', selected);
    }

    // مسح عداد الرسائل غير المقروءة عند التحديد
    if (selected && this.state.unreadCount > 0) {
      this.updateUnreadCount(0);
    }
  }

  /**
   * تحديث عداد الرسائل غير المقروءة
   * @param {number} count - العدد الجديد
   */
  updateUnreadCount(count) {
    this.state.unreadCount = Math.max(0, count);
    this.chat.unreadCount = this.state.unreadCount;
    
    if (this.elements.unreadBadge) {
      const display = this.state.unreadCount > 0 ? 'flex' : 'none';
      const text = this.state.unreadCount > 99 ? '99+' : this.state.unreadCount;
      
      this.elements.unreadBadge.style.display = display;
      this.elements.unreadBadge.textContent = text;
    }

    // تحديث كلاس العنصر
    if (this.elements.container) {
      this.elements.container.classList.toggle('has-unread', this.state.unreadCount > 0);
    }
  }

  /**
   * تحديث حالة الاتصال
   * @param {boolean} isOnline - هل المستخدم متصل
   */
  updateOnlineStatus(isOnline) {
    this.state.isOnline = isOnline;
    
    if (this.elements.statusIndicator) {
      this.elements.statusIndicator.className = `chat-status-indicator ${isOnline ? 'online' : 'offline'}`;
      this.elements.statusIndicator.title = isOnline ? 'متصل' : 'غير متصل';
    }
  }

  /**
   * تحديث مؤشر الكتابة
   * @param {boolean} isTyping - هل المستخدم يكتب
   */
  updateTypingStatus(isTyping) {
    this.state.isTyping = isTyping;
    
    if (this.elements.typingIndicator && this.elements.lastMessage) {
      const typingDisplay = isTyping ? 'flex' : 'none';
      const messageDisplay = isTyping ? 'none' : 'block';
      
      this.elements.typingIndicator.style.display = typingDisplay;
      this.elements.lastMessage.querySelector('.last-message-content').style.display = messageDisplay;
    }
  }

  /**
   * تحديث آخر رسالة
   * @param {Object} message - بيانات الرسالة
   */
  updateLastMessage(message) {
    this.chat.lastMessage = message.messageText;
    this.chat.lastMessageDate = message.createdDate;
    this.chat.lastMessageType = message.messageType;
    this.chat.lastMessageDirection = message.direction;

    // تحديث النص
    if (this.elements.lastMessage) {
      const messageText = this.elements.lastMessage.querySelector('.message-text');
      if (messageText) {
        messageText.textContent = this._getLastMessagePreview();
      }
    }

    // تحديث الوقت
    if (this.elements.timestamp) {
      this.elements.timestamp.textContent = this._formatLastMessageTime();
      this.elements.timestamp.title = this._getFullTimestamp();
    }

    // تحديث مؤشر حالة الرسالة
    this._updateMessageStatusIcon();
  }

  /**
   * تحديث حالة الرسالة
   * @param {string} messageId - معرف الرسالة
   * @param {string} status - الحالة الجديدة
   */
  updateMessageStatus(messageId, status) {
    // تحديث حالة آخر رسالة إذا كانت تطابق
    if (this.chat.lastMessageId === messageId) {
      this.state.lastMessageStatus = status;
      this._updateMessageStatusIcon();
    }
  }

  /**
   * تمرير العنصر إلى منطقة الرؤية
   */
  scrollIntoView() {
    if (this.elements.container) {
      this.elements.container.scrollIntoView({
        behavior: 'smooth',
        block: 'nearest'
      });
    }
  }

  /**
   * تحديث بيانات المحادثة
   * @param {Object} updatedChat - البيانات المحدثة
   */
  updateChat(updatedChat) {
    this.chat = { ...this.chat, ...updatedChat };
    
    // إعادة عرض العنصر
    this.render();
  }

  // ==================== دوال خاصة ====================

  /**
   * الحصول على مراجع العناصر
   * @private
   */
  _getElementReferences() {
    const container = this.getElement();
    
    this.elements = {
      container,
      avatar: container.querySelector('[data-element="avatar"]'),
      name: container.querySelector('[data-element="name"]'),
      lastMessage: container.querySelector('[data-element="last-message"]'),
      timestamp: container.querySelector('[data-element="timestamp"]'),
      unreadBadge: container.querySelector('[data-element="unread-badge"]'),
      statusIndicator: container.querySelector('[data-element="status-indicator"]'),
      typingIndicator: container.querySelector('[data-element="typing-indicator"]')
    };
  }

  /**
   * تهيئة مستمعي الأحداث
   * @private
   */
  _setupEventListeners() {
    if (!this.elements.container) return;

    // النقر
    this._handleClick = (e) => {
      e.preventDefault();
      this.onClick(this.chat.id);
    };

    // القائمة السياقية
    this._handleContextMenu = (e) => {
      this.onContextMenu(this.chat.id, e);
    };

    // لوحة المفاتيح
    this._handleKeyDown = (e) => {
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault();
        this.onClick(this.chat.id);
      }
    };

    this.elements.container.addEventListener('click', this._handleClick);
    this.elements.container.addEventListener('contextmenu', this._handleContextMenu);
    this.elements.container.addEventListener('keydown', this._handleKeyDown);
  }

  /**
   * تحديث حالة الاتصال
   * @private
   */
  _updateOnlineStatus() {
    // تحديد حالة الاتصال بناءً على نوع المحادثة
    if (this.chat.type === 'Private') {
      // للمحادثات الخاصة، تحقق من حالة المستخدم
      this.state.isOnline = this.chat.isOnline || false;
    } else {
      // للمحادثات الجماعية، إخفاء مؤشر الحالة
      this.state.isOnline = false;
    }
    
    this.updateOnlineStatus(this.state.isOnline);
  }

  /**
   * تحديث عداد الرسائل غير المقروءة
   * @private
   */
  _updateUnreadCount() {
    this.updateUnreadCount(this.chat.unreadCount || 0);
  }

  /**
   * الحصول على صورة افتراضية
   * @private
   */
  _getDefaultAvatar() {
    const isGroup = this.chat.type !== 'Private';
    const initial = this.chat.name ? this.chat.name.charAt(0).toUpperCase() : '?';
    
    // يمكن إنشاء صورة ديناميكية أو استخدام صورة افتراضية
    return `data:image/svg+xml,${encodeURIComponent(`
      <svg width="40" height="40" xmlns="http://www.w3.org/2000/svg">
        <circle cx="20" cy="20" r="20" fill="${this._getAvatarColor()}"/>
        <text x="20" y="26" text-anchor="middle" fill="white" font-size="16" font-family="Arial">
          ${initial}
        </text>
      </svg>
    `)}`;
  }

  /**
   * الحصول على لون الصورة الافتراضية
   * @private
   */
  _getAvatarColor() {
    const colors = [
      '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', 
      '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F'
    ];
    
    const hash = this.chat.id ? this.chat.id.split('').reduce((a, b) => {
      a = ((a << 5) - a) + b.charCodeAt(0);
      return a & a;
    }, 0) : 0;
    
    return colors[Math.abs(hash) % colors.length];
  }

  /**
   * الحصول على مؤشر المجموعة
   * @private
   */
  _getGroupIndicator() {
    return '<div class="group-indicator" title="محادثة جماعية"><i class="icon-users"></i></div>';
  }

  /**
   * تنسيق وقت آخر رسالة
   * @private
   */
  _formatLastMessageTime() {
    if (!this.chat.lastMessageDate) return '';
    
    return DateUtils.formatRelativeTime(this.chat.lastMessageDate);
  }

  /**
   * الحصول على الوقت الكامل
   * @private
   */
  _getFullTimestamp() {
    if (!this.chat.lastMessageDate) return '';
    
    return DateUtils.formatFullDateTime(this.chat.lastMessageDate);
  }

  /**
   * الحصول على معاينة آخر رسالة
   * @private
   */
  _getLastMessagePreview() {
    if (!this.chat.lastMessage) {
      return 'لا توجد رسائل';
    }

    let preview = this.chat.lastMessage;
    
    // تخصيص المعاينة حسب نوع الرسالة
    switch (this.chat.lastMessageType) {
      case 'Image':
        preview = '📷 صورة';
        break;
      case 'File':
        preview = '📎 ملف';
        break;
      case 'Audio':
        preview = '🎵 رسالة صوتية';
        break;
      case 'Video':
        preview = '🎥 فيديو';
        break;
      case 'Location':
        preview = '📍 موقع';
        break;
      case 'Contact':
        preview = '👤 جهة اتصال';
        break;
      default:
        // تقصير النص الطويل
        if (preview.length > 50) {
          preview = preview.substring(0, 47) + '...';
        }
        break;
    }

    return preview;
  }

  /**
   * الحصول على أيقونة حالة الرسالة
   * @private
   */
  _getMessageStatusIcon() {
    if (this.chat.lastMessageDirection !== 'Outgoing') {
      return '';
    }

    const status = this.state.lastMessageStatus || this.chat.lastMessageStatus;
    
    switch (status) {
      case 'Sent':
        return '<i class="message-status-icon icon-check" title="تم الإرسال"></i>';
      case 'Delivered':
        return '<i class="message-status-icon icon-check-double" title="تم التسليم"></i>';
      case 'Read':
        return '<i class="message-status-icon icon-check-double read" title="تم القراءة"></i>';
      case 'Failed':
        return '<i class="message-status-icon icon-x-circle error" title="فشل الإرسال"></i>';
      default:
        return '<i class="message-status-icon icon-clock" title="جاري الإرسال"></i>';
    }
  }

  /**
   * تحديث أيقونة حالة الرسالة
   * @private
   */
  _updateMessageStatusIcon() {
    const messageContent = this.elements.lastMessage?.querySelector('.last-message-content');
    if (!messageContent) return;

    // إزالة الأيقونة القديمة
    const oldIcon = messageContent.querySelector('.message-status-icon');
    if (oldIcon) {
      oldIcon.remove();
    }

    // إضافة الأيقونة الجديدة
    const iconHtml = this._getMessageStatusIcon();
    if (iconHtml) {
      const messageText = messageContent.querySelector('.message-text');
      if (messageText) {
        messageText.insertAdjacentHTML('beforebegin', iconHtml);
      }
    }
  }
}

// تصدير الكلاس
export default ChatListItem;
