/**
 * أدوات التاريخ والوقت - DateUtils
 * يوفر دوال مساعدة للتعامل مع التواريخ والأوقات بطريقة محسنة
 * يدعم التنسيق العربي والحسابات المتقدمة
 */

/**
 * تنسيق التاريخ والوقت للعرض في قائمة المحادثات
 * @param {Date|string} date - التاريخ المراد تنسيقه
 * @returns {string} التاريخ المنسق
 */
export function formatChatListDate(date) {
  if (!date) return '';
  
  const messageDate = new Date(date);
  const now = new Date();
  const diffInMs = now - messageDate;
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));
  
  if (diffInDays === 0) {
    // اليوم - إظهار الوقت فقط
    return formatTime(messageDate);
  } else if (diffInDays === 1) {
    // أمس
    return 'أمس';
  } else if (diffInDays < 7) {
    // خلال الأسبوع - إظهار اسم اليوم
    return getDayName(messageDate);
  } else {
    // أكثر من أسبوع - إظهار التاريخ
    return formatShortDate(messageDate);
  }
}

/**
 * تنسيق الوقت (ساعة:دقيقة)
 * @param {Date|string} date - التاريخ
 * @returns {string} الوقت المنسق
 */
export function formatTime(date) {
  if (!date) return '';
  
  const dateObj = new Date(date);
  const hours = dateObj.getHours().toString().padStart(2, '0');
  const minutes = dateObj.getMinutes().toString().padStart(2, '0');
  
  return `${hours}:${minutes}`;
}

/**
 * تنسيق التاريخ المختصر (يوم/شهر/سنة)
 * @param {Date|string} date - التاريخ
 * @returns {string} التاريخ المنسق
 */
export function formatShortDate(date) {
  if (!date) return '';
  
  const dateObj = new Date(date);
  const day = dateObj.getDate().toString().padStart(2, '0');
  const month = (dateObj.getMonth() + 1).toString().padStart(2, '0');
  const year = dateObj.getFullYear();
  
  return `${day}/${month}/${year}`;
}

/**
 * تنسيق التاريخ الكامل مع الوقت
 * @param {Date|string} date - التاريخ
 * @returns {string} التاريخ والوقت المنسق
 */
export function formatFullDateTime(date) {
  if (!date) return '';
  
  const dateObj = new Date(date);
  const dateStr = formatShortDate(dateObj);
  const timeStr = formatTime(dateObj);
  
  return `${dateStr} ${timeStr}`;
}

/**
 * الحصول على اسم اليوم باللغة العربية
 * @param {Date|string} date - التاريخ
 * @returns {string} اسم اليوم
 */
export function getDayName(date) {
  if (!date) return '';
  
  const dateObj = new Date(date);
  const dayNames = [
    'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 
    'الخميس', 'الجمعة', 'السبت'
  ];
  
  return dayNames[dateObj.getDay()];
}

/**
 * الحصول على اسم الشهر باللغة العربية
 * @param {Date|string} date - التاريخ
 * @returns {string} اسم الشهر
 */
export function getMonthName(date) {
  if (!date) return '';
  
  const dateObj = new Date(date);
  const monthNames = [
    'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
    'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
  ];
  
  return monthNames[dateObj.getMonth()];
}

/**
 * حساب الوقت المنقضي منذ تاريخ معين
 * @param {Date|string} date - التاريخ
 * @returns {string} الوقت المنقضي بصيغة مقروءة
 */
export function getTimeAgo(date) {
  if (!date) return '';
  
  const messageDate = new Date(date);
  const now = new Date();
  const diffInMs = now - messageDate;
  
  const seconds = Math.floor(diffInMs / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);
  const weeks = Math.floor(days / 7);
  const months = Math.floor(days / 30);
  const years = Math.floor(days / 365);
  
  if (years > 0) {
    return years === 1 ? 'منذ سنة' : `منذ ${years} سنوات`;
  } else if (months > 0) {
    return months === 1 ? 'منذ شهر' : `منذ ${months} أشهر`;
  } else if (weeks > 0) {
    return weeks === 1 ? 'منذ أسبوع' : `منذ ${weeks} أسابيع`;
  } else if (days > 0) {
    return days === 1 ? 'منذ يوم' : `منذ ${days} أيام`;
  } else if (hours > 0) {
    return hours === 1 ? 'منذ ساعة' : `منذ ${hours} ساعات`;
  } else if (minutes > 0) {
    return minutes === 1 ? 'منذ دقيقة' : `منذ ${minutes} دقائق`;
  } else {
    return 'الآن';
  }
}

/**
 * تنسيق آخر ظهور للمستخدم
 * @param {Date|string} lastSeen - تاريخ آخر ظهور
 * @returns {string} نص آخر ظهور
 */
export function formatLastSeen(lastSeen) {
  if (!lastSeen) return 'غير متاح';
  
  const lastSeenDate = new Date(lastSeen);
  const now = new Date();
  const diffInMs = now - lastSeenDate;
  const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
  
  if (diffInMinutes < 1) {
    return 'متصل الآن';
  } else if (diffInMinutes < 60) {
    return `آخر ظهور منذ ${diffInMinutes} دقيقة`;
  } else {
    const timeAgo = getTimeAgo(lastSeenDate);
    return `آخر ظهور ${timeAgo}`;
  }
}

/**
 * التحقق من كون التاريخ اليوم
 * @param {Date|string} date - التاريخ
 * @returns {boolean} هل التاريخ اليوم
 */
export function isToday(date) {
  if (!date) return false;
  
  const dateObj = new Date(date);
  const today = new Date();
  
  return dateObj.toDateString() === today.toDateString();
}

/**
 * التحقق من كون التاريخ أمس
 * @param {Date|string} date - التاريخ
 * @returns {boolean} هل التاريخ أمس
 */
export function isYesterday(date) {
  if (!date) return false;
  
  const dateObj = new Date(date);
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  
  return dateObj.toDateString() === yesterday.toDateString();
}

/**
 * التحقق من كون التاريخ في نفس الأسبوع
 * @param {Date|string} date - التاريخ
 * @returns {boolean} هل التاريخ في نفس الأسبوع
 */
export function isThisWeek(date) {
  if (!date) return false;
  
  const dateObj = new Date(date);
  const now = new Date();
  const diffInMs = now - dateObj;
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));
  
  return diffInDays >= 0 && diffInDays < 7;
}

/**
 * تحويل التاريخ إلى طابع زمني
 * @param {Date|string} date - التاريخ
 * @returns {number} الطابع الزمني
 */
export function toTimestamp(date) {
  if (!date) return 0;
  
  return new Date(date).getTime();
}

/**
 * تحويل الطابع الزمني إلى تاريخ
 * @param {number} timestamp - الطابع الزمني
 * @returns {Date} التاريخ
 */
export function fromTimestamp(timestamp) {
  if (!timestamp) return null;
  
  return new Date(timestamp);
}

/**
 * مقارنة تاريخين
 * @param {Date|string} date1 - التاريخ الأول
 * @param {Date|string} date2 - التاريخ الثاني
 * @returns {number} -1 إذا كان الأول أقدم، 0 إذا كانا متساويين، 1 إذا كان الأول أحدث
 */
export function compareDates(date1, date2) {
  const d1 = new Date(date1);
  const d2 = new Date(date2);
  
  if (d1 < d2) return -1;
  if (d1 > d2) return 1;
  return 0;
}

/**
 * الحصول على بداية اليوم
 * @param {Date|string} date - التاريخ
 * @returns {Date} بداية اليوم
 */
export function getStartOfDay(date = new Date()) {
  const dateObj = new Date(date);
  dateObj.setHours(0, 0, 0, 0);
  return dateObj;
}

/**
 * الحصول على نهاية اليوم
 * @param {Date|string} date - التاريخ
 * @returns {Date} نهاية اليوم
 */
export function getEndOfDay(date = new Date()) {
  const dateObj = new Date(date);
  dateObj.setHours(23, 59, 59, 999);
  return dateObj;
}

/**
 * إضافة أيام إلى تاريخ
 * @param {Date|string} date - التاريخ
 * @param {number} days - عدد الأيام
 * @returns {Date} التاريخ الجديد
 */
export function addDays(date, days) {
  const dateObj = new Date(date);
  dateObj.setDate(dateObj.getDate() + days);
  return dateObj;
}

/**
 * إضافة ساعات إلى تاريخ
 * @param {Date|string} date - التاريخ
 * @param {number} hours - عدد الساعات
 * @returns {Date} التاريخ الجديد
 */
export function addHours(date, hours) {
  const dateObj = new Date(date);
  dateObj.setHours(dateObj.getHours() + hours);
  return dateObj;
}

/**
 * إضافة دقائق إلى تاريخ
 * @param {Date|string} date - التاريخ
 * @param {number} minutes - عدد الدقائق
 * @returns {Date} التاريخ الجديد
 */
export function addMinutes(date, minutes) {
  const dateObj = new Date(date);
  dateObj.setMinutes(dateObj.getMinutes() + minutes);
  return dateObj;
}

/**
 * تنسيق مدة زمنية بالثواني إلى نص مقروء
 * @param {number} seconds - المدة بالثواني
 * @returns {string} المدة المنسقة
 */
export function formatDuration(seconds) {
  if (!seconds || seconds < 0) return '00:00';
  
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  
  if (hours > 0) {
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  } else {
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  }
}

/**
 * تحويل نص التاريخ إلى كائن Date مع التحقق من الصحة
 * @param {string} dateString - نص التاريخ
 * @returns {Date|null} كائن التاريخ أو null إذا كان غير صحيح
 */
export function parseDate(dateString) {
  if (!dateString) return null;
  
  const date = new Date(dateString);
  
  // التحقق من صحة التاريخ
  if (isNaN(date.getTime())) {
    return null;
  }
  
  return date;
}

/**
 * تنسيق التاريخ حسب المنطقة الزمنية المحلية
 * @param {Date|string} date - التاريخ
 * @param {string} locale - المنطقة (افتراضي: ar-SA)
 * @param {Object} options - خيارات التنسيق
 * @returns {string} التاريخ المنسق
 */
export function formatLocalDate(date, locale = 'ar-SA', options = {}) {
  if (!date) return '';
  
  const dateObj = new Date(date);
  
  const defaultOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  };
  
  const formatOptions = { ...defaultOptions, ...options };
  
  try {
    return dateObj.toLocaleDateString(locale, formatOptions);
  } catch (error) {
    console.warn('فشل في تنسيق التاريخ:', error);
    return formatFullDateTime(date);
  }
}

// تصدير جميع الدوال
export default {
  formatChatListDate,
  formatTime,
  formatShortDate,
  formatFullDateTime,
  getDayName,
  getMonthName,
  getTimeAgo,
  formatLastSeen,
  isToday,
  isYesterday,
  isThisWeek,
  toTimestamp,
  fromTimestamp,
  compareDates,
  getStartOfDay,
  getEndOfDay,
  addDays,
  addHours,
  addMinutes,
  formatDuration,
  parseDate,
  formatLocalDate
};
