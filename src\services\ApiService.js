/**
 * خدمة API موحدة - ApiService
 * توفر واجهة موحدة لجميع طلبات API مع معالجة الأخطاء والتخزين المؤقت
 * تدعم المصادقة والتحقق من الاستجابات وإعادة المحاولة التلقائية
 */

import eventBus from '../core/EventBus.js';
import stateManager from '../core/StateManager.js';
import errorHandler, { ERROR_TYPES, ERROR_SEVERITY } from '../core/ErrorHandler.js';

// أنواع طلبات HTTP
export const HTTP_METHODS = {
  GET: 'GET',
  POST: 'POST',
  PUT: 'PUT',
  DELETE: 'DELETE',
  PATCH: 'PATCH'
};

// حالات الاستجابة
export const RESPONSE_STATUS = {
  SUCCESS: 'success',
  ERROR: 'error',
  TIMEOUT: 'timeout',
  NETWORK_ERROR: 'network_error',
  UNAUTHORIZED: 'unauthorized',
  FORBIDDEN: 'forbidden',
  NOT_FOUND: 'not_found',
  SERVER_ERROR: 'server_error'
};

class ApiService {
  constructor() {
    // إعدادات الخدمة
    this.config = {
      baseUrl: 'http://82.114.181.89:3000/chatproject/',
      timeout: 30000,
      retryAttempts: 3,
      retryDelay: 1000,
      enableCaching: true,
      cacheTimeout: 5 * 60 * 1000, // 5 دقائق
      enableRequestLogging: true,
      enableResponseLogging: true
    };

    // التخزين المؤقت للطلبات
    this.cache = new Map();
    this.cacheTimestamps = new Map();

    // قائمة انتظار الطلبات
    this.requestQueue = [];
    this.activeRequests = new Map();

    // إحصائيات الطلبات
    this.stats = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      cachedRequests: 0,
      averageResponseTime: 0,
      requestsByEndpoint: new Map()
    };

    // رؤوس HTTP الافتراضية
    this.defaultHeaders = {
      'Content-Type': 'application/json; charset=utf-8',
      'Accept': 'application/json'
    };

    // تهيئة الخدمة
    this._initialize();
  }

  /**
   * تنفيذ طلب GET
   * @param {string} endpoint - نقطة النهاية
   * @param {Object} options - خيارات الطلب
   * @returns {Promise<Object>} استجابة الطلب
   */
  async get(endpoint, options = {}) {
    return this._makeRequest(HTTP_METHODS.GET, endpoint, null, options);
  }

  /**
   * تنفيذ طلب POST
   * @param {string} endpoint - نقطة النهاية
   * @param {Object} data - البيانات المرسلة
   * @param {Object} options - خيارات الطلب
   * @returns {Promise<Object>} استجابة الطلب
   */
  async post(endpoint, data = null, options = {}) {
    return this._makeRequest(HTTP_METHODS.POST, endpoint, data, options);
  }

  /**
   * تنفيذ طلب PUT
   * @param {string} endpoint - نقطة النهاية
   * @param {Object} data - البيانات المرسلة
   * @param {Object} options - خيارات الطلب
   * @returns {Promise<Object>} استجابة الطلب
   */
  async put(endpoint, data = null, options = {}) {
    return this._makeRequest(HTTP_METHODS.PUT, endpoint, data, options);
  }

  /**
   * تنفيذ طلب DELETE
   * @param {string} endpoint - نقطة النهاية
   * @param {Object} options - خيارات الطلب
   * @returns {Promise<Object>} استجابة الطلب
   */
  async delete(endpoint, options = {}) {
    return this._makeRequest(HTTP_METHODS.DELETE, endpoint, null, options);
  }

  /**
   * تنفيذ طلب PATCH
   * @param {string} endpoint - نقطة النهاية
   * @param {Object} data - البيانات المرسلة
   * @param {Object} options - خيارات الطلب
   * @returns {Promise<Object>} استجابة الطلب
   */
  async patch(endpoint, data = null, options = {}) {
    return this._makeRequest(HTTP_METHODS.PATCH, endpoint, data, options);
  }

  /**
   * رفع ملف
   * @param {string} endpoint - نقطة النهاية
   * @param {FormData} formData - بيانات النموذج
   * @param {Object} options - خيارات الطلب
   * @returns {Promise<Object>} استجابة الطلب
   */
  async uploadFile(endpoint, formData, options = {}) {
    const uploadOptions = {
      ...options,
      headers: {
        ...options.headers,
        // إزالة Content-Type للسماح للمتصفح بتعيينه تلقائياً مع boundary
      },
      isFileUpload: true
    };

    // إزالة Content-Type من الرؤوس الافتراضية للرفع
    delete uploadOptions.headers['Content-Type'];

    return this._makeRequest(HTTP_METHODS.POST, endpoint, formData, uploadOptions);
  }

  /**
   * تحميل ملف
   * @param {string} url - رابط الملف
   * @param {Object} options - خيارات التحميل
   * @returns {Promise<Blob>} الملف المحمل
   */
  async downloadFile(url, options = {}) {
    try {
      const response = await this._makeRawRequest(url, {
        method: HTTP_METHODS.GET,
        ...options,
        responseType: 'blob'
      });

      if (!response.ok) {
        throw new Error(`فشل في تحميل الملف: ${response.status}`);
      }

      return await response.blob();
    } catch (error) {
      errorHandler.handleNetworkError(error, { url, operation: 'download' });
      throw error;
    }
  }

  /**
   * إلغاء طلب معين
   * @param {string} requestId - معرف الطلب
   */
  cancelRequest(requestId) {
    const activeRequest = this.activeRequests.get(requestId);
    
    if (activeRequest && activeRequest.controller) {
      activeRequest.controller.abort();
      this.activeRequests.delete(requestId);
      
      eventBus.emit('api:request:cancelled', { requestId });
    }
  }

  /**
   * إلغاء جميع الطلبات النشطة
   */
  cancelAllRequests() {
    this.activeRequests.forEach((request, requestId) => {
      if (request.controller) {
        request.controller.abort();
      }
    });
    
    this.activeRequests.clear();
    eventBus.emit('api:all:requests:cancelled');
  }

  /**
   * مسح التخزين المؤقت
   * @param {string} pattern - نمط المفاتيح للمسح (اختياري)
   */
  clearCache(pattern = null) {
    if (pattern) {
      // مسح المفاتيح التي تطابق النمط
      for (const key of this.cache.keys()) {
        if (key.includes(pattern)) {
          this.cache.delete(key);
          this.cacheTimestamps.delete(key);
        }
      }
    } else {
      // مسح جميع التخزين المؤقت
      this.cache.clear();
      this.cacheTimestamps.clear();
    }
    
    eventBus.emit('api:cache:cleared', { pattern });
  }

  /**
   * الحصول على إحصائيات الطلبات
   * @returns {Object} إحصائيات مفصلة
   */
  getStats() {
    return {
      ...this.stats,
      cacheSize: this.cache.size,
      activeRequestsCount: this.activeRequests.size,
      requestsByEndpoint: Object.fromEntries(this.stats.requestsByEndpoint)
    };
  }

  /**
   * تحديث إعدادات الخدمة
   * @param {Object} newConfig - الإعدادات الجديدة
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    eventBus.emit('api:config:updated', this.config);
  }

  /**
   * تنفيذ طلب HTTP
   * @private
   * @param {string} method - نوع الطلب
   * @param {string} endpoint - نقطة النهاية
   * @param {*} data - البيانات
   * @param {Object} options - خيارات الطلب
   * @returns {Promise<Object>} استجابة الطلب
   */
  async _makeRequest(method, endpoint, data = null, options = {}) {
    const requestId = this._generateRequestId();
    const startTime = performance.now();

    try {
      // تحديث الإحصائيات
      this.stats.totalRequests++;
      this._updateEndpointStats(endpoint);

      // إنشاء مفتاح التخزين المؤقت
      const cacheKey = this._generateCacheKey(method, endpoint, data);
      
      // التحقق من التخزين المؤقت للطلبات GET
      if (method === HTTP_METHODS.GET && this.config.enableCaching && !options.skipCache) {
        const cachedResponse = this._getCachedResponse(cacheKey);
        if (cachedResponse) {
          this.stats.cachedRequests++;
          return cachedResponse;
        }
      }

      // إعداد الطلب
      const requestConfig = this._buildRequestConfig(method, endpoint, data, options);
      
      // إضافة الطلب إلى القائمة النشطة
      const controller = new AbortController();
      this.activeRequests.set(requestId, {
        controller,
        endpoint,
        method,
        startTime
      });

      requestConfig.signal = controller.signal;

      // تسجيل الطلب
      if (this.config.enableRequestLogging) {
        this._logRequest(requestId, method, endpoint, data);
      }

      // إطلاق حدث بداية الطلب
      eventBus.emit('api:request:started', {
        requestId,
        method,
        endpoint,
        data
      });

      // تنفيذ الطلب
      const response = await this._executeRequest(requestConfig);
      
      // معالجة الاستجابة
      const result = await this._processResponse(response, requestConfig);
      
      // حفظ في التخزين المؤقت إذا كان مناسباً
      if (method === HTTP_METHODS.GET && this.config.enableCaching && result.status === RESPONSE_STATUS.SUCCESS) {
        this._setCachedResponse(cacheKey, result);
      }

      // حساب وقت الاستجابة
      const responseTime = performance.now() - startTime;
      this._updateResponseTimeStats(responseTime);

      // تسجيل الاستجابة
      if (this.config.enableResponseLogging) {
        this._logResponse(requestId, result, responseTime);
      }

      // إطلاق حدث إكمال الطلب
      eventBus.emit('api:request:completed', {
        requestId,
        method,
        endpoint,
        result,
        responseTime
      });

      // تحديث الإحصائيات
      this.stats.successfulRequests++;

      return result;

    } catch (error) {
      // معالجة الأخطاء
      const responseTime = performance.now() - startTime;
      
      this.stats.failedRequests++;
      
      const errorResult = this._handleRequestError(error, {
        requestId,
        method,
        endpoint,
        data,
        responseTime
      });

      // إطلاق حدث فشل الطلب
      eventBus.emit('api:request:failed', {
        requestId,
        method,
        endpoint,
        error: errorResult,
        responseTime
      });

      throw errorResult;
    } finally {
      // إزالة الطلب من القائمة النشطة
      this.activeRequests.delete(requestId);
    }
  }

  /**
   * تنفيذ طلب خام
   * @private
   */
  async _makeRawRequest(url, options = {}) {
    const controller = new AbortController();
    
    const requestOptions = {
      ...options,
      signal: controller.signal,
      headers: {
        ...this.defaultHeaders,
        ...this._getAuthHeaders(),
        ...options.headers
      }
    };

    return fetch(url, requestOptions);
  }

  /**
   * بناء إعدادات الطلب
   * @private
   */
  _buildRequestConfig(method, endpoint, data, options) {
    const url = this._buildUrl(endpoint, options.params);
    
    const config = {
      method,
      url,
      headers: {
        ...this.defaultHeaders,
        ...this._getAuthHeaders(),
        ...options.headers
      },
      timeout: options.timeout || this.config.timeout
    };

    // إضافة البيانات للطلبات التي تدعمها
    if (data && [HTTP_METHODS.POST, HTTP_METHODS.PUT, HTTP_METHODS.PATCH].includes(method)) {
      if (options.isFileUpload) {
        config.body = data;
      } else {
        config.body = JSON.stringify(data);
      }
    }

    return config;
  }

  /**
   * تنفيذ الطلب مع إعادة المحاولة
   * @private
   */
  async _executeRequest(config, attempt = 1) {
    try {
      const response = await fetch(config.url, {
        method: config.method,
        headers: config.headers,
        body: config.body,
        signal: config.signal
      });

      return response;
    } catch (error) {
      // إعادة المحاولة في حالة أخطاء الشبكة
      if (attempt < this.config.retryAttempts && this._shouldRetry(error)) {
        console.log(`🔄 إعادة محاولة الطلب (المحاولة ${attempt + 1})`);
        
        await new Promise(resolve => 
          setTimeout(resolve, this.config.retryDelay * attempt)
        );
        
        return this._executeRequest(config, attempt + 1);
      }
      
      throw error;
    }
  }

  /**
   * معالجة استجابة الطلب
   * @private
   */
  async _processResponse(response, config) {
    try {
      // التحقق من حالة الاستجابة
      if (!response.ok) {
        return this._handleHttpError(response);
      }

      // محاولة تحليل JSON
      let data;
      const contentType = response.headers.get('content-type');
      
      if (contentType && contentType.includes('application/json')) {
        data = await response.json();
      } else {
        data = await response.text();
      }

      return {
        status: RESPONSE_STATUS.SUCCESS,
        data,
        headers: Object.fromEntries(response.headers.entries()),
        statusCode: response.status,
        url: config.url
      };
    } catch (error) {
      throw new Error(`فشل في معالجة استجابة الطلب: ${error.message}`);
    }
  }

  /**
   * معالجة أخطاء HTTP
   * @private
   */
  async _handleHttpError(response) {
    let errorData;
    
    try {
      errorData = await response.json();
    } catch {
      errorData = { message: await response.text() };
    }

    const errorResult = {
      status: this._getErrorStatus(response.status),
      statusCode: response.status,
      message: errorData.message || `خطأ HTTP ${response.status}`,
      data: errorData,
      url: response.url
    };

    // معالجة أخطاء المصادقة
    if (response.status === 401) {
      eventBus.emit('api:authentication:failed');
      stateManager.setState('currentUser', null);
    }

    return errorResult;
  }

  /**
   * معالجة أخطاء الطلب
   * @private
   */
  _handleRequestError(error, context) {
    let errorType = ERROR_TYPES.NETWORK;
    let errorStatus = RESPONSE_STATUS.NETWORK_ERROR;

    if (error.name === 'AbortError') {
      errorStatus = RESPONSE_STATUS.TIMEOUT;
      errorType = ERROR_TYPES.NETWORK;
    } else if (error.message.includes('timeout')) {
      errorStatus = RESPONSE_STATUS.TIMEOUT;
      errorType = ERROR_TYPES.NETWORK;
    }

    const errorResult = {
      status: errorStatus,
      message: error.message,
      error,
      context
    };

    // تسجيل الخطأ
    errorHandler.handleError(error, {
      type: errorType,
      severity: ERROR_SEVERITY.HIGH,
      context: {
        ...context,
        apiRequest: true
      }
    });

    return errorResult;
  }

  /**
   * الحصول على رؤوس المصادقة
   * @private
   */
  _getAuthHeaders() {
    const currentUser = stateManager.getState('currentUser');
    const headers = {};

    if (currentUser?.tokenInfo?.token) {
      headers['Authorization'] = `Bearer ${currentUser.tokenInfo.token}`;
    }

    // إضافة رمز CSRF إذا كان متوفراً
    const csrfToken = document.querySelector("[name='AntiforgeryFieldname']")?.value;
    if (csrfToken) {
      headers['X-XSRF-TOKEN'] = csrfToken;
    }

    return headers;
  }

  /**
   * بناء URL الكامل
   * @private
   */
  _buildUrl(endpoint, params = null) {
    let url = endpoint.startsWith('http') ? endpoint : `${this.config.baseUrl}${endpoint}`;
    
    if (params) {
      const searchParams = new URLSearchParams(params);
      url += `?${searchParams.toString()}`;
    }
    
    return url;
  }

  /**
   * إنشاء مفتاح التخزين المؤقت
   * @private
   */
  _generateCacheKey(method, endpoint, data) {
    const dataString = data ? JSON.stringify(data) : '';
    return `${method}:${endpoint}:${btoa(dataString)}`;
  }

  /**
   * الحصول على استجابة مخزنة مؤقتاً
   * @private
   */
  _getCachedResponse(cacheKey) {
    if (!this.cache.has(cacheKey)) {
      return null;
    }

    const timestamp = this.cacheTimestamps.get(cacheKey);
    const now = Date.now();

    if (now - timestamp > this.config.cacheTimeout) {
      this.cache.delete(cacheKey);
      this.cacheTimestamps.delete(cacheKey);
      return null;
    }

    return this.cache.get(cacheKey);
  }

  /**
   * حفظ استجابة في التخزين المؤقت
   * @private
   */
  _setCachedResponse(cacheKey, response) {
    this.cache.set(cacheKey, response);
    this.cacheTimestamps.set(cacheKey, Date.now());
  }

  /**
   * تحديد ما إذا كان يجب إعادة المحاولة
   * @private
   */
  _shouldRetry(error) {
    // إعادة المحاولة في حالة أخطاء الشبكة فقط
    return error.name === 'TypeError' || 
           error.message.includes('network') ||
           error.message.includes('fetch');
  }

  /**
   * الحصول على حالة الخطأ من رمز HTTP
   * @private
   */
  _getErrorStatus(statusCode) {
    switch (statusCode) {
      case 401:
        return RESPONSE_STATUS.UNAUTHORIZED;
      case 403:
        return RESPONSE_STATUS.FORBIDDEN;
      case 404:
        return RESPONSE_STATUS.NOT_FOUND;
      case 408:
        return RESPONSE_STATUS.TIMEOUT;
      case 500:
      case 502:
      case 503:
      case 504:
        return RESPONSE_STATUS.SERVER_ERROR;
      default:
        return RESPONSE_STATUS.ERROR;
    }
  }

  /**
   * إنشاء معرف فريد للطلب
   * @private
   */
  _generateRequestId() {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * تحديث إحصائيات نقطة النهاية
   * @private
   */
  _updateEndpointStats(endpoint) {
    const current = this.stats.requestsByEndpoint.get(endpoint) || 0;
    this.stats.requestsByEndpoint.set(endpoint, current + 1);
  }

  /**
   * تحديث إحصائيات وقت الاستجابة
   * @private
   */
  _updateResponseTimeStats(responseTime) {
    const totalRequests = this.stats.successfulRequests + this.stats.failedRequests;
    const currentAverage = this.stats.averageResponseTime;
    
    this.stats.averageResponseTime = 
      (currentAverage * (totalRequests - 1) + responseTime) / totalRequests;
  }

  /**
   * تسجيل الطلب
   * @private
   */
  _logRequest(requestId, method, endpoint, data) {
    console.group(`📤 طلب API: ${method} ${endpoint}`);
    console.log('معرف الطلب:', requestId);
    console.log('البيانات:', data);
    console.groupEnd();
  }

  /**
   * تسجيل الاستجابة
   * @private
   */
  _logResponse(requestId, result, responseTime) {
    console.group(`📥 استجابة API: ${requestId}`);
    console.log('الحالة:', result.status);
    console.log('وقت الاستجابة:', `${responseTime.toFixed(2)} مللي ثانية`);
    console.log('البيانات:', result.data);
    console.groupEnd();
  }

  /**
   * تهيئة الخدمة
   * @private
   */
  _initialize() {
    // مراقبة تغييرات حالة الاتصال
    eventBus.on('state:connectionStatus.isOnline:changed', ({ value }) => {
      if (value) {
        console.log('🌐 تم استعادة الاتصال بالإنترنت');
        eventBus.emit('api:connection:restored');
      } else {
        console.log('🚫 فقدان الاتصال بالإنترنت');
        eventBus.emit('api:connection:lost');
      }
    });

    console.log('✅ تم تهيئة خدمة API');
  }
}

// إنشاء مثيل عام للاستخدام في التطبيق
const apiService = new ApiService();

// تصدير الكلاس والمثيل والثوابت
export { ApiService, apiService, HTTP_METHODS, RESPONSE_STATUS };
export default apiService;
