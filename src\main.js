/**
 * النقطة الرئيسية للتطبيق - Main Entry Point
 * يدير تهيئة وتشغيل التطبيق بالكامل مع معالجة الأخطاء والتحميل التدريجي
 * يضمن تهيئة جميع الخدمات والمكونات بالترتيب الصحيح
 */

// استيراد الوحدات الأساسية
import eventBus from './core/EventBus.js';
import stateManager from './core/StateManager.js';
import errorHandler from './core/ErrorHandler.js';
import appInitializer, { INIT_PHASES } from './core/AppInitializer.js';

// استيراد الخدمات
import apiService from './services/ApiService.js';
import databaseService from './services/DatabaseService.js';

// استيراد الإعدادات
import appConfig from './config/appConfig.js';

/**
 * كلاس التطبيق الرئيسي
 */
class ChatApplication {
  constructor() {
    // حالة التطبيق
    this.isInitialized = false;
    this.isRunning = false;
    this.startTime = null;
    
    // مراجع للخدمات الرئيسية
    this.services = {
      eventBus,
      stateManager,
      errorHandler,
      apiService,
      databaseService
    };

    // مراجع للمكونات (سيتم تحميلها لاحقاً)
    this.components = {};

    // معلومات الأداء
    this.performanceMetrics = {
      initializationTime: 0,
      memoryUsage: 0,
      activeConnections: 0
    };

    console.log('🚀 تم إنشاء مثيل التطبيق');
  }

  /**
   * تهيئة وتشغيل التطبيق
   * @returns {Promise<boolean>} نجح التشغيل أم لا
   */
  async start() {
    if (this.isRunning) {
      console.warn('⚠️ التطبيق يعمل بالفعل');
      return true;
    }

    try {
      console.log('🎯 بدء تشغيل التطبيق...');
      this.startTime = performance.now();

      // تحديث حالة التطبيق
      stateManager.setState('ui.isLoading', true);
      stateManager.setState('ui.loadingMessage', 'جاري تهيئة التطبيق...');

      // تسجيل الخدمات والمكونات للتهيئة
      this._registerServicesAndComponents();

      // تهيئة التطبيق
      const initSuccess = await appInitializer.initialize();
      
      if (!initSuccess) {
        throw new Error('فشل في تهيئة التطبيق');
      }

      // إعداد مستمعي الأحداث الرئيسية
      this._setupEventListeners();

      // تحديث حالة التطبيق
      this.isInitialized = true;
      this.isRunning = true;

      // حساب وقت التهيئة
      this.performanceMetrics.initializationTime = performance.now() - this.startTime;

      // تحديث الحالة النهائية
      stateManager.setState('ui.isLoading', false);
      stateManager.setState('ui.loadingMessage', '');

      // إطلاق حدث تشغيل التطبيق
      eventBus.emit('app:started', {
        initializationTime: this.performanceMetrics.initializationTime,
        config: appConfig
      });

      console.log(`✅ تم تشغيل التطبيق بنجاح في ${this.performanceMetrics.initializationTime.toFixed(2)} مللي ثانية`);

      // بدء مراقبة الأداء
      this._startPerformanceMonitoring();

      return true;
    } catch (error) {
      console.error('❌ فشل في تشغيل التطبيق:', error);
      
      // معالجة خطأ التشغيل
      errorHandler.handleError(error, {
        type: 'UNKNOWN',
        severity: 'CRITICAL',
        context: 'app_startup'
      });

      // تحديث حالة التطبيق
      stateManager.setState('ui.isLoading', false);
      stateManager.setState('ui.loadingMessage', 'فشل في تشغيل التطبيق');

      // إطلاق حدث فشل التشغيل
      eventBus.emit('app:startup:failed', { error });

      return false;
    }
  }

  /**
   * إيقاف التطبيق
   * @returns {Promise<boolean>} نجح الإيقاف أم لا
   */
  async stop() {
    if (!this.isRunning) {
      console.warn('⚠️ التطبيق غير قيد التشغيل');
      return true;
    }

    try {
      console.log('🛑 إيقاف التطبيق...');

      // إطلاق حدث إيقاف التطبيق
      eventBus.emit('app:stopping');

      // إيقاف الخدمات
      await this._stopServices();

      // تنظيف الموارد
      this._cleanup();

      // تحديث حالة التطبيق
      this.isRunning = false;
      this.isInitialized = false;

      // إطلاق حدث إيقاف التطبيق
      eventBus.emit('app:stopped');

      console.log('✅ تم إيقاف التطبيق بنجاح');

      return true;
    } catch (error) {
      console.error('❌ فشل في إيقاف التطبيق:', error);
      
      errorHandler.handleError(error, {
        type: 'UNKNOWN',
        severity: 'HIGH',
        context: 'app_shutdown'
      });

      return false;
    }
  }

  /**
   * إعادة تشغيل التطبيق
   * @returns {Promise<boolean>} نجح إعادة التشغيل أم لا
   */
  async restart() {
    console.log('🔄 إعادة تشغيل التطبيق...');
    
    const stopSuccess = await this.stop();
    if (!stopSuccess) {
      return false;
    }

    // انتظار قصير قبل إعادة التشغيل
    await new Promise(resolve => setTimeout(resolve, 1000));

    return await this.start();
  }

  /**
   * الحصول على حالة التطبيق
   * @returns {Object} معلومات حالة التطبيق
   */
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      isRunning: this.isRunning,
      startTime: this.startTime,
      uptime: this.startTime ? performance.now() - this.startTime : 0,
      performanceMetrics: this.performanceMetrics,
      initializationState: appInitializer.getInitializationState(),
      servicesStatus: this._getServicesStatus(),
      componentsStatus: this._getComponentsStatus()
    };
  }

  /**
   * تسجيل الخدمات والمكونات للتهيئة
   * @private
   */
  _registerServicesAndComponents() {
    console.log('📋 تسجيل الخدمات والمكونات...');

    // تسجيل الخدمات الأساسية
    appInitializer.registerService('database', async () => {
      return await databaseService.initialize();
    }, {
      phase: INIT_PHASES.DATABASE,
      priority: 100,
      critical: true
    });

    appInitializer.registerService('api', async () => {
      // تحديث إعدادات API
      apiService.updateConfig(appConfig.api);
      return true;
    }, {
      phase: INIT_PHASES.CORE_SERVICES,
      priority: 90,
      critical: true,
      dependencies: ['database']
    });

    // تسجيل خدمة المصادقة
    appInitializer.registerService('authentication', async () => {
      return await this._initializeAuthentication();
    }, {
      phase: INIT_PHASES.AUTHENTICATION,
      priority: 80,
      critical: true,
      dependencies: ['api', 'database']
    });

    // تسجيل خدمة SignalR
    appInitializer.registerService('signalr', async () => {
      return await this._initializeSignalR();
    }, {
      phase: INIT_PHASES.REAL_TIME,
      priority: 70,
      critical: false,
      dependencies: ['authentication']
    });

    // تسجيل مكونات واجهة المستخدم
    appInitializer.registerComponent('chatList', async () => {
      return await this._initializeChatListComponent();
    }, {
      phase: INIT_PHASES.UI_COMPONENTS,
      priority: 60,
      critical: true,
      dependencies: ['database', 'api']
    });

    appInitializer.registerComponent('messageArea', async () => {
      return await this._initializeMessageAreaComponent();
    }, {
      phase: INIT_PHASES.UI_COMPONENTS,
      priority: 50,
      critical: true,
      dependencies: ['chatList']
    });

    appInitializer.registerComponent('userProfile', async () => {
      return await this._initializeUserProfileComponent();
    }, {
      phase: INIT_PHASES.UI_COMPONENTS,
      priority: 40,
      critical: false,
      dependencies: ['authentication']
    });

    console.log('✅ تم تسجيل جميع الخدمات والمكونات');
  }

  /**
   * إعداد مستمعي الأحداث الرئيسية
   * @private
   */
  _setupEventListeners() {
    console.log('🎧 إعداد مستمعي الأحداث...');

    // مراقبة أخطاء التطبيق
    eventBus.on('error:occurred', (errorData) => {
      console.error('خطأ في التطبيق:', errorData);
      
      // إذا كان خطأ حرج، قد نحتاج لإعادة تشغيل التطبيق
      if (errorData.severity === 'CRITICAL') {
        this._handleCriticalError(errorData);
      }
    });

    // مراقبة تغييرات الاتصال
    eventBus.on('api:connection:lost', () => {
      console.warn('🚫 فقدان الاتصال بالخادم');
      stateManager.setState('connectionStatus.isOnline', false);
    });

    eventBus.on('api:connection:restored', () => {
      console.log('🌐 تم استعادة الاتصال بالخادم');
      stateManager.setState('connectionStatus.isOnline', true);
    });

    // مراقبة أحداث قاعدة البيانات
    eventBus.on('database:initialized', () => {
      console.log('🗄️ تم تهيئة قاعدة البيانات');
    });

    // مراقبة أحداث المصادقة
    eventBus.on('api:authentication:failed', () => {
      console.warn('🔐 فشل في المصادقة');
      this._handleAuthenticationFailure();
    });

    // مراقبة تغييرات حالة التطبيق
    stateManager.subscribe('currentUser', (user) => {
      if (user) {
        console.log('👤 تم تسجيل دخول المستخدم:', user.userName);
      } else {
        console.log('👤 تم تسجيل خروج المستخدم');
      }
    });

    console.log('✅ تم إعداد مستمعي الأحداث');
  }

  /**
   * تهيئة خدمة المصادقة
   * @private
   */
  async _initializeAuthentication() {
    try {
      console.log('🔐 تهيئة خدمة المصادقة...');

      // محاولة الحصول على معلومات المستخدم الحالي
      const response = await apiService.get(appConfig.endpoints.AUTH.PROFILE);
      
      if (response.status === 'success' && response.data) {
        // حفظ معلومات المستخدم في الحالة وقاعدة البيانات
        await stateManager.setState('currentUser', response.data);
        await databaseService.save('currentUser', response.data);
        
        console.log('✅ تم تحميل معلومات المستخدم');
        return true;
      } else {
        console.log('ℹ️ لم يتم العثور على مستخدم مسجل');
        return true; // ليس خطأ، فقط لا يوجد مستخدم مسجل
      }
    } catch (error) {
      console.error('❌ فشل في تهيئة المصادقة:', error);
      
      // محاولة تحميل المستخدم من قاعدة البيانات المحلية
      try {
        const localUser = await databaseService.getById('currentUser', 'current');
        if (localUser) {
          await stateManager.setState('currentUser', localUser);
          console.log('✅ تم تحميل المستخدم من قاعدة البيانات المحلية');
          return true;
        }
      } catch (localError) {
        console.error('فشل في تحميل المستخدم من قاعدة البيانات المحلية:', localError);
      }
      
      return false;
    }
  }

  /**
   * تهيئة خدمة SignalR
   * @private
   */
  async _initializeSignalR() {
    try {
      console.log('📡 تهيئة خدمة SignalR...');

      const currentUser = stateManager.getState('currentUser');
      
      if (!currentUser?.tokenInfo?.token) {
        console.log('ℹ️ لا يوجد رمز مصادقة، تخطي تهيئة SignalR');
        return true;
      }

      // تحميل وتهيئة SignalR (سيتم تنفيذه لاحقاً)
      // const signalRService = await import('./services/SignalRService.js');
      // await signalRService.default.initialize(appConfig.signalr.url, currentUser.tokenInfo.token);

      console.log('✅ تم تهيئة خدمة SignalR');
      return true;
    } catch (error) {
      console.error('❌ فشل في تهيئة SignalR:', error);
      return false; // SignalR ليس حرج للتطبيق
    }
  }

  /**
   * تهيئة مكون قائمة المحادثات
   * @private
   */
  async _initializeChatListComponent() {
    try {
      console.log('💬 تهيئة مكون قائمة المحادثات...');

      // تحميل وتهيئة مكون قائمة المحادثات (سيتم تنفيذه لاحقاً)
      // const ChatListComponent = await import('./components/ChatList/ChatListComponent.js');
      // this.components.chatList = new ChatListComponent.default();
      // await this.components.chatList.initialize();

      console.log('✅ تم تهيئة مكون قائمة المحادثات');
      return true;
    } catch (error) {
      console.error('❌ فشل في تهيئة مكون قائمة المحادثات:', error);
      return false;
    }
  }

  /**
   * تهيئة مكون منطقة الرسائل
   * @private
   */
  async _initializeMessageAreaComponent() {
    try {
      console.log('📝 تهيئة مكون منطقة الرسائل...');

      // تحميل وتهيئة مكون منطقة الرسائل (سيتم تنفيذه لاحقاً)
      // const MessageAreaComponent = await import('./components/MessageArea/MessageAreaComponent.js');
      // this.components.messageArea = new MessageAreaComponent.default();
      // await this.components.messageArea.initialize();

      console.log('✅ تم تهيئة مكون منطقة الرسائل');
      return true;
    } catch (error) {
      console.error('❌ فشل في تهيئة مكون منطقة الرسائل:', error);
      return false;
    }
  }

  /**
   * تهيئة مكون ملف المستخدم
   * @private
   */
  async _initializeUserProfileComponent() {
    try {
      console.log('👤 تهيئة مكون ملف المستخدم...');

      // تحميل وتهيئة مكون ملف المستخدم (سيتم تنفيذه لاحقاً)
      // const UserProfileComponent = await import('./components/UserProfile/UserProfileComponent.js');
      // this.components.userProfile = new UserProfileComponent.default();
      // await this.components.userProfile.initialize();

      console.log('✅ تم تهيئة مكون ملف المستخدم');
      return true;
    } catch (error) {
      console.error('❌ فشل في تهيئة مكون ملف المستخدم:', error);
      return false;
    }
  }

  /**
   * معالجة الأخطاء الحرجة
   * @private
   */
  async _handleCriticalError(errorData) {
    console.error('🚨 خطأ حرج في التطبيق:', errorData);

    // إظهار رسالة للمستخدم
    stateManager.setState('ui.criticalError', {
      message: 'حدث خطأ حرج في التطبيق. سيتم إعادة التشغيل تلقائياً.',
      timestamp: new Date(),
      errorId: errorData.id
    });

    // انتظار قصير ثم إعادة التشغيل
    setTimeout(async () => {
      await this.restart();
    }, 3000);
  }

  /**
   * معالجة فشل المصادقة
   * @private
   */
  _handleAuthenticationFailure() {
    console.warn('🔐 فشل في المصادقة، مسح بيانات المستخدم');

    // مسح بيانات المستخدم
    stateManager.setState('currentUser', null);
    
    // إظهار شاشة تسجيل الدخول
    stateManager.setState('ui.activeView', 'login');
    
    // إطلاق حدث فشل المصادقة
    eventBus.emit('authentication:failed');
  }

  /**
   * إيقاف الخدمات
   * @private
   */
  async _stopServices() {
    console.log('🛑 إيقاف الخدمات...');

    // إيقاف SignalR
    if (this.services.signalRService) {
      await this.services.signalRService.disconnect();
    }

    // إلغاء جميع طلبات API النشطة
    apiService.cancelAllRequests();

    // إغلاق قاعدة البيانات
    if (databaseService.db) {
      databaseService.db.close();
    }

    console.log('✅ تم إيقاف جميع الخدمات');
  }

  /**
   * تنظيف الموارد
   * @private
   */
  _cleanup() {
    console.log('🧹 تنظيف الموارد...');

    // مسح التخزين المؤقت
    apiService.clearCache();
    
    // إزالة مستمعي الأحداث
    eventBus.removeAllListeners();
    
    // مسح المراجع
    this.components = {};
    
    console.log('✅ تم تنظيف الموارد');
  }

  /**
   * بدء مراقبة الأداء
   * @private
   */
  _startPerformanceMonitoring() {
    if (!appConfig.debug.enablePerformanceMonitoring) {
      return;
    }

    console.log('📊 بدء مراقبة الأداء...');

    // مراقبة استخدام الذاكرة
    setInterval(() => {
      if (performance.memory) {
        this.performanceMetrics.memoryUsage = performance.memory.usedJSHeapSize;
      }
    }, 30000); // كل 30 ثانية

    // مراقبة الاتصالات النشطة
    eventBus.on('api:request:started', () => {
      this.performanceMetrics.activeConnections++;
    });

    eventBus.on('api:request:completed', () => {
      this.performanceMetrics.activeConnections--;
    });

    eventBus.on('api:request:failed', () => {
      this.performanceMetrics.activeConnections--;
    });
  }

  /**
   * الحصول على حالة الخدمات
   * @private
   */
  _getServicesStatus() {
    return {
      eventBus: !!eventBus,
      stateManager: !!stateManager,
      errorHandler: !!errorHandler,
      apiService: !!apiService,
      databaseService: databaseService.isInitialized
    };
  }

  /**
   * الحصول على حالة المكونات
   * @private
   */
  _getComponentsStatus() {
    const status = {};
    
    Object.keys(this.components).forEach(componentName => {
      status[componentName] = !!this.components[componentName];
    });
    
    return status;
  }
}

// إنشاء مثيل التطبيق الرئيسي
const chatApp = new ChatApplication();

// تشغيل التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', async () => {
  console.log('📄 تم تحميل الصفحة، بدء تشغيل التطبيق...');
  
  try {
    const success = await chatApp.start();
    
    if (!success) {
      console.error('❌ فشل في تشغيل التطبيق');
      
      // إظهار رسالة خطأ للمستخدم
      document.body.innerHTML = `
        <div style="
          display: flex;
          justify-content: center;
          align-items: center;
          height: 100vh;
          font-family: Arial, sans-serif;
          background-color: #f5f5f5;
        ">
          <div style="
            text-align: center;
            padding: 2rem;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
          ">
            <h2 style="color: #e74c3c; margin-bottom: 1rem;">خطأ في تشغيل التطبيق</h2>
            <p style="color: #666; margin-bottom: 1.5rem;">
              عذراً، حدث خطأ أثناء تشغيل التطبيق. يرجى تحديث الصفحة والمحاولة مرة أخرى.
            </p>
            <button 
              onclick="window.location.reload()" 
              style="
                background: #3498db;
                color: white;
                border: none;
                padding: 0.75rem 1.5rem;
                border-radius: 4px;
                cursor: pointer;
                font-size: 1rem;
              "
            >
              تحديث الصفحة
            </button>
          </div>
        </div>
      `;
    }
  } catch (error) {
    console.error('❌ خطأ غير متوقع في تشغيل التطبيق:', error);
  }
});

// معالجة إغلاق النافذة
window.addEventListener('beforeunload', async () => {
  console.log('🚪 إغلاق النافذة، إيقاف التطبيق...');
  await chatApp.stop();
});

// تصدير مثيل التطبيق للاستخدام العام
window.chatApp = chatApp;

// تصدير للوحدات الأخرى
export default chatApp;
