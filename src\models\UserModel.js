/**
 * نموذج المستخدم - UserModel
 * يمثل بنية بيانات المستخدم مع دوال التحقق والتحويل
 * يدعم إدارة الملف الشخصي والحالة والإعدادات
 */

import { formatLastSeen, parseDate } from '../utils/DateUtils.js';
import { sanitizeInput, isValidLength, isValidEmail, isValidPhone } from '../utils/ValidationUtils.js';

// حالات المستخدم
export const USER_STATUS = {
  ONLINE: 'Online',
  OFFLINE: 'Offline',
  AWAY: 'Away',
  BUSY: 'Busy',
  INVISIBLE: 'Invisible'
};

// أنواع المستخدمين
export const USER_TYPES = {
  REGULAR: 'Regular',
  ADMIN: 'Admin',
  MODERATOR: 'Moderator',
  BOT: 'Bot',
  SYSTEM: 'System'
};

// مستويات الخصوصية
export const PRIVACY_LEVELS = {
  PUBLIC: 'Public',
  CONTACTS: 'Contacts',
  PRIVATE: 'Private'
};

/**
 * كلاس نموذج المستخدم
 */
export class UserModel {
  constructor(data = {}) {
    // الخصائص الأساسية
    this.id = data.id || null;
    this.userName = data.userName || '';
    this.email = data.email || '';
    this.phoneNumber = data.phoneNumber || '';
    this.firstName = data.firstName || '';
    this.lastName = data.lastName || '';
    this.displayName = data.displayName || '';
    
    // الصورة والملف الشخصي
    this.image = data.image || '';
    this.coverImage = data.coverImage || '';
    this.avatar = data.avatar || '';
    this.bio = data.bio || '';
    this.website = data.website || '';
    
    // معلومات الحالة
    this.status = data.status || USER_STATUS.OFFLINE;
    this.statusMessage = data.statusMessage || '';
    this.isOnline = data.isOnline || false;
    this.lastSeen = data.lastSeen ? parseDate(data.lastSeen) : null;
    this.lastActivity = data.lastActivity ? parseDate(data.lastActivity) : null;
    
    // معلومات النوع والأدوار
    this.userType = data.userType || USER_TYPES.REGULAR;
    this.roles = data.roles || [];
    this.permissions = data.permissions || [];
    this.isVerified = data.isVerified || false;
    this.isPremium = data.isPremium || false;
    
    // معلومات الموقع
    this.location = data.location ? {
      country: data.location.country || '',
      city: data.location.city || '',
      timezone: data.location.timezone || '',
      coordinates: data.location.coordinates || null
    } : null;
    
    // معلومات الاتصال
    this.socialLinks = data.socialLinks || {};
    this.languages = data.languages || ['ar'];
    this.preferredLanguage = data.preferredLanguage || 'ar';
    
    // إعدادات الخصوصية
    this.privacy = {
      profileVisibility: data.privacy?.profileVisibility || PRIVACY_LEVELS.PUBLIC,
      lastSeenVisibility: data.privacy?.lastSeenVisibility || PRIVACY_LEVELS.CONTACTS,
      phoneVisibility: data.privacy?.phoneVisibility || PRIVACY_LEVELS.CONTACTS,
      emailVisibility: data.privacy?.emailVisibility || PRIVACY_LEVELS.PRIVATE,
      allowDirectMessages: data.privacy?.allowDirectMessages !== false,
      allowGroupInvites: data.privacy?.allowGroupInvites !== false,
      allowCalls: data.privacy?.allowCalls !== false,
      ...data.privacy
    };
    
    // إعدادات الإشعارات
    this.notifications = {
      enabled: data.notifications?.enabled !== false,
      sound: data.notifications?.sound !== false,
      vibration: data.notifications?.vibration !== false,
      showPreview: data.notifications?.showPreview !== false,
      messageNotifications: data.notifications?.messageNotifications !== false,
      groupNotifications: data.notifications?.groupNotifications !== false,
      callNotifications: data.notifications?.callNotifications !== false,
      emailNotifications: data.notifications?.emailNotifications || false,
      ...data.notifications
    };
    
    // إعدادات التطبيق
    this.settings = {
      theme: data.settings?.theme || 'light',
      fontSize: data.settings?.fontSize || 'medium',
      autoDownload: data.settings?.autoDownload !== false,
      saveToGallery: data.settings?.saveToGallery || false,
      enterToSend: data.settings?.enterToSend !== false,
      readReceipts: data.settings?.readReceipts !== false,
      typingIndicator: data.settings?.typingIndicator !== false,
      ...data.settings
    };
    
    // معلومات الأمان
    this.security = {
      twoFactorEnabled: data.security?.twoFactorEnabled || false,
      lastPasswordChange: data.security?.lastPasswordChange ? parseDate(data.security.lastPasswordChange) : null,
      loginAttempts: data.security?.loginAttempts || 0,
      isLocked: data.security?.isLocked || false,
      lockUntil: data.security?.lockUntil ? parseDate(data.security.lockUntil) : null,
      ...data.security
    };
    
    // معلومات الجلسة
    this.session = {
      deviceId: data.session?.deviceId || '',
      deviceName: data.session?.deviceName || '',
      deviceType: data.session?.deviceType || '',
      ipAddress: data.session?.ipAddress || '',
      userAgent: data.session?.userAgent || '',
      loginDate: data.session?.loginDate ? parseDate(data.session.loginDate) : null,
      ...data.session
    };
    
    // معلومات المصادقة
    this.tokenInfo = data.tokenInfo ? {
      token: data.tokenInfo.token || '',
      refreshToken: data.tokenInfo.refreshToken || '',
      expiresAt: data.tokenInfo.expiresAt ? parseDate(data.tokenInfo.expiresAt) : null,
      tokenType: data.tokenInfo.tokenType || 'Bearer',
      ...data.tokenInfo
    } : null;
    
    // الإحصائيات
    this.stats = {
      totalMessages: data.stats?.totalMessages || 0,
      totalChats: data.stats?.totalChats || 0,
      totalContacts: data.stats?.totalContacts || 0,
      joinDate: data.stats?.joinDate ? parseDate(data.stats.joinDate) : new Date(),
      lastLoginDate: data.stats?.lastLoginDate ? parseDate(data.stats.lastLoginDate) : null,
      ...data.stats
    };
    
    // التوقيتات
    this.createdDate = data.createdDate ? parseDate(data.createdDate) : new Date();
    this.updatedDate = data.updatedDate ? parseDate(data.updatedDate) : new Date();
    this.deletedDate = data.deletedDate ? parseDate(data.deletedDate) : null;
    
    // معلومات إضافية
    this.metadata = data.metadata || {};
    this.tags = data.tags || [];
    this.isActive = data.isActive !== false;
    this.isDeleted = data.isDeleted || false;
    this.isBlocked = data.isBlocked || false;
    
    // معلومات المزامنة
    this._version = data._version || 1;
    this._lastSync = data._lastSync ? parseDate(data._lastSync) : null;
    this._isLocal = data._isLocal || false;
  }

  /**
   * التحقق من صحة بيانات المستخدم
   * @returns {Object} نتيجة التحقق
   */
  validate() {
    const errors = [];
    
    // التحقق من اسم المستخدم
    if (!isValidLength(this.userName, 3, 50)) {
      errors.push('اسم المستخدم يجب أن يكون بين 3 و 50 حرف');
    }
    
    // التحقق من البريد الإلكتروني
    if (this.email && !isValidEmail(this.email)) {
      errors.push('البريد الإلكتروني غير صحيح');
    }
    
    // التحقق من رقم الهاتف
    if (this.phoneNumber && !isValidPhone(this.phoneNumber)) {
      errors.push('رقم الهاتف غير صحيح');
    }
    
    // التحقق من الاسم الأول
    if (this.firstName && !isValidLength(this.firstName, 1, 50)) {
      errors.push('الاسم الأول يجب أن يكون بين 1 و 50 حرف');
    }
    
    // التحقق من الاسم الأخير
    if (this.lastName && !isValidLength(this.lastName, 1, 50)) {
      errors.push('الاسم الأخير يجب أن يكون بين 1 و 50 حرف');
    }
    
    // التحقق من السيرة الذاتية
    if (this.bio && !isValidLength(this.bio, 0, 500)) {
      errors.push('السيرة الذاتية يجب أن تكون أقل من 500 حرف');
    }
    
    // التحقق من حالة المستخدم
    if (!Object.values(USER_STATUS).includes(this.status)) {
      errors.push('حالة المستخدم غير صحيحة');
    }
    
    // التحقق من نوع المستخدم
    if (!Object.values(USER_TYPES).includes(this.userType)) {
      errors.push('نوع المستخدم غير صحيح');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * تنظيف وتطهير البيانات
   * @returns {UserModel} نسخة منظفة من المستخدم
   */
  sanitize() {
    const sanitized = new UserModel({
      ...this.toObject(),
      userName: sanitizeInput(this.userName, { maxLength: 50 }),
      firstName: sanitizeInput(this.firstName, { maxLength: 50 }),
      lastName: sanitizeInput(this.lastName, { maxLength: 50 }),
      displayName: sanitizeInput(this.displayName, { maxLength: 100 }),
      bio: sanitizeInput(this.bio, { maxLength: 500 }),
      statusMessage: sanitizeInput(this.statusMessage, { maxLength: 100 })
    });
    
    return sanitized;
  }

  /**
   * تحديث حالة المستخدم
   * @param {string} newStatus - الحالة الجديدة
   * @param {string} statusMessage - رسالة الحالة
   */
  updateStatus(newStatus, statusMessage = '') {
    if (Object.values(USER_STATUS).includes(newStatus)) {
      this.status = newStatus;
      this.statusMessage = sanitizeInput(statusMessage, { maxLength: 100 });
      this.isOnline = newStatus === USER_STATUS.ONLINE;
      
      if (newStatus === USER_STATUS.ONLINE) {
        this.lastActivity = new Date();
      } else {
        this.lastSeen = new Date();
      }
      
      this.updatedDate = new Date();
      this._version++;
    }
  }

  /**
   * تحديث آخر نشاط
   */
  updateLastActivity() {
    this.lastActivity = new Date();
    if (this.isOnline) {
      this.lastSeen = new Date();
    }
    this.updatedDate = new Date();
    this._version++;
  }

  /**
   * تسجيل الدخول
   * @param {Object} sessionInfo - معلومات الجلسة
   * @param {Object} tokenInfo - معلومات الرمز المميز
   */
  login(sessionInfo = {}, tokenInfo = {}) {
    this.isOnline = true;
    this.status = USER_STATUS.ONLINE;
    this.lastActivity = new Date();
    this.stats.lastLoginDate = new Date();
    
    // تحديث معلومات الجلسة
    this.session = {
      ...this.session,
      ...sessionInfo,
      loginDate: new Date()
    };
    
    // تحديث معلومات الرمز المميز
    if (tokenInfo.token) {
      this.tokenInfo = {
        ...this.tokenInfo,
        ...tokenInfo
      };
    }
    
    // إعادة تعيين محاولات تسجيل الدخول
    this.security.loginAttempts = 0;
    this.security.isLocked = false;
    this.security.lockUntil = null;
    
    this.updatedDate = new Date();
    this._version++;
  }

  /**
   * تسجيل الخروج
   */
  logout() {
    this.isOnline = false;
    this.status = USER_STATUS.OFFLINE;
    this.lastSeen = new Date();
    
    // مسح معلومات الجلسة
    this.session = {
      deviceId: '',
      deviceName: '',
      deviceType: '',
      ipAddress: '',
      userAgent: '',
      loginDate: null
    };
    
    // مسح معلومات الرمز المميز
    this.tokenInfo = null;
    
    this.updatedDate = new Date();
    this._version++;
  }

  /**
   * تحديث الملف الشخصي
   * @param {Object} profileData - بيانات الملف الشخصي
   */
  updateProfile(profileData) {
    const allowedFields = [
      'firstName', 'lastName', 'displayName', 'bio', 'website',
      'image', 'coverImage', 'location', 'socialLinks', 'languages'
    ];
    
    allowedFields.forEach(field => {
      if (profileData[field] !== undefined) {
        if (typeof profileData[field] === 'string') {
          this[field] = sanitizeInput(profileData[field]);
        } else {
          this[field] = profileData[field];
        }
      }
    });
    
    this.updatedDate = new Date();
    this._version++;
  }

  /**
   * تحديث إعدادات الخصوصية
   * @param {Object} privacySettings - إعدادات الخصوصية
   */
  updatePrivacySettings(privacySettings) {
    this.privacy = { ...this.privacy, ...privacySettings };
    this.updatedDate = new Date();
    this._version++;
  }

  /**
   * تحديث إعدادات الإشعارات
   * @param {Object} notificationSettings - إعدادات الإشعارات
   */
  updateNotificationSettings(notificationSettings) {
    this.notifications = { ...this.notifications, ...notificationSettings };
    this.updatedDate = new Date();
    this._version++;
  }

  /**
   * تحديث إعدادات التطبيق
   * @param {Object} appSettings - إعدادات التطبيق
   */
  updateAppSettings(appSettings) {
    this.settings = { ...this.settings, ...appSettings };
    this.updatedDate = new Date();
    this._version++;
  }

  /**
   * إضافة دور جديد
   * @param {string} role - الدور الجديد
   */
  addRole(role) {
    if (typeof role === 'string' && !this.roles.includes(role)) {
      this.roles.push(role);
      this.updatedDate = new Date();
      this._version++;
    }
  }

  /**
   * إزالة دور
   * @param {string} role - الدور المراد إزالته
   */
  removeRole(role) {
    const index = this.roles.indexOf(role);
    if (index !== -1) {
      this.roles.splice(index, 1);
      this.updatedDate = new Date();
      this._version++;
    }
  }

  /**
   * التحقق من وجود دور معين
   * @param {string} role - الدور
   * @returns {boolean} هل يملك الدور
   */
  hasRole(role) {
    return this.roles.includes(role);
  }

  /**
   * إضافة صلاحية
   * @param {string} permission - الصلاحية
   */
  addPermission(permission) {
    if (typeof permission === 'string' && !this.permissions.includes(permission)) {
      this.permissions.push(permission);
      this.updatedDate = new Date();
      this._version++;
    }
  }

  /**
   * إزالة صلاحية
   * @param {string} permission - الصلاحية
   */
  removePermission(permission) {
    const index = this.permissions.indexOf(permission);
    if (index !== -1) {
      this.permissions.splice(index, 1);
      this.updatedDate = new Date();
      this._version++;
    }
  }

  /**
   * التحقق من وجود صلاحية معينة
   * @param {string} permission - الصلاحية
   * @returns {boolean} هل يملك الصلاحية
   */
  hasPermission(permission) {
    return this.permissions.includes(permission) || this.userType === USER_TYPES.ADMIN;
  }

  /**
   * حظر المستخدم
   * @param {string} reason - سبب الحظر
   */
  block(reason = '') {
    this.isBlocked = true;
    this.isActive = false;
    this.metadata.blockReason = reason;
    this.metadata.blockedAt = new Date();
    this.updatedDate = new Date();
    this._version++;
  }

  /**
   * إلغاء حظر المستخدم
   */
  unblock() {
    this.isBlocked = false;
    this.isActive = true;
    delete this.metadata.blockReason;
    delete this.metadata.blockedAt;
    this.updatedDate = new Date();
    this._version++;
  }

  /**
   * تفعيل المصادقة الثنائية
   * @param {string} secret - المفتاح السري
   */
  enableTwoFactor(secret) {
    this.security.twoFactorEnabled = true;
    this.security.twoFactorSecret = secret;
    this.updatedDate = new Date();
    this._version++;
  }

  /**
   * إلغاء تفعيل المصادقة الثنائية
   */
  disableTwoFactor() {
    this.security.twoFactorEnabled = false;
    delete this.security.twoFactorSecret;
    this.updatedDate = new Date();
    this._version++;
  }

  /**
   * زيادة محاولات تسجيل الدخول الفاشلة
   */
  incrementLoginAttempts() {
    this.security.loginAttempts++;
    
    // قفل الحساب بعد 5 محاولات فاشلة
    if (this.security.loginAttempts >= 5) {
      this.security.isLocked = true;
      this.security.lockUntil = new Date(Date.now() + 30 * 60 * 1000); // 30 دقيقة
    }
    
    this.updatedDate = new Date();
    this._version++;
  }

  /**
   * إعادة تعيين محاولات تسجيل الدخول
   */
  resetLoginAttempts() {
    this.security.loginAttempts = 0;
    this.security.isLocked = false;
    this.security.lockUntil = null;
    this.updatedDate = new Date();
    this._version++;
  }

  /**
   * التحقق من كون الحساب مقفل
   * @returns {boolean} هل الحساب مقفل
   */
  isAccountLocked() {
    if (!this.security.isLocked) return false;
    
    // التحقق من انتهاء مدة القفل
    if (this.security.lockUntil && new Date() > this.security.lockUntil) {
      this.resetLoginAttempts();
      return false;
    }
    
    return true;
  }

  /**
   * الحصول على الاسم الكامل
   * @returns {string} الاسم الكامل
   */
  getFullName() {
    if (this.displayName) {
      return this.displayName;
    }
    
    const parts = [];
    if (this.firstName) parts.push(this.firstName);
    if (this.lastName) parts.push(this.lastName);
    
    return parts.length > 0 ? parts.join(' ') : this.userName;
  }

  /**
   * الحصول على الاسم للعرض
   * @returns {string} اسم العرض
   */
  getDisplayName() {
    return this.getFullName() || this.userName || 'مستخدم';
  }

  /**
   * الحصول على نص آخر ظهور منسق
   * @returns {string} نص آخر ظهور
   */
  getFormattedLastSeen() {
    if (this.isOnline) {
      return 'متصل الآن';
    }
    
    return formatLastSeen(this.lastSeen);
  }

  /**
   * التحقق من كون المستخدم متصل
   * @returns {boolean} هل المستخدم متصل
   */
  isUserOnline() {
    return this.isOnline && this.status === USER_STATUS.ONLINE;
  }

  /**
   * التحقق من كون المستخدم مشغول
   * @returns {boolean} هل المستخدم مشغول
   */
  isBusy() {
    return this.status === USER_STATUS.BUSY;
  }

  /**
   * التحقق من كون المستخدم غير مرئي
   * @returns {boolean} هل المستخدم غير مرئي
   */
  isInvisible() {
    return this.status === USER_STATUS.INVISIBLE;
  }

  /**
   * التحقق من كون المستخدم مدير
   * @returns {boolean} هل المستخدم مدير
   */
  isAdmin() {
    return this.userType === USER_TYPES.ADMIN || this.hasRole('admin');
  }

  /**
   * التحقق من كون المستخدم مشرف
   * @returns {boolean} هل المستخدم مشرف
   */
  isModerator() {
    return this.userType === USER_TYPES.MODERATOR || this.hasRole('moderator') || this.isAdmin();
  }

  /**
   * التحقق من انتهاء صلاحية الرمز المميز
   * @returns {boolean} هل انتهت الصلاحية
   */
  isTokenExpired() {
    if (!this.tokenInfo || !this.tokenInfo.expiresAt) {
      return true;
    }
    
    return new Date() >= this.tokenInfo.expiresAt;
  }

  /**
   * تحديث وقت المزامنة
   */
  updateSyncTime() {
    this._lastSync = new Date();
  }

  /**
   * تحويل إلى كائن عادي
   * @returns {Object} كائن البيانات
   */
  toObject() {
    return {
      id: this.id,
      userName: this.userName,
      email: this.email,
      phoneNumber: this.phoneNumber,
      firstName: this.firstName,
      lastName: this.lastName,
      displayName: this.displayName,
      image: this.image,
      coverImage: this.coverImage,
      avatar: this.avatar,
      bio: this.bio,
      website: this.website,
      status: this.status,
      statusMessage: this.statusMessage,
      isOnline: this.isOnline,
      lastSeen: this.lastSeen?.toISOString(),
      lastActivity: this.lastActivity?.toISOString(),
      userType: this.userType,
      roles: this.roles,
      permissions: this.permissions,
      isVerified: this.isVerified,
      isPremium: this.isPremium,
      location: this.location,
      socialLinks: this.socialLinks,
      languages: this.languages,
      preferredLanguage: this.preferredLanguage,
      privacy: this.privacy,
      notifications: this.notifications,
      settings: this.settings,
      security: {
        ...this.security,
        lastPasswordChange: this.security.lastPasswordChange?.toISOString(),
        lockUntil: this.security.lockUntil?.toISOString()
      },
      session: {
        ...this.session,
        loginDate: this.session.loginDate?.toISOString()
      },
      tokenInfo: this.tokenInfo ? {
        ...this.tokenInfo,
        expiresAt: this.tokenInfo.expiresAt?.toISOString()
      } : null,
      stats: {
        ...this.stats,
        joinDate: this.stats.joinDate?.toISOString(),
        lastLoginDate: this.stats.lastLoginDate?.toISOString()
      },
      createdDate: this.createdDate?.toISOString(),
      updatedDate: this.updatedDate?.toISOString(),
      deletedDate: this.deletedDate?.toISOString(),
      metadata: this.metadata,
      tags: this.tags,
      isActive: this.isActive,
      isDeleted: this.isDeleted,
      isBlocked: this.isBlocked,
      _version: this._version,
      _lastSync: this._lastSync?.toISOString(),
      _isLocal: this._isLocal
    };
  }

  /**
   * تحويل إلى JSON
   * @returns {string} نص JSON
   */
  toJSON() {
    return JSON.stringify(this.toObject());
  }

  /**
   * إنشاء نسخة من المستخدم
   * @returns {UserModel} نسخة جديدة
   */
  clone() {
    return new UserModel(this.toObject());
  }

  /**
   * إنشاء مستخدم من كائن البيانات
   * @static
   * @param {Object} data - بيانات المستخدم
   * @returns {UserModel} نموذج المستخدم
   */
  static fromObject(data) {
    return new UserModel(data);
  }

  /**
   * إنشاء مستخدم من JSON
   * @static
   * @param {string} jsonString - نص JSON
   * @returns {UserModel} نموذج المستخدم
   */
  static fromJSON(jsonString) {
    try {
      const data = JSON.parse(jsonString);
      return new UserModel(data);
    } catch (error) {
      throw new Error('فشل في تحليل JSON للمستخدم');
    }
  }

  /**
   * إنشاء مستخدم جديد
   * @static
   * @param {string} userName - اسم المستخدم
   * @param {string} email - البريد الإلكتروني
   * @param {Object} additionalData - بيانات إضافية
   * @returns {UserModel} مستخدم جديد
   */
  static createUser(userName, email, additionalData = {}) {
    return new UserModel({
      userName,
      email,
      createdDate: new Date(),
      updatedDate: new Date(),
      stats: {
        joinDate: new Date(),
        totalMessages: 0,
        totalChats: 0,
        totalContacts: 0
      },
      ...additionalData
    });
  }
}

// تصدير النموذج والثوابت
export default UserModel;
export { USER_STATUS, USER_TYPES, PRIVACY_LEVELS };
