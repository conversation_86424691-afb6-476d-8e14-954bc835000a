/**
 * المكون الأساسي - BaseComponent
 * كلاس أساسي لجميع مكونات واجهة المستخدم
 * يوفر وظائف مشتركة وإدارة دورة الحياة
 */

import eventBus from '../../core/EventBus.js';
import stateManager from '../../core/StateManager.js';
import errorHandler from '../../core/ErrorHandler.js';
import { createElement, addEventListener, removeEventListener } from '../../utils/DOMUtils.js';

/**
 * كلاس المكون الأساسي
 */
export class BaseComponent {
  constructor(options = {}) {
    // الخصائص الأساسية
    this.id = options.id || this._generateId();
    this.container = options.container || null;
    this.template = options.template || '';
    this.data = options.data || {};
    
    // حالة المكون
    this.isInitialized = false;
    this.isRendered = false;
    this.isDestroyed = false;
    this.isVisible = true;
    
    // عنصر DOM الرئيسي
    this.element = null;
    this.childElements = new Map();
    
    // مستمعي الأحداث
    this.eventListeners = new Map();
    this.stateSubscriptions = new Map();
    this.eventBusListeners = new Map();
    
    // المكونات الفرعية
    this.childComponents = new Map();
    
    // إعدادات المكون
    this.config = {
      autoRender: options.autoRender !== false,
      enableStateBinding: options.enableStateBinding !== false,
      enableEventBus: options.enableEventBus !== false,
      destroyOnRemove: options.destroyOnRemove !== false,
      ...options.config
    };
    
    // معلومات الأداء
    this.performance = {
      initTime: 0,
      renderTime: 0,
      updateCount: 0,
      lastUpdate: null
    };
    
    console.log(`🔧 تم إنشاء المكون: ${this.constructor.name} (${this.id})`);
  }

  /**
   * تهيئة المكون
   * @returns {Promise<boolean>} نجح التهيئة أم لا
   */
  async initialize() {
    if (this.isInitialized || this.isDestroyed) {
      return false;
    }

    const startTime = performance.now();

    try {
      console.log(`🚀 تهيئة المكون: ${this.constructor.name}`);

      // تهيئة الحاوي
      await this._initializeContainer();

      // تهيئة البيانات
      await this._initializeData();

      // تهيئة مستمعي الأحداث
      this._initializeEventListeners();

      // تهيئة ربط الحالة
      if (this.config.enableStateBinding) {
        this._initializeStateBinding();
      }

      // تهيئة Event Bus
      if (this.config.enableEventBus) {
        this._initializeEventBus();
      }

      // تهيئة مخصصة
      await this.onInitialize();

      // العرض التلقائي
      if (this.config.autoRender) {
        await this.render();
      }

      this.isInitialized = true;
      this.performance.initTime = performance.now() - startTime;

      // إطلاق حدث التهيئة
      this._emitEvent('initialized');

      console.log(`✅ تم تهيئة المكون: ${this.constructor.name} في ${this.performance.initTime.toFixed(2)} مللي ثانية`);

      return true;
    } catch (error) {
      console.error(`❌ فشل في تهيئة المكون: ${this.constructor.name}`, error);
      
      errorHandler.handleError(error, {
        type: 'COMPONENT',
        severity: 'HIGH',
        context: {
          component: this.constructor.name,
          id: this.id,
          phase: 'initialization'
        }
      });

      return false;
    }
  }

  /**
   * عرض المكون
   * @returns {Promise<boolean>} نجح العرض أم لا
   */
  async render() {
    if (!this.isInitialized || this.isDestroyed) {
      console.warn(`⚠️ لا يمكن عرض المكون غير المهيأ: ${this.constructor.name}`);
      return false;
    }

    const startTime = performance.now();

    try {
      console.log(`🎨 عرض المكون: ${this.constructor.name}`);

      // إنشاء العنصر الرئيسي
      await this._createElement();

      // عرض المحتوى
      await this._renderContent();

      // ربط الأحداث
      this._bindEvents();

      // عرض المكونات الفرعية
      await this._renderChildComponents();

      // عرض مخصص
      await this.onRender();

      // إضافة إلى الحاوي
      if (this.container && this.element) {
        this.container.appendChild(this.element);
      }

      this.isRendered = true;
      this.performance.renderTime = performance.now() - startTime;
      this.performance.lastUpdate = new Date();

      // إطلاق حدث العرض
      this._emitEvent('rendered');

      console.log(`✅ تم عرض المكون: ${this.constructor.name} في ${this.performance.renderTime.toFixed(2)} مللي ثانية`);

      return true;
    } catch (error) {
      console.error(`❌ فشل في عرض المكون: ${this.constructor.name}`, error);
      
      errorHandler.handleError(error, {
        type: 'COMPONENT',
        severity: 'HIGH',
        context: {
          component: this.constructor.name,
          id: this.id,
          phase: 'rendering'
        }
      });

      return false;
    }
  }

  /**
   * تحديث المكون
   * @param {Object} newData - البيانات الجديدة
   * @returns {Promise<boolean>} نجح التحديث أم لا
   */
  async update(newData = {}) {
    if (!this.isRendered || this.isDestroyed) {
      return false;
    }

    try {
      console.log(`🔄 تحديث المكون: ${this.constructor.name}`);

      // دمج البيانات الجديدة
      this.data = { ...this.data, ...newData };

      // تحديث مخصص
      await this.onUpdate(newData);

      // إعادة عرض المحتوى
      await this._renderContent();

      // تحديث المكونات الفرعية
      await this._updateChildComponents();

      this.performance.updateCount++;
      this.performance.lastUpdate = new Date();

      // إطلاق حدث التحديث
      this._emitEvent('updated', { data: newData });

      console.log(`✅ تم تحديث المكون: ${this.constructor.name}`);

      return true;
    } catch (error) {
      console.error(`❌ فشل في تحديث المكون: ${this.constructor.name}`, error);
      
      errorHandler.handleError(error, {
        type: 'COMPONENT',
        severity: 'MEDIUM',
        context: {
          component: this.constructor.name,
          id: this.id,
          phase: 'updating'
        }
      });

      return false;
    }
  }

  /**
   * إخفاء المكون
   */
  hide() {
    if (this.element) {
      this.element.style.display = 'none';
      this.isVisible = false;
      this._emitEvent('hidden');
    }
  }

  /**
   * إظهار المكون
   */
  show() {
    if (this.element) {
      this.element.style.display = '';
      this.isVisible = true;
      this._emitEvent('shown');
    }
  }

  /**
   * تبديل رؤية المكون
   */
  toggle() {
    if (this.isVisible) {
      this.hide();
    } else {
      this.show();
    }
  }

  /**
   * تدمير المكون
   */
  destroy() {
    if (this.isDestroyed) {
      return;
    }

    try {
      console.log(`🗑️ تدمير المكون: ${this.constructor.name}`);

      // تدمير مخصص
      this.onDestroy();

      // تدمير المكونات الفرعية
      this._destroyChildComponents();

      // إزالة مستمعي الأحداث
      this._removeEventListeners();

      // إزالة اشتراكات الحالة
      this._removeStateSubscriptions();

      // إزالة مستمعي Event Bus
      this._removeEventBusListeners();

      // إزالة العنصر من DOM
      if (this.element && this.element.parentNode) {
        this.element.parentNode.removeChild(this.element);
      }

      // تنظيف المراجع
      this.element = null;
      this.container = null;
      this.childElements.clear();
      this.childComponents.clear();

      this.isDestroyed = true;

      // إطلاق حدث التدمير
      this._emitEvent('destroyed');

      console.log(`✅ تم تدمير المكون: ${this.constructor.name}`);
    } catch (error) {
      console.error(`❌ فشل في تدمير المكون: ${this.constructor.name}`, error);
    }
  }

  /**
   * إضافة مكون فرعي
   * @param {string} name - اسم المكون
   * @param {BaseComponent} component - المكون الفرعي
   */
  addChildComponent(name, component) {
    if (component instanceof BaseComponent) {
      this.childComponents.set(name, component);
      
      // تهيئة المكون الفرعي إذا لم يكن مهيأ
      if (!component.isInitialized) {
        component.initialize();
      }
    }
  }

  /**
   * إزالة مكون فرعي
   * @param {string} name - اسم المكون
   */
  removeChildComponent(name) {
    const component = this.childComponents.get(name);
    if (component) {
      if (this.config.destroyOnRemove) {
        component.destroy();
      }
      this.childComponents.delete(name);
    }
  }

  /**
   * الحصول على مكون فرعي
   * @param {string} name - اسم المكون
   * @returns {BaseComponent|null} المكون الفرعي
   */
  getChildComponent(name) {
    return this.childComponents.get(name) || null;
  }

  /**
   * إضافة مستمع حدث
   * @param {Element} element - العنصر
   * @param {string} event - نوع الحدث
   * @param {Function} handler - معالج الحدث
   * @param {Object} options - خيارات الحدث
   */
  addEventListener(element, event, handler, options = {}) {
    if (!element || !handler) return;

    const wrappedHandler = (e) => {
      try {
        handler.call(this, e);
      } catch (error) {
        errorHandler.handleError(error, {
          type: 'COMPONENT',
          severity: 'MEDIUM',
          context: {
            component: this.constructor.name,
            event,
            element: element.tagName
          }
        });
      }
    };

    addEventListener(element, event, wrappedHandler, options);

    // حفظ المرجع للتنظيف لاحقاً
    const key = `${element.tagName}_${event}_${Date.now()}`;
    this.eventListeners.set(key, {
      element,
      event,
      handler: wrappedHandler,
      options
    });
  }

  /**
   * الاشتراك في تغييرات الحالة
   * @param {string} statePath - مسار الحالة
   * @param {Function} callback - دالة الاستدعاء
   * @returns {string} معرف الاشتراك
   */
  subscribeToState(statePath, callback) {
    if (!this.config.enableStateBinding) return null;

    const subscriptionId = stateManager.subscribe(statePath, (value, oldValue) => {
      try {
        callback.call(this, value, oldValue);
      } catch (error) {
        errorHandler.handleError(error, {
          type: 'COMPONENT',
          severity: 'MEDIUM',
          context: {
            component: this.constructor.name,
            statePath
          }
        });
      }
    });

    this.stateSubscriptions.set(statePath, subscriptionId);
    return subscriptionId;
  }

  /**
   * الاشتراك في أحداث Event Bus
   * @param {string} eventName - اسم الحدث
   * @param {Function} callback - دالة الاستدعاء
   * @returns {string} معرف المستمع
   */
  subscribeToEvent(eventName, callback) {
    if (!this.config.enableEventBus) return null;

    const listenerId = eventBus.on(eventName, (data) => {
      try {
        callback.call(this, data);
      } catch (error) {
        errorHandler.handleError(error, {
          type: 'COMPONENT',
          severity: 'MEDIUM',
          context: {
            component: this.constructor.name,
            eventName
          }
        });
      }
    });

    this.eventBusListeners.set(eventName, listenerId);
    return listenerId;
  }

  /**
   * إطلاق حدث مخصص
   * @param {string} eventName - اسم الحدث
   * @param {*} data - بيانات الحدث
   */
  emit(eventName, data = null) {
    if (this.config.enableEventBus) {
      eventBus.emit(`component:${this.constructor.name}:${eventName}`, {
        componentId: this.id,
        data
      });
    }
  }

  /**
   * الحصول على إحصائيات الأداء
   * @returns {Object} إحصائيات الأداء
   */
  getPerformanceStats() {
    return {
      ...this.performance,
      isInitialized: this.isInitialized,
      isRendered: this.isRendered,
      isVisible: this.isVisible,
      childComponentsCount: this.childComponents.size,
      eventListenersCount: this.eventListeners.size,
      stateSubscriptionsCount: this.stateSubscriptions.size
    };
  }

  // دوال يمكن إعادة تعريفها في المكونات الفرعية

  /**
   * تهيئة مخصصة - يمكن إعادة تعريفها
   */
  async onInitialize() {
    // تنفيذ مخصص في المكونات الفرعية
  }

  /**
   * عرض مخصص - يمكن إعادة تعريفها
   */
  async onRender() {
    // تنفيذ مخصص في المكونات الفرعية
  }

  /**
   * تحديث مخصص - يمكن إعادة تعريفها
   * @param {Object} newData - البيانات الجديدة
   */
  async onUpdate(newData) {
    // تنفيذ مخصص في المكونات الفرعية
  }

  /**
   * تدمير مخصص - يمكن إعادة تعريفها
   */
  onDestroy() {
    // تنفيذ مخصص في المكونات الفرعية
  }

  /**
   * إنشاء القالب - يجب إعادة تعريفها
   * @returns {string} HTML القالب
   */
  getTemplate() {
    return this.template || '<div></div>';
  }

  // دوال خاصة

  /**
   * تهيئة الحاوي
   * @private
   */
  async _initializeContainer() {
    if (typeof this.container === 'string') {
      this.container = document.querySelector(this.container);
    }

    if (!this.container) {
      throw new Error(`لم يتم العثور على الحاوي للمكون: ${this.constructor.name}`);
    }
  }

  /**
   * تهيئة البيانات
   * @private
   */
  async _initializeData() {
    // يمكن إعادة تعريفها في المكونات الفرعية
  }

  /**
   * تهيئة مستمعي الأحداث
   * @private
   */
  _initializeEventListeners() {
    // يمكن إعادة تعريفها في المكونات الفرعية
  }

  /**
   * تهيئة ربط الحالة
   * @private
   */
  _initializeStateBinding() {
    // يمكن إعادة تعريفها في المكونات الفرعية
  }

  /**
   * تهيئة Event Bus
   * @private
   */
  _initializeEventBus() {
    // يمكن إعادة تعريفها في المكونات الفرعية
  }

  /**
   * إنشاء العنصر الرئيسي
   * @private
   */
  async _createElement() {
    if (this.element) {
      return;
    }

    const template = this.getTemplate();
    
    if (template.trim().startsWith('<')) {
      // HTML template
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = template.trim();
      this.element = tempDiv.firstChild;
    } else {
      // نص عادي
      this.element = createElement('div', {
        className: `component-${this.constructor.name.toLowerCase()}`,
        id: this.id
      }, template);
    }

    if (this.element) {
      this.element.setAttribute('data-component', this.constructor.name);
      this.element.setAttribute('data-component-id', this.id);
    }
  }

  /**
   * عرض المحتوى
   * @private
   */
  async _renderContent() {
    // يمكن إعادة تعريفها في المكونات الفرعية
  }

  /**
   * ربط الأحداث
   * @private
   */
  _bindEvents() {
    // يمكن إعادة تعريفها في المكونات الفرعية
  }

  /**
   * عرض المكونات الفرعية
   * @private
   */
  async _renderChildComponents() {
    for (const [name, component] of this.childComponents) {
      if (!component.isRendered) {
        await component.render();
      }
    }
  }

  /**
   * تحديث المكونات الفرعية
   * @private
   */
  async _updateChildComponents() {
    for (const [name, component] of this.childComponents) {
      await component.update();
    }
  }

  /**
   * تدمير المكونات الفرعية
   * @private
   */
  _destroyChildComponents() {
    for (const [name, component] of this.childComponents) {
      component.destroy();
    }
    this.childComponents.clear();
  }

  /**
   * إزالة مستمعي الأحداث
   * @private
   */
  _removeEventListeners() {
    for (const [key, listener] of this.eventListeners) {
      removeEventListener(
        listener.element,
        listener.event,
        listener.handler,
        listener.options
      );
    }
    this.eventListeners.clear();
  }

  /**
   * إزالة اشتراكات الحالة
   * @private
   */
  _removeStateSubscriptions() {
    for (const [statePath, subscriptionId] of this.stateSubscriptions) {
      stateManager.unsubscribe(statePath, subscriptionId);
    }
    this.stateSubscriptions.clear();
  }

  /**
   * إزالة مستمعي Event Bus
   * @private
   */
  _removeEventBusListeners() {
    for (const [eventName, listenerId] of this.eventBusListeners) {
      eventBus.off(eventName, listenerId);
    }
    this.eventBusListeners.clear();
  }

  /**
   * إطلاق حدث داخلي
   * @private
   */
  _emitEvent(eventName, data = null) {
    this.emit(eventName, data);
  }

  /**
   * إنشاء معرف فريد
   * @private
   */
  _generateId() {
    return `component_${this.constructor.name}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

export default BaseComponent;
