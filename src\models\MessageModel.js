/**
 * نموذج الرسالة - MessageModel
 * يمثل بنية بيانات الرسالة مع دوال التحقق والتحويل
 * يدعم أنواع مختلفة من الرسائل مع إدارة الحالة والمرفقات
 */

import { formatTime, formatFullDateTime, parseDate } from '../utils/DateUtils.js';
import { sanitizeInput, isValidLength } from '../utils/ValidationUtils.js';

// أنواع الرسائل
export const MESSAGE_TYPES = {
  TEXT: 'Text',
  IMAGE: 'Image',
  VIDEO: 'Video',
  AUDIO: 'Audio',
  VOICE: 'Voice',
  FILE: 'File',
  LOCATION: 'Location',
  CONTACT: 'Contact',
  POLL: 'Poll',
  SYSTEM: 'System'
};

// حالات الرسالة
export const MESSAGE_STATUS = {
  SENDING: 'Sending',
  SENT: 'Sent',
  DELIVERED: 'Delivered',
  READ: 'Read',
  FAILED: 'Failed',
  DELETED: 'Deleted'
};

// اتجاه الرسالة
export const MESSAGE_DIRECTION = {
  INCOMING: 'Incoming',
  OUTGOING: 'Outgoing'
};

/**
 * كلاس نموذج الرسالة
 */
export class MessageModel {
  constructor(data = {}) {
    // الخصائص الأساسية
    this.id = data.id || null;
    this.locId = data.locId || this._generateLocalId();
    this.chatID = data.chatID || null;
    this.senderID = data.senderID || null;
    this.receiverID = data.receiverID || null;
    
    // محتوى الرسالة
    this.messageText = data.messageText || '';
    this.messageType = data.messageType || MESSAGE_TYPES.TEXT;
    this.messageStatus = data.messageStatus || MESSAGE_STATUS.SENDING;
    this.direction = data.direction || MESSAGE_DIRECTION.OUTGOING;
    
    // معلومات المرسل
    this.senderName = data.senderName || '';
    this.senderImage = data.senderImage || '';
    this.senderRole = data.senderRole || '';
    
    // المرفقات والوسائط
    this.attachments = data.attachments || [];
    this.mediaUrl = data.mediaUrl || '';
    this.thumbnailUrl = data.thumbnailUrl || '';
    this.fileName = data.fileName || '';
    this.fileSize = data.fileSize || 0;
    this.fileMimeType = data.fileMimeType || '';
    this.duration = data.duration || 0; // للصوت والفيديو
    
    // الموقع (للرسائل الجغرافية)
    this.location = data.location ? {
      latitude: data.location.latitude || 0,
      longitude: data.location.longitude || 0,
      address: data.location.address || '',
      name: data.location.name || ''
    } : null;
    
    // جهة الاتصال (لرسائل جهات الاتصال)
    this.contact = data.contact ? {
      name: data.contact.name || '',
      phone: data.contact.phone || '',
      email: data.contact.email || '',
      image: data.contact.image || ''
    } : null;
    
    // الاستطلاع (لرسائل الاستطلاع)
    this.poll = data.poll ? {
      question: data.poll.question || '',
      options: data.poll.options || [],
      allowMultiple: data.poll.allowMultiple || false,
      votes: data.poll.votes || [],
      totalVotes: data.poll.totalVotes || 0,
      expiresAt: data.poll.expiresAt ? parseDate(data.poll.expiresAt) : null
    } : null;
    
    // الرد على رسالة
    this.replyTo = data.replyTo ? {
      messageId: data.replyTo.messageId || null,
      messageText: data.replyTo.messageText || '',
      senderName: data.replyTo.senderName || '',
      messageType: data.replyTo.messageType || MESSAGE_TYPES.TEXT
    } : null;
    
    // التوقيتات
    this.createdDate = data.createdDate ? parseDate(data.createdDate) : new Date();
    this.updatedDate = data.updatedDate ? parseDate(data.updatedDate) : new Date();
    this.deliveredDate = data.deliveredDate ? parseDate(data.deliveredDate) : null;
    this.readDate = data.readDate ? parseDate(data.readDate) : null;
    this.deletedDate = data.deletedDate ? parseDate(data.deletedDate) : null;
    
    // معلومات التحرير
    this.isEdited = data.isEdited || false;
    this.editHistory = data.editHistory || [];
    this.lastEditDate = data.lastEditDate ? parseDate(data.lastEditDate) : null;
    
    // معلومات الحذف
    this.isDeleted = data.isDeleted || false;
    this.deletedBy = data.deletedBy || null;
    this.deleteReason = data.deleteReason || '';
    
    // التفاعلات
    this.reactions = data.reactions || [];
    this.mentions = data.mentions || [];
    this.hashtags = data.hashtags || [];
    
    // الأمان والخصوصية
    this.isEncrypted = data.isEncrypted || false;
    this.isForwarded = data.isForwarded || false;
    this.forwardedFrom = data.forwardedFrom || null;
    this.isStarred = data.isStarred || false;
    
    // معلومات إضافية
    this.metadata = data.metadata || {};
    this.priority = data.priority || 'normal'; // low, normal, high, urgent
    this.expiresAt = data.expiresAt ? parseDate(data.expiresAt) : null;
    
    // معلومات المزامنة
    this._version = data._version || 1;
    this._lastSync = data._lastSync ? parseDate(data._lastSync) : null;
    this._isLocal = data._isLocal || false;
    this._needsSync = data._needsSync || false;
  }

  /**
   * التحقق من صحة بيانات الرسالة
   * @returns {Object} نتيجة التحقق
   */
  validate() {
    const errors = [];
    
    // التحقق من معرف المحادثة
    if (!this.chatID) {
      errors.push('معرف المحادثة مطلوب');
    }
    
    // التحقق من معرف المرسل
    if (!this.senderID) {
      errors.push('معرف المرسل مطلوب');
    }
    
    // التحقق من نوع الرسالة
    if (!Object.values(MESSAGE_TYPES).includes(this.messageType)) {
      errors.push('نوع الرسالة غير صحيح');
    }
    
    // التحقق من حالة الرسالة
    if (!Object.values(MESSAGE_STATUS).includes(this.messageStatus)) {
      errors.push('حالة الرسالة غير صحيحة');
    }
    
    // التحقق من اتجاه الرسالة
    if (!Object.values(MESSAGE_DIRECTION).includes(this.direction)) {
      errors.push('اتجاه الرسالة غير صحيح');
    }
    
    // التحقق من محتوى الرسالة حسب النوع
    if (this.messageType === MESSAGE_TYPES.TEXT) {
      if (!isValidLength(this.messageText, 1, 4000)) {
        errors.push('نص الرسالة يجب أن يكون بين 1 و 4000 حرف');
      }
    }
    
    // التحقق من الملفات المرفقة
    if ([MESSAGE_TYPES.IMAGE, MESSAGE_TYPES.VIDEO, MESSAGE_TYPES.AUDIO, MESSAGE_TYPES.FILE].includes(this.messageType)) {
      if (!this.mediaUrl && this.attachments.length === 0) {
        errors.push('الرسالة تتطلب مرفق أو رابط وسائط');
      }
    }
    
    // التحقق من بيانات الموقع
    if (this.messageType === MESSAGE_TYPES.LOCATION) {
      if (!this.location || !this.location.latitude || !this.location.longitude) {
        errors.push('بيانات الموقع غير مكتملة');
      }
    }
    
    // التحقق من بيانات جهة الاتصال
    if (this.messageType === MESSAGE_TYPES.CONTACT) {
      if (!this.contact || !this.contact.name) {
        errors.push('بيانات جهة الاتصال غير مكتملة');
      }
    }
    
    // التحقق من بيانات الاستطلاع
    if (this.messageType === MESSAGE_TYPES.POLL) {
      if (!this.poll || !this.poll.question || this.poll.options.length < 2) {
        errors.push('بيانات الاستطلاع غير مكتملة');
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * تنظيف وتطهير البيانات
   * @returns {MessageModel} نسخة منظفة من الرسالة
   */
  sanitize() {
    const sanitized = new MessageModel({
      ...this.toObject(),
      messageText: sanitizeInput(this.messageText, { maxLength: 4000 }),
      senderName: sanitizeInput(this.senderName, { maxLength: 100 })
    });
    
    return sanitized;
  }

  /**
   * تحديث حالة الرسالة
   * @param {string} newStatus - الحالة الجديدة
   */
  updateStatus(newStatus) {
    if (Object.values(MESSAGE_STATUS).includes(newStatus)) {
      this.messageStatus = newStatus;
      this.updatedDate = new Date();
      
      // تحديث التوقيتات حسب الحالة
      switch (newStatus) {
        case MESSAGE_STATUS.DELIVERED:
          this.deliveredDate = new Date();
          break;
        case MESSAGE_STATUS.READ:
          this.readDate = new Date();
          break;
        case MESSAGE_STATUS.DELETED:
          this.deletedDate = new Date();
          this.isDeleted = true;
          break;
      }
      
      this._version++;
      this._needsSync = true;
    }
  }

  /**
   * تحرير محتوى الرسالة
   * @param {string} newText - النص الجديد
   * @param {string} editReason - سبب التحرير
   */
  edit(newText, editReason = '') {
    if (this.messageType === MESSAGE_TYPES.TEXT && isValidLength(newText, 1, 4000)) {
      // حفظ النص القديم في التاريخ
      this.editHistory.push({
        oldText: this.messageText,
        editDate: new Date(),
        editReason
      });
      
      // تحديث النص
      this.messageText = sanitizeInput(newText, { maxLength: 4000 });
      this.isEdited = true;
      this.lastEditDate = new Date();
      this.updatedDate = new Date();
      this._version++;
      this._needsSync = true;
    }
  }

  /**
   * حذف الرسالة
   * @param {string} deletedBy - معرف من قام بالحذف
   * @param {string} reason - سبب الحذف
   */
  delete(deletedBy, reason = '') {
    this.isDeleted = true;
    this.deletedBy = deletedBy;
    this.deleteReason = reason;
    this.deletedDate = new Date();
    this.messageStatus = MESSAGE_STATUS.DELETED;
    this.updatedDate = new Date();
    this._version++;
    this._needsSync = true;
  }

  /**
   * إضافة تفاعل على الرسالة
   * @param {string} userId - معرف المستخدم
   * @param {string} reaction - نوع التفاعل (emoji)
   */
  addReaction(userId, reaction) {
    // إزالة التفاعل السابق للمستخدم إن وجد
    this.reactions = this.reactions.filter(r => r.userId !== userId);
    
    // إضافة التفاعل الجديد
    this.reactions.push({
      userId,
      reaction,
      timestamp: new Date()
    });
    
    this.updatedDate = new Date();
    this._version++;
    this._needsSync = true;
  }

  /**
   * إزالة تفاعل من الرسالة
   * @param {string} userId - معرف المستخدم
   */
  removeReaction(userId) {
    const initialLength = this.reactions.length;
    this.reactions = this.reactions.filter(r => r.userId !== userId);
    
    if (this.reactions.length !== initialLength) {
      this.updatedDate = new Date();
      this._version++;
      this._needsSync = true;
    }
  }

  /**
   * تثبيت/إلغاء تثبيت الرسالة
   */
  toggleStar() {
    this.isStarred = !this.isStarred;
    this.updatedDate = new Date();
    this._version++;
    this._needsSync = true;
  }

  /**
   * إضافة منشن
   * @param {Object} mention - بيانات المنشن
   */
  addMention(mention) {
    if (mention.userId && mention.userName) {
      this.mentions.push({
        userId: mention.userId,
        userName: mention.userName,
        startIndex: mention.startIndex || 0,
        length: mention.length || mention.userName.length
      });
      
      this.updatedDate = new Date();
      this._version++;
    }
  }

  /**
   * إضافة هاشتاج
   * @param {string} hashtag - الهاشتاج
   */
  addHashtag(hashtag) {
    if (typeof hashtag === 'string' && !this.hashtags.includes(hashtag)) {
      this.hashtags.push(hashtag);
      this.updatedDate = new Date();
      this._version++;
    }
  }

  /**
   * إضافة مرفق
   * @param {Object} attachment - بيانات المرفق
   */
  addAttachment(attachment) {
    if (attachment.url || attachment.file) {
      this.attachments.push({
        id: attachment.id || this._generateAttachmentId(),
        name: attachment.name || '',
        url: attachment.url || '',
        type: attachment.type || '',
        size: attachment.size || 0,
        thumbnailUrl: attachment.thumbnailUrl || '',
        uploadedAt: new Date()
      });
      
      this.updatedDate = new Date();
      this._version++;
      this._needsSync = true;
    }
  }

  /**
   * إزالة مرفق
   * @param {string} attachmentId - معرف المرفق
   */
  removeAttachment(attachmentId) {
    const initialLength = this.attachments.length;
    this.attachments = this.attachments.filter(a => a.id !== attachmentId);
    
    if (this.attachments.length !== initialLength) {
      this.updatedDate = new Date();
      this._version++;
      this._needsSync = true;
    }
  }

  /**
   * التصويت في استطلاع
   * @param {string} userId - معرف المستخدم
   * @param {Array} optionIds - معرفات الخيارات المختارة
   */
  voteInPoll(userId, optionIds) {
    if (this.messageType !== MESSAGE_TYPES.POLL || !this.poll) {
      return false;
    }
    
    // التحقق من انتهاء صلاحية الاستطلاع
    if (this.poll.expiresAt && new Date() > this.poll.expiresAt) {
      return false;
    }
    
    // إزالة الأصوات السابقة للمستخدم
    this.poll.votes = this.poll.votes.filter(v => v.userId !== userId);
    
    // إضافة الأصوات الجديدة
    optionIds.forEach(optionId => {
      this.poll.votes.push({
        userId,
        optionId,
        timestamp: new Date()
      });
    });
    
    // تحديث إجمالي الأصوات
    this.poll.totalVotes = this.poll.votes.length;
    
    this.updatedDate = new Date();
    this._version++;
    this._needsSync = true;
    
    return true;
  }

  /**
   * الحصول على نص الرسالة للعرض
   * @param {number} maxLength - الحد الأقصى لطول النص
   * @returns {string} نص الرسالة
   */
  getDisplayText(maxLength = 100) {
    if (this.isDeleted) {
      return 'تم حذف هذه الرسالة';
    }
    
    let text = '';
    
    switch (this.messageType) {
      case MESSAGE_TYPES.TEXT:
        text = this.messageText;
        break;
      case MESSAGE_TYPES.IMAGE:
        text = '📷 صورة';
        break;
      case MESSAGE_TYPES.VIDEO:
        text = '🎥 فيديو';
        break;
      case MESSAGE_TYPES.AUDIO:
      case MESSAGE_TYPES.VOICE:
        text = '🎵 رسالة صوتية';
        break;
      case MESSAGE_TYPES.FILE:
        text = `📎 ${this.fileName || 'ملف'}`;
        break;
      case MESSAGE_TYPES.LOCATION:
        text = '📍 موقع';
        break;
      case MESSAGE_TYPES.CONTACT:
        text = `👤 ${this.contact?.name || 'جهة اتصال'}`;
        break;
      case MESSAGE_TYPES.POLL:
        text = `📊 ${this.poll?.question || 'استطلاع'}`;
        break;
      case MESSAGE_TYPES.SYSTEM:
        text = this.messageText;
        break;
      default:
        text = this.messageText;
    }
    
    // تقصير النص إذا كان طويل
    if (text.length > maxLength) {
      text = text.substring(0, maxLength) + '...';
    }
    
    return text;
  }

  /**
   * الحصول على وقت الرسالة منسق
   * @returns {string} الوقت المنسق
   */
  getFormattedTime() {
    return formatTime(this.createdDate);
  }

  /**
   * الحصول على تاريخ ووقت الرسالة منسق
   * @returns {string} التاريخ والوقت المنسق
   */
  getFormattedDateTime() {
    return formatFullDateTime(this.createdDate);
  }

  /**
   * التحقق من كون الرسالة مقروءة
   * @returns {boolean} هل الرسالة مقروءة
   */
  isRead() {
    return this.messageStatus === MESSAGE_STATUS.READ;
  }

  /**
   * التحقق من كون الرسالة مُسلمة
   * @returns {boolean} هل الرسالة مُسلمة
   */
  isDelivered() {
    return [MESSAGE_STATUS.DELIVERED, MESSAGE_STATUS.READ].includes(this.messageStatus);
  }

  /**
   * التحقق من كون الرسالة فاشلة
   * @returns {boolean} هل الرسالة فاشلة
   */
  isFailed() {
    return this.messageStatus === MESSAGE_STATUS.FAILED;
  }

  /**
   * التحقق من كون الرسالة قيد الإرسال
   * @returns {boolean} هل الرسالة قيد الإرسال
   */
  isSending() {
    return this.messageStatus === MESSAGE_STATUS.SENDING;
  }

  /**
   * التحقق من كون الرسالة تحتوي على وسائط
   * @returns {boolean} هل تحتوي على وسائط
   */
  hasMedia() {
    return [MESSAGE_TYPES.IMAGE, MESSAGE_TYPES.VIDEO, MESSAGE_TYPES.AUDIO, MESSAGE_TYPES.VOICE, MESSAGE_TYPES.FILE].includes(this.messageType);
  }

  /**
   * التحقق من انتهاء صلاحية الرسالة
   * @returns {boolean} هل انتهت الصلاحية
   */
  isExpired() {
    return this.expiresAt && new Date() > this.expiresAt;
  }

  /**
   * الحصول على حجم الملف منسق
   * @returns {string} حجم الملف المنسق
   */
  getFormattedFileSize() {
    if (!this.fileSize) return '';
    
    const units = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
    let size = this.fileSize;
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return `${size.toFixed(1)} ${units[unitIndex]}`;
  }

  /**
   * الحصول على مدة الصوت/الفيديو منسقة
   * @returns {string} المدة المنسقة
   */
  getFormattedDuration() {
    if (!this.duration) return '';
    
    const minutes = Math.floor(this.duration / 60);
    const seconds = Math.floor(this.duration % 60);
    
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }

  /**
   * تحديث وقت المزامنة
   */
  updateSyncTime() {
    this._lastSync = new Date();
    this._needsSync = false;
  }

  /**
   * تحويل إلى كائن عادي
   * @returns {Object} كائن البيانات
   */
  toObject() {
    return {
      id: this.id,
      locId: this.locId,
      chatID: this.chatID,
      senderID: this.senderID,
      receiverID: this.receiverID,
      messageText: this.messageText,
      messageType: this.messageType,
      messageStatus: this.messageStatus,
      direction: this.direction,
      senderName: this.senderName,
      senderImage: this.senderImage,
      senderRole: this.senderRole,
      attachments: this.attachments,
      mediaUrl: this.mediaUrl,
      thumbnailUrl: this.thumbnailUrl,
      fileName: this.fileName,
      fileSize: this.fileSize,
      fileMimeType: this.fileMimeType,
      duration: this.duration,
      location: this.location,
      contact: this.contact,
      poll: this.poll ? {
        ...this.poll,
        expiresAt: this.poll.expiresAt?.toISOString()
      } : null,
      replyTo: this.replyTo,
      createdDate: this.createdDate?.toISOString(),
      updatedDate: this.updatedDate?.toISOString(),
      deliveredDate: this.deliveredDate?.toISOString(),
      readDate: this.readDate?.toISOString(),
      deletedDate: this.deletedDate?.toISOString(),
      isEdited: this.isEdited,
      editHistory: this.editHistory,
      lastEditDate: this.lastEditDate?.toISOString(),
      isDeleted: this.isDeleted,
      deletedBy: this.deletedBy,
      deleteReason: this.deleteReason,
      reactions: this.reactions,
      mentions: this.mentions,
      hashtags: this.hashtags,
      isEncrypted: this.isEncrypted,
      isForwarded: this.isForwarded,
      forwardedFrom: this.forwardedFrom,
      isStarred: this.isStarred,
      metadata: this.metadata,
      priority: this.priority,
      expiresAt: this.expiresAt?.toISOString(),
      _version: this._version,
      _lastSync: this._lastSync?.toISOString(),
      _isLocal: this._isLocal,
      _needsSync: this._needsSync
    };
  }

  /**
   * تحويل إلى JSON
   * @returns {string} نص JSON
   */
  toJSON() {
    return JSON.stringify(this.toObject());
  }

  /**
   * إنشاء نسخة من الرسالة
   * @returns {MessageModel} نسخة جديدة
   */
  clone() {
    return new MessageModel(this.toObject());
  }

  /**
   * إنشاء معرف محلي فريد
   * @private
   * @returns {string} معرف محلي
   */
  _generateLocalId() {
    return `local_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * إنشاء معرف مرفق فريد
   * @private
   * @returns {string} معرف المرفق
   */
  _generateAttachmentId() {
    return `att_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * إنشاء رسالة من كائن البيانات
   * @static
   * @param {Object} data - بيانات الرسالة
   * @returns {MessageModel} نموذج الرسالة
   */
  static fromObject(data) {
    return new MessageModel(data);
  }

  /**
   * إنشاء رسالة من JSON
   * @static
   * @param {string} jsonString - نص JSON
   * @returns {MessageModel} نموذج الرسالة
   */
  static fromJSON(jsonString) {
    try {
      const data = JSON.parse(jsonString);
      return new MessageModel(data);
    } catch (error) {
      throw new Error('فشل في تحليل JSON للرسالة');
    }
  }

  /**
   * إنشاء رسالة نصية جديدة
   * @static
   * @param {string} text - نص الرسالة
   * @param {string} chatId - معرف المحادثة
   * @param {string} senderId - معرف المرسل
   * @param {Object} options - خيارات إضافية
   * @returns {MessageModel} رسالة نصية جديدة
   */
  static createTextMessage(text, chatId, senderId, options = {}) {
    return new MessageModel({
      messageText: text,
      messageType: MESSAGE_TYPES.TEXT,
      chatID: chatId,
      senderID: senderId,
      direction: options.direction || MESSAGE_DIRECTION.OUTGOING,
      senderName: options.senderName || '',
      senderImage: options.senderImage || '',
      replyTo: options.replyTo || null,
      ...options
    });
  }

  /**
   * إنشاء رسالة وسائط جديدة
   * @static
   * @param {string} type - نوع الوسائط
   * @param {string} mediaUrl - رابط الوسائط
   * @param {string} chatId - معرف المحادثة
   * @param {string} senderId - معرف المرسل
   * @param {Object} options - خيارات إضافية
   * @returns {MessageModel} رسالة وسائط جديدة
   */
  static createMediaMessage(type, mediaUrl, chatId, senderId, options = {}) {
    return new MessageModel({
      messageType: type,
      mediaUrl,
      chatID: chatId,
      senderID: senderId,
      direction: options.direction || MESSAGE_DIRECTION.OUTGOING,
      fileName: options.fileName || '',
      fileSize: options.fileSize || 0,
      fileMimeType: options.fileMimeType || '',
      thumbnailUrl: options.thumbnailUrl || '',
      duration: options.duration || 0,
      messageText: options.caption || '',
      ...options
    });
  }

  /**
   * إنشاء رسالة نظام
   * @static
   * @param {string} text - نص الرسالة
   * @param {string} chatId - معرف المحادثة
   * @param {Object} options - خيارات إضافية
   * @returns {MessageModel} رسالة نظام
   */
  static createSystemMessage(text, chatId, options = {}) {
    return new MessageModel({
      messageText: text,
      messageType: MESSAGE_TYPES.SYSTEM,
      chatID: chatId,
      senderID: 'system',
      direction: MESSAGE_DIRECTION.INCOMING,
      messageStatus: MESSAGE_STATUS.READ,
      ...options
    });
  }
}

// تصدير النموذج والثوابت
export default MessageModel;
export { MESSAGE_TYPES, MESSAGE_STATUS, MESSAGE_DIRECTION };
