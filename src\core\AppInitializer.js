/**
 * مهيئ التطبيق - AppInitializer
 * يدير عملية تهيئة التطبيق بالكامل مع معالجة الأخطاء والتحميل التدريجي
 * يضمن تهيئة جميع الخدمات والمكونات بالترتيب الصحيح
 */

import eventBus from './EventBus.js';
import stateManager from './StateManager.js';
import errorHandler, { ERROR_TYPES, ERROR_SEVERITY } from './ErrorHandler.js';

// مراحل التهيئة
export const INIT_PHASES = {
  STARTING: 'starting',
  CORE_SERVICES: 'core_services',
  DATABASE: 'database',
  AUTHENTICATION: 'authentication',
  UI_COMPONENTS: 'ui_components',
  REAL_TIME: 'real_time',
  FINALIZATION: 'finalization',
  COMPLETED: 'completed',
  FAILED: 'failed'
};

class AppInitializer {
  constructor() {
    // حالة التهيئة
    this.initializationState = {
      currentPhase: null,
      completedPhases: [],
      failedPhases: [],
      startTime: null,
      endTime: null,
      totalDuration: 0
    };

    // الخدمات المطلوب تهيئتها
    this.services = new Map();
    this.components = new Map();
    
    // إعدادات التهيئة
    this.config = {
      timeout: 30000, // 30 ثانية
      retryAttempts: 3,
      retryDelay: 2000,
      enableProgressTracking: true,
      enablePerformanceMetrics: true
    };

    // معلومات الأداء
    this.performanceMetrics = {
      phases: new Map(),
      services: new Map(),
      components: new Map()
    };
  }

  /**
   * تسجيل خدمة للتهيئة
   * @param {string} name - اسم الخدمة
   * @param {Function} initFunction - دالة التهيئة
   * @param {Object} options - خيارات التهيئة
   */
  registerService(name, initFunction, options = {}) {
    this.services.set(name, {
      name,
      initFunction,
      phase: options.phase || INIT_PHASES.CORE_SERVICES,
      dependencies: options.dependencies || [],
      priority: options.priority || 0,
      timeout: options.timeout || this.config.timeout,
      retryable: options.retryable !== false,
      critical: options.critical !== false,
      initialized: false,
      error: null
    });
  }

  /**
   * تسجيل مكون للتهيئة
   * @param {string} name - اسم المكون
   * @param {Function} initFunction - دالة التهيئة
   * @param {Object} options - خيارات التهيئة
   */
  registerComponent(name, initFunction, options = {}) {
    this.components.set(name, {
      name,
      initFunction,
      phase: options.phase || INIT_PHASES.UI_COMPONENTS,
      dependencies: options.dependencies || [],
      priority: options.priority || 0,
      timeout: options.timeout || this.config.timeout,
      retryable: options.retryable !== false,
      critical: options.critical !== false,
      initialized: false,
      error: null
    });
  }

  /**
   * بدء عملية التهيئة
   * @returns {Promise<boolean>} نجح التهيئة أم لا
   */
  async initialize() {
    try {
      console.log('🚀 بدء تهيئة التطبيق...');
      
      this.initializationState.startTime = performance.now();
      this.initializationState.currentPhase = INIT_PHASES.STARTING;
      
      // تحديث حالة التطبيق
      stateManager.setState('ui.isLoading', true);
      stateManager.setState('ui.loadingMessage', 'جاري تهيئة التطبيق...');
      
      // إطلاق حدث بداية التهيئة
      eventBus.emit('app:initialization:started');

      // تنفيذ مراحل التهيئة بالترتيب
      const phases = [
        INIT_PHASES.CORE_SERVICES,
        INIT_PHASES.DATABASE,
        INIT_PHASES.AUTHENTICATION,
        INIT_PHASES.UI_COMPONENTS,
        INIT_PHASES.REAL_TIME,
        INIT_PHASES.FINALIZATION
      ];

      for (const phase of phases) {
        const success = await this._executePhase(phase);
        
        if (!success) {
          this.initializationState.currentPhase = INIT_PHASES.FAILED;
          this.initializationState.failedPhases.push(phase);
          
          errorHandler.handleError(`فشل في مرحلة التهيئة: ${phase}`, {
            type: ERROR_TYPES.UNKNOWN,
            severity: ERROR_SEVERITY.CRITICAL,
            phase,
            initializationState: this.initializationState
          });
          
          return false;
        }
        
        this.initializationState.completedPhases.push(phase);
      }

      // إكمال التهيئة بنجاح
      this.initializationState.currentPhase = INIT_PHASES.COMPLETED;
      this.initializationState.endTime = performance.now();
      this.initializationState.totalDuration = 
        this.initializationState.endTime - this.initializationState.startTime;

      // تحديث حالة التطبيق
      stateManager.setState('ui.isLoading', false);
      stateManager.setState('ui.loadingMessage', '');
      
      // إطلاق حدث إكمال التهيئة
      eventBus.emit('app:initialization:completed', {
        duration: this.initializationState.totalDuration,
        metrics: this.performanceMetrics
      });

      console.log(`✅ تم تهيئة التطبيق بنجاح في ${this.initializationState.totalDuration.toFixed(2)} مللي ثانية`);
      
      return true;
    } catch (error) {
      this.initializationState.currentPhase = INIT_PHASES.FAILED;
      
      errorHandler.handleError(error, {
        type: ERROR_TYPES.UNKNOWN,
        severity: ERROR_SEVERITY.CRITICAL,
        context: 'app_initialization'
      });
      
      // تحديث حالة التطبيق
      stateManager.setState('ui.isLoading', false);
      stateManager.setState('ui.loadingMessage', 'فشل في تهيئة التطبيق');
      
      eventBus.emit('app:initialization:failed', { error });
      
      return false;
    }
  }

  /**
   * إعادة تهيئة التطبيق
   * @returns {Promise<boolean>} نجح إعادة التهيئة أم لا
   */
  async reinitialize() {
    console.log('🔄 إعادة تهيئة التطبيق...');
    
    // إعادة تعيين حالة التهيئة
    this._resetInitializationState();
    
    // إعادة تعيين حالة الخدمات والمكونات
    this.services.forEach(service => {
      service.initialized = false;
      service.error = null;
    });
    
    this.components.forEach(component => {
      component.initialized = false;
      component.error = null;
    });
    
    // بدء التهيئة من جديد
    return await this.initialize();
  }

  /**
   * الحصول على حالة التهيئة
   * @returns {Object} حالة التهيئة الحالية
   */
  getInitializationState() {
    return {
      ...this.initializationState,
      services: this._getServicesStatus(),
      components: this._getComponentsStatus(),
      metrics: this.performanceMetrics
    };
  }

  /**
   * تنفيذ مرحلة معينة من التهيئة
   * @private
   * @param {string} phase - اسم المرحلة
   * @returns {Promise<boolean>} نجح تنفيذ المرحلة أم لا
   */
  async _executePhase(phase) {
    try {
      console.log(`📋 تنفيذ مرحلة: ${phase}`);
      
      const phaseStartTime = performance.now();
      this.initializationState.currentPhase = phase;
      
      // تحديث رسالة التحميل
      stateManager.setState('ui.loadingMessage', this._getPhaseMessage(phase));
      
      // إطلاق حدث بداية المرحلة
      eventBus.emit('app:phase:started', { phase });

      // الحصول على العناصر المطلوب تهيئتها في هذه المرحلة
      const phaseServices = Array.from(this.services.values())
        .filter(service => service.phase === phase);
      
      const phaseComponents = Array.from(this.components.values())
        .filter(component => component.phase === phase);

      // ترتيب العناصر حسب الأولوية والتبعيات
      const sortedServices = this._sortByDependencies(phaseServices);
      const sortedComponents = this._sortByDependencies(phaseComponents);

      // تهيئة الخدمات أولاً
      for (const service of sortedServices) {
        const success = await this._initializeService(service);
        if (!success && service.critical) {
          return false;
        }
      }

      // ثم تهيئة المكونات
      for (const component of sortedComponents) {
        const success = await this._initializeComponent(component);
        if (!success && component.critical) {
          return false;
        }
      }

      // حساب وقت المرحلة
      const phaseEndTime = performance.now();
      const phaseDuration = phaseEndTime - phaseStartTime;
      
      this.performanceMetrics.phases.set(phase, {
        duration: phaseDuration,
        servicesCount: phaseServices.length,
        componentsCount: phaseComponents.length,
        timestamp: new Date()
      });

      // إطلاق حدث إكمال المرحلة
      eventBus.emit('app:phase:completed', { 
        phase, 
        duration: phaseDuration 
      });

      console.log(`✅ تم إكمال مرحلة ${phase} في ${phaseDuration.toFixed(2)} مللي ثانية`);
      
      return true;
    } catch (error) {
      console.error(`❌ فشل في مرحلة ${phase}:`, error);
      
      eventBus.emit('app:phase:failed', { phase, error });
      
      return false;
    }
  }

  /**
   * تهيئة خدمة معينة
   * @private
   * @param {Object} service - كائن الخدمة
   * @returns {Promise<boolean>} نجح التهيئة أم لا
   */
  async _initializeService(service) {
    if (service.initialized) {
      return true;
    }

    try {
      console.log(`🔧 تهيئة الخدمة: ${service.name}`);
      
      const serviceStartTime = performance.now();
      
      // التحقق من التبعيات
      const dependenciesReady = await this._checkDependencies(service.dependencies);
      if (!dependenciesReady) {
        throw new Error(`التبعيات غير جاهزة للخدمة: ${service.name}`);
      }

      // تنفيذ دالة التهيئة مع مهلة زمنية
      const result = await this._executeWithTimeout(
        service.initFunction,
        service.timeout,
        `تهيئة الخدمة ${service.name}`
      );

      // حساب وقت التهيئة
      const serviceEndTime = performance.now();
      const serviceDuration = serviceEndTime - serviceStartTime;
      
      this.performanceMetrics.services.set(service.name, {
        duration: serviceDuration,
        phase: service.phase,
        timestamp: new Date()
      });

      service.initialized = true;
      
      console.log(`✅ تم تهيئة الخدمة ${service.name} في ${serviceDuration.toFixed(2)} مللي ثانية`);
      
      eventBus.emit('service:initialized', { 
        name: service.name, 
        duration: serviceDuration,
        result 
      });
      
      return true;
    } catch (error) {
      console.error(`❌ فشل في تهيئة الخدمة ${service.name}:`, error);
      
      service.error = error;
      
      eventBus.emit('service:failed', { 
        name: service.name, 
        error 
      });

      // إعادة المحاولة إذا كانت مسموحة
      if (service.retryable && service.retryCount < this.config.retryAttempts) {
        service.retryCount = (service.retryCount || 0) + 1;
        
        console.log(`🔄 إعادة محاولة تهيئة الخدمة ${service.name} (المحاولة ${service.retryCount})`);
        
        await new Promise(resolve => setTimeout(resolve, this.config.retryDelay));
        
        return await this._initializeService(service);
      }
      
      return false;
    }
  }

  /**
   * تهيئة مكون معين
   * @private
   * @param {Object} component - كائن المكون
   * @returns {Promise<boolean>} نجح التهيئة أم لا
   */
  async _initializeComponent(component) {
    if (component.initialized) {
      return true;
    }

    try {
      console.log(`🎨 تهيئة المكون: ${component.name}`);
      
      const componentStartTime = performance.now();
      
      // التحقق من التبعيات
      const dependenciesReady = await this._checkDependencies(component.dependencies);
      if (!dependenciesReady) {
        throw new Error(`التبعيات غير جاهزة للمكون: ${component.name}`);
      }

      // تنفيذ دالة التهيئة مع مهلة زمنية
      const result = await this._executeWithTimeout(
        component.initFunction,
        component.timeout,
        `تهيئة المكون ${component.name}`
      );

      // حساب وقت التهيئة
      const componentEndTime = performance.now();
      const componentDuration = componentEndTime - componentStartTime;
      
      this.performanceMetrics.components.set(component.name, {
        duration: componentDuration,
        phase: component.phase,
        timestamp: new Date()
      });

      component.initialized = true;
      
      console.log(`✅ تم تهيئة المكون ${component.name} في ${componentDuration.toFixed(2)} مللي ثانية`);
      
      eventBus.emit('component:initialized', { 
        name: component.name, 
        duration: componentDuration,
        result 
      });
      
      return true;
    } catch (error) {
      console.error(`❌ فشل في تهيئة المكون ${component.name}:`, error);
      
      component.error = error;
      
      eventBus.emit('component:failed', { 
        name: component.name, 
        error 
      });

      // إعادة المحاولة إذا كانت مسموحة
      if (component.retryable && component.retryCount < this.config.retryAttempts) {
        component.retryCount = (component.retryCount || 0) + 1;
        
        console.log(`🔄 إعادة محاولة تهيئة المكون ${component.name} (المحاولة ${component.retryCount})`);
        
        await new Promise(resolve => setTimeout(resolve, this.config.retryDelay));
        
        return await this._initializeComponent(component);
      }
      
      return false;
    }
  }

  /**
   * تنفيذ دالة مع مهلة زمنية
   * @private
   */
  async _executeWithTimeout(func, timeout, description) {
    return new Promise(async (resolve, reject) => {
      const timeoutId = setTimeout(() => {
        reject(new Error(`انتهت المهلة الزمنية لـ ${description}`));
      }, timeout);

      try {
        const result = await func();
        clearTimeout(timeoutId);
        resolve(result);
      } catch (error) {
        clearTimeout(timeoutId);
        reject(error);
      }
    });
  }

  /**
   * التحقق من جاهزية التبعيات
   * @private
   */
  async _checkDependencies(dependencies) {
    if (!dependencies || dependencies.length === 0) {
      return true;
    }

    for (const dependency of dependencies) {
      const service = this.services.get(dependency);
      const component = this.components.get(dependency);
      
      if (service && !service.initialized) {
        return false;
      }
      
      if (component && !component.initialized) {
        return false;
      }
      
      if (!service && !component) {
        console.warn(`تبعية غير موجودة: ${dependency}`);
        return false;
      }
    }

    return true;
  }

  /**
   * ترتيب العناصر حسب التبعيات والأولوية
   * @private
   */
  _sortByDependencies(items) {
    // ترتيب بسيط حسب الأولوية أولاً
    return items.sort((a, b) => {
      // الأولوية الأعلى أولاً
      if (a.priority !== b.priority) {
        return b.priority - a.priority;
      }
      
      // العناصر بدون تبعيات أولاً
      if (a.dependencies.length !== b.dependencies.length) {
        return a.dependencies.length - b.dependencies.length;
      }
      
      return 0;
    });
  }

  /**
   * الحصول على رسالة المرحلة
   * @private
   */
  _getPhaseMessage(phase) {
    const messages = {
      [INIT_PHASES.CORE_SERVICES]: 'تهيئة الخدمات الأساسية...',
      [INIT_PHASES.DATABASE]: 'تهيئة قاعدة البيانات...',
      [INIT_PHASES.AUTHENTICATION]: 'التحقق من المصادقة...',
      [INIT_PHASES.UI_COMPONENTS]: 'تحميل مكونات الواجهة...',
      [INIT_PHASES.REAL_TIME]: 'تهيئة الاتصال المباشر...',
      [INIT_PHASES.FINALIZATION]: 'إنهاء التهيئة...'
    };

    return messages[phase] || 'جاري التحميل...';
  }

  /**
   * إعادة تعيين حالة التهيئة
   * @private
   */
  _resetInitializationState() {
    this.initializationState = {
      currentPhase: null,
      completedPhases: [],
      failedPhases: [],
      startTime: null,
      endTime: null,
      totalDuration: 0
    };

    this.performanceMetrics = {
      phases: new Map(),
      services: new Map(),
      components: new Map()
    };
  }

  /**
   * الحصول على حالة الخدمات
   * @private
   */
  _getServicesStatus() {
    const status = {};
    this.services.forEach((service, name) => {
      status[name] = {
        initialized: service.initialized,
        phase: service.phase,
        error: service.error?.message || null
      };
    });
    return status;
  }

  /**
   * الحصول على حالة المكونات
   * @private
   */
  _getComponentsStatus() {
    const status = {};
    this.components.forEach((component, name) => {
      status[name] = {
        initialized: component.initialized,
        phase: component.phase,
        error: component.error?.message || null
      };
    });
    return status;
  }
}

// إنشاء مثيل عام للاستخدام في التطبيق
const appInitializer = new AppInitializer();

// تصدير الكلاس والمثيل والثوابت
export { AppInitializer, appInitializer, INIT_PHASES };
export default appInitializer;
