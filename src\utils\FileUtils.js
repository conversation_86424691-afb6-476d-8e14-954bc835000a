/**
 * أدوات الملفات - FileUtils
 * يوفر دوال شاملة للتعامل مع الملفات والوسائط
 * يدعم الرفع والتحميل والمعاينة والضغط
 */

/**
 * قراءة ملف كـ Data URL
 * @param {File} file - الملف
 * @returns {Promise<string>} Data URL للملف
 */
export function readFileAsDataURL(file) {
  return new Promise((resolve, reject) => {
    if (!(file instanceof File)) {
      reject(new Error('الملف غير صحيح'));
      return;
    }
    
    const reader = new FileReader();
    
    reader.onload = (event) => {
      resolve(event.target.result);
    };
    
    reader.onerror = () => {
      reject(new Error('فشل في قراءة الملف'));
    };
    
    reader.readAsDataURL(file);
  });
}

/**
 * قراءة ملف كنص
 * @param {File} file - الملف
 * @param {string} encoding - ترميز النص (افتراضي: UTF-8)
 * @returns {Promise<string>} محتوى الملف كنص
 */
export function readFileAsText(file, encoding = 'UTF-8') {
  return new Promise((resolve, reject) => {
    if (!(file instanceof File)) {
      reject(new Error('الملف غير صحيح'));
      return;
    }
    
    const reader = new FileReader();
    
    reader.onload = (event) => {
      resolve(event.target.result);
    };
    
    reader.onerror = () => {
      reject(new Error('فشل في قراءة الملف'));
    };
    
    reader.readAsText(file, encoding);
  });
}

/**
 * قراءة ملف كـ ArrayBuffer
 * @param {File} file - الملف
 * @returns {Promise<ArrayBuffer>} محتوى الملف كـ ArrayBuffer
 */
export function readFileAsArrayBuffer(file) {
  return new Promise((resolve, reject) => {
    if (!(file instanceof File)) {
      reject(new Error('الملف غير صحيح'));
      return;
    }
    
    const reader = new FileReader();
    
    reader.onload = (event) => {
      resolve(event.target.result);
    };
    
    reader.onerror = () => {
      reject(new Error('فشل في قراءة الملف'));
    };
    
    reader.readAsArrayBuffer(file);
  });
}

/**
 * ضغط صورة
 * @param {File} file - ملف الصورة
 * @param {Object} options - خيارات الضغط
 * @returns {Promise<File>} الصورة المضغوطة
 */
export function compressImage(file, options = {}) {
  return new Promise((resolve, reject) => {
    if (!file.type.startsWith('image/')) {
      reject(new Error('الملف ليس صورة'));
      return;
    }
    
    const defaultOptions = {
      maxWidth: 1920,
      maxHeight: 1080,
      quality: 0.8,
      format: 'image/jpeg'
    };
    
    const opts = { ...defaultOptions, ...options };
    
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();
    
    img.onload = () => {
      // حساب الأبعاد الجديدة
      let { width, height } = img;
      
      if (width > opts.maxWidth) {
        height = (height * opts.maxWidth) / width;
        width = opts.maxWidth;
      }
      
      if (height > opts.maxHeight) {
        width = (width * opts.maxHeight) / height;
        height = opts.maxHeight;
      }
      
      // تعيين أبعاد الكانفاس
      canvas.width = width;
      canvas.height = height;
      
      // رسم الصورة
      ctx.drawImage(img, 0, 0, width, height);
      
      // تحويل إلى Blob
      canvas.toBlob(
        (blob) => {
          if (blob) {
            const compressedFile = new File([blob], file.name, {
              type: opts.format,
              lastModified: Date.now()
            });
            resolve(compressedFile);
          } else {
            reject(new Error('فشل في ضغط الصورة'));
          }
        },
        opts.format,
        opts.quality
      );
    };
    
    img.onerror = () => {
      reject(new Error('فشل في تحميل الصورة'));
    };
    
    readFileAsDataURL(file)
      .then(dataURL => {
        img.src = dataURL;
      })
      .catch(reject);
  });
}

/**
 * إنشاء صورة مصغرة
 * @param {File} file - ملف الصورة أو الفيديو
 * @param {Object} options - خيارات الصورة المصغرة
 * @returns {Promise<string>} Data URL للصورة المصغرة
 */
export function createThumbnail(file, options = {}) {
  return new Promise((resolve, reject) => {
    const defaultOptions = {
      width: 200,
      height: 200,
      quality: 0.8,
      format: 'image/jpeg'
    };
    
    const opts = { ...defaultOptions, ...options };
    
    if (file.type.startsWith('image/')) {
      createImageThumbnail(file, opts).then(resolve).catch(reject);
    } else if (file.type.startsWith('video/')) {
      createVideoThumbnail(file, opts).then(resolve).catch(reject);
    } else {
      reject(new Error('نوع الملف غير مدعوم للصورة المصغرة'));
    }
  });
}

/**
 * إنشاء صورة مصغرة للصورة
 * @private
 */
function createImageThumbnail(file, options) {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();
    
    img.onload = () => {
      // حساب الأبعاد مع الحفاظ على النسبة
      const { width, height } = calculateThumbnailSize(
        img.width,
        img.height,
        options.width,
        options.height
      );
      
      canvas.width = width;
      canvas.height = height;
      
      // رسم الصورة
      ctx.drawImage(img, 0, 0, width, height);
      
      // تحويل إلى Data URL
      const dataURL = canvas.toDataURL(options.format, options.quality);
      resolve(dataURL);
    };
    
    img.onerror = () => {
      reject(new Error('فشل في تحميل الصورة'));
    };
    
    readFileAsDataURL(file)
      .then(dataURL => {
        img.src = dataURL;
      })
      .catch(reject);
  });
}

/**
 * إنشاء صورة مصغرة للفيديو
 * @private
 */
function createVideoThumbnail(file, options) {
  return new Promise((resolve, reject) => {
    const video = document.createElement('video');
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    video.onloadedmetadata = () => {
      // الانتقال إلى ثانية واحدة من الفيديو
      video.currentTime = 1;
    };
    
    video.onseeked = () => {
      // حساب الأبعاد
      const { width, height } = calculateThumbnailSize(
        video.videoWidth,
        video.videoHeight,
        options.width,
        options.height
      );
      
      canvas.width = width;
      canvas.height = height;
      
      // رسم الإطار
      ctx.drawImage(video, 0, 0, width, height);
      
      // تحويل إلى Data URL
      const dataURL = canvas.toDataURL(options.format, options.quality);
      resolve(dataURL);
    };
    
    video.onerror = () => {
      reject(new Error('فشل في تحميل الفيديو'));
    };
    
    readFileAsDataURL(file)
      .then(dataURL => {
        video.src = dataURL;
      })
      .catch(reject);
  });
}

/**
 * حساب أبعاد الصورة المصغرة مع الحفاظ على النسبة
 * @private
 */
function calculateThumbnailSize(originalWidth, originalHeight, maxWidth, maxHeight) {
  let width = originalWidth;
  let height = originalHeight;
  
  // حساب النسبة
  const aspectRatio = originalWidth / originalHeight;
  
  if (width > maxWidth) {
    width = maxWidth;
    height = width / aspectRatio;
  }
  
  if (height > maxHeight) {
    height = maxHeight;
    width = height * aspectRatio;
  }
  
  return { width: Math.round(width), height: Math.round(height) };
}

/**
 * تحويل Data URL إلى File
 * @param {string} dataURL - Data URL
 * @param {string} filename - اسم الملف
 * @returns {File} كائن الملف
 */
export function dataURLToFile(dataURL, filename) {
  const arr = dataURL.split(',');
  const mime = arr[0].match(/:(.*?);/)[1];
  const bstr = atob(arr[1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);
  
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  
  return new File([u8arr], filename, { type: mime });
}

/**
 * تحويل Blob إلى File
 * @param {Blob} blob - كائن Blob
 * @param {string} filename - اسم الملف
 * @returns {File} كائن الملف
 */
export function blobToFile(blob, filename) {
  return new File([blob], filename, {
    type: blob.type,
    lastModified: Date.now()
  });
}

/**
 * تحميل ملف من URL
 * @param {string} url - رابط الملف
 * @param {string} filename - اسم الملف (اختياري)
 * @returns {Promise<void>} وعد بإكمال التحميل
 */
export function downloadFile(url, filename = null) {
  return new Promise((resolve, reject) => {
    try {
      const link = document.createElement('a');
      link.href = url;
      
      if (filename) {
        link.download = filename;
      }
      
      // إضافة الرابط إلى DOM مؤقتاً
      document.body.appendChild(link);
      
      // تشغيل التحميل
      link.click();
      
      // إزالة الرابط
      document.body.removeChild(link);
      
      resolve();
    } catch (error) {
      reject(new Error('فشل في تحميل الملف'));
    }
  });
}

/**
 * تحميل ملف من Blob
 * @param {Blob} blob - كائن Blob
 * @param {string} filename - اسم الملف
 */
export function downloadBlob(blob, filename) {
  const url = URL.createObjectURL(blob);
  
  downloadFile(url, filename)
    .then(() => {
      // تنظيف URL
      URL.revokeObjectURL(url);
    })
    .catch((error) => {
      URL.revokeObjectURL(url);
      throw error;
    });
}

/**
 * الحصول على معلومات الملف
 * @param {File} file - الملف
 * @returns {Object} معلومات الملف
 */
export function getFileInfo(file) {
  if (!(file instanceof File)) {
    return null;
  }
  
  return {
    name: file.name,
    size: file.size,
    type: file.type,
    lastModified: file.lastModified,
    lastModifiedDate: new Date(file.lastModified),
    extension: getFileExtension(file.name),
    isImage: file.type.startsWith('image/'),
    isVideo: file.type.startsWith('video/'),
    isAudio: file.type.startsWith('audio/'),
    isDocument: isDocumentFile(file.type),
    formattedSize: formatFileSize(file.size)
  };
}

/**
 * التحقق من نوع الملف
 * @param {File} file - الملف
 * @param {Array} allowedTypes - الأنواع المسموحة
 * @returns {boolean} هل النوع مسموح
 */
export function isFileTypeAllowed(file, allowedTypes) {
  if (!(file instanceof File) || !Array.isArray(allowedTypes)) {
    return false;
  }
  
  return allowedTypes.some(type => {
    if (type.endsWith('/*')) {
      // نوع عام مثل image/*
      const category = type.slice(0, -2);
      return file.type.startsWith(category + '/');
    } else {
      // نوع محدد
      return file.type === type;
    }
  });
}

/**
 * تجميع ملفات متعددة في ZIP (يتطلب مكتبة خارجية)
 * @param {Array} files - مصفوفة الملفات
 * @param {string} zipName - اسم ملف ZIP
 * @returns {Promise<Blob>} ملف ZIP
 */
export async function createZipFile(files, zipName = 'files.zip') {
  // هذه الدالة تتطلب مكتبة JSZip
  // يمكن تنفيذها عند الحاجة
  throw new Error('تجميع الملفات في ZIP يتطلب مكتبة JSZip');
}

/**
 * استخراج ملفات من ZIP (يتطلب مكتبة خارجية)
 * @param {File} zipFile - ملف ZIP
 * @returns {Promise<Array>} مصفوفة الملفات المستخرجة
 */
export async function extractZipFile(zipFile) {
  // هذه الدالة تتطلب مكتبة JSZip
  // يمكن تنفيذها عند الحاجة
  throw new Error('استخراج ملفات ZIP يتطلب مكتبة JSZip');
}

/**
 * تحويل ملف إلى Base64
 * @param {File} file - الملف
 * @returns {Promise<string>} النص المُرمز بـ Base64
 */
export function fileToBase64(file) {
  return new Promise((resolve, reject) => {
    readFileAsDataURL(file)
      .then(dataURL => {
        // إزالة البادئة data:type;base64,
        const base64 = dataURL.split(',')[1];
        resolve(base64);
      })
      .catch(reject);
  });
}

/**
 * تحويل Base64 إلى Blob
 * @param {string} base64 - النص المُرمز
 * @param {string} mimeType - نوع الملف
 * @returns {Blob} كائن Blob
 */
export function base64ToBlob(base64, mimeType) {
  const byteCharacters = atob(base64);
  const byteNumbers = new Array(byteCharacters.length);
  
  for (let i = 0; i < byteCharacters.length; i++) {
    byteNumbers[i] = byteCharacters.charCodeAt(i);
  }
  
  const byteArray = new Uint8Array(byteNumbers);
  return new Blob([byteArray], { type: mimeType });
}

/**
 * مراقبة تقدم رفع الملف
 * @param {XMLHttpRequest} xhr - كائن XMLHttpRequest
 * @param {Function} onProgress - دالة مراقبة التقدم
 */
export function trackUploadProgress(xhr, onProgress) {
  if (xhr.upload && typeof onProgress === 'function') {
    xhr.upload.addEventListener('progress', (event) => {
      if (event.lengthComputable) {
        const percentComplete = (event.loaded / event.total) * 100;
        onProgress({
          loaded: event.loaded,
          total: event.total,
          percentage: Math.round(percentComplete)
        });
      }
    });
  }
}

/**
 * إنشاء معاينة للملف
 * @param {File} file - الملف
 * @param {Object} options - خيارات المعاينة
 * @returns {Promise<string>} HTML للمعاينة
 */
export async function createFilePreview(file, options = {}) {
  const defaultOptions = {
    showThumbnail: true,
    thumbnailSize: 100,
    showFileInfo: true
  };
  
  const opts = { ...defaultOptions, ...options };
  const fileInfo = getFileInfo(file);
  
  let previewHTML = '<div class="file-preview">';
  
  // إضافة الصورة المصغرة
  if (opts.showThumbnail && (fileInfo.isImage || fileInfo.isVideo)) {
    try {
      const thumbnail = await createThumbnail(file, {
        width: opts.thumbnailSize,
        height: opts.thumbnailSize
      });
      previewHTML += `<img src="${thumbnail}" alt="معاينة" class="file-thumbnail">`;
    } catch (error) {
      previewHTML += '<div class="file-icon">📄</div>';
    }
  } else {
    // أيقونة حسب نوع الملف
    const icon = getFileIcon(fileInfo.type);
    previewHTML += `<div class="file-icon">${icon}</div>`;
  }
  
  // إضافة معلومات الملف
  if (opts.showFileInfo) {
    previewHTML += `
      <div class="file-info">
        <div class="file-name">${fileInfo.name}</div>
        <div class="file-size">${fileInfo.formattedSize}</div>
        <div class="file-type">${fileInfo.type}</div>
      </div>
    `;
  }
  
  previewHTML += '</div>';
  
  return previewHTML;
}

// دوال مساعدة

/**
 * الحصول على امتداد الملف
 * @param {string} filename - اسم الملف
 * @returns {string} امتداد الملف
 */
function getFileExtension(filename) {
  if (typeof filename !== 'string') return '';
  
  const lastDot = filename.lastIndexOf('.');
  return lastDot === -1 ? '' : filename.substring(lastDot + 1).toLowerCase();
}

/**
 * تنسيق حجم الملف
 * @param {number} bytes - الحجم بالبايت
 * @returns {string} الحجم المنسق
 */
function formatFileSize(bytes) {
  if (bytes === 0) return '0 بايت';
  
  const k = 1024;
  const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت', 'تيرابايت'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * التحقق من كون الملف مستند
 * @param {string} mimeType - نوع الملف
 * @returns {boolean} هل هو مستند
 */
function isDocumentFile(mimeType) {
  const documentTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'text/plain',
    'text/rtf'
  ];
  
  return documentTypes.includes(mimeType);
}

/**
 * الحصول على أيقونة الملف
 * @param {string} mimeType - نوع الملف
 * @returns {string} أيقونة الملف
 */
function getFileIcon(mimeType) {
  if (mimeType.startsWith('image/')) return '🖼️';
  if (mimeType.startsWith('video/')) return '🎥';
  if (mimeType.startsWith('audio/')) return '🎵';
  if (mimeType === 'application/pdf') return '📄';
  if (mimeType.includes('word')) return '📝';
  if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) return '📊';
  if (mimeType.includes('powerpoint') || mimeType.includes('presentation')) return '📽️';
  if (mimeType.startsWith('text/')) return '📃';
  if (mimeType.includes('zip') || mimeType.includes('archive')) return '📦';
  
  return '📄'; // أيقونة افتراضية
}

// تصدير جميع الدوال
export default {
  readFileAsDataURL,
  readFileAsText,
  readFileAsArrayBuffer,
  compressImage,
  createThumbnail,
  dataURLToFile,
  blobToFile,
  downloadFile,
  downloadBlob,
  getFileInfo,
  isFileTypeAllowed,
  createZipFile,
  extractZipFile,
  fileToBase64,
  base64ToBlob,
  trackUploadProgress,
  createFilePreview
};
