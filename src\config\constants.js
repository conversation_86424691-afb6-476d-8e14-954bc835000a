/**
 * الثوابت العامة للتطبيق - Constants
 * يحتوي على جميع الثوابت المستخدمة في التطبيق
 * منظم حسب الفئات لسهولة الوصول والصيانة
 */

// معلومات التطبيق
export const APP_INFO = {
  NAME: 'تطبيق المحادثات المطور',
  VERSION: '2.0.0',
  BUILD: '20240101',
  AUTHOR: 'فريق التطوير',
  DESCRIPTION: 'تطبيق محادثات متقدم مع ميزات حديثة',
  WEBSITE: 'https://chatapp.example.com',
  SUPPORT_EMAIL: '<EMAIL>'
};

// إعدادات قاعدة البيانات
export const DATABASE = {
  NAME: 'ChatAppCloneDB',
  VERSION: 2,
  MAX_SIZE: 50 * 1024 * 1024, // 50 ميجابايت
  TIMEOUT: 30000, // 30 ثانية
  
  STORES: {
    CURRENT_USER: 'currentUser',
    CHATS: 'chats',
    MESSAGES: 'messages',
    CONTACTS: 'contacts',
    USERS: 'users',
    TEAMS: 'teams',
    PAGE_DATA: 'pageData'
  }
};

// إعدادات API
export const API = {
  TIMEOUT: 30000, // 30 ثانية
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000, // 1 ثانية
  MAX_CONCURRENT_REQUESTS: 10,
  
  HEADERS: {
    CONTENT_TYPE: 'application/json; charset=utf-8',
    ACCEPT: 'application/json',
    USER_AGENT: `${APP_INFO.NAME}/${APP_INFO.VERSION}`
  }
};

// إعدادات SignalR
export const SIGNALR = {
  RECONNECT_ATTEMPTS: 5,
  RECONNECT_DELAY: 2000, // 2 ثانية
  PING_INTERVAL: 30000, // 30 ثانية
  TIMEOUT: 60000, // 60 ثانية
  
  EVENTS: {
    USER_CHAT_ONLINE: 'userChatOnline',
    CHAT_UPDATE: 'chatUpdate',
    GROUP_EVENTS: 'groupEvents',
    NEW_MESSAGE: 'newMessage',
    MESSAGE_STATUS: 'messageStatus',
    MESSAGE_UPDATE_OR_DELETE: 'messageUpdateOrDelete',
    POLL_VOTE: 'pollVote',
    BLOCKING_EVENT: 'blockingEvent',
    USER_INFO: 'userInfo'
  }
};

// إعدادات الملفات والوسائط
export const MEDIA = {
  // أحجام الملفات (بالبايت)
  MAX_FILE_SIZE: 50 * 1024 * 1024, // 50 ميجابايت
  MAX_IMAGE_SIZE: 10 * 1024 * 1024, // 10 ميجابايت
  MAX_VIDEO_SIZE: 100 * 1024 * 1024, // 100 ميجابايت
  MAX_AUDIO_SIZE: 20 * 1024 * 1024, // 20 ميجابايت
  
  // أنواع الملفات المدعومة
  SUPPORTED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  SUPPORTED_VIDEO_TYPES: ['video/mp4', 'video/webm', 'video/ogg'],
  SUPPORTED_AUDIO_TYPES: ['audio/mp3', 'audio/wav', 'audio/ogg', 'audio/m4a'],
  SUPPORTED_DOCUMENT_TYPES: [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain'
  ],
  
  // إعدادات الضغط
  IMAGE_COMPRESSION: {
    MAX_WIDTH: 1920,
    MAX_HEIGHT: 1080,
    QUALITY: 0.8,
    FORMAT: 'image/jpeg'
  },
  
  // إعدادات الصور المصغرة
  THUMBNAIL: {
    SIZE: 200,
    QUALITY: 0.7,
    FORMAT: 'image/jpeg'
  },
  
  // إعدادات التسجيل الصوتي
  VOICE_RECORDING: {
    MAX_DURATION: 5 * 60 * 1000, // 5 دقائق
    SAMPLE_RATE: 44100,
    BIT_RATE: 128000,
    FORMAT: 'audio/webm'
  }
};

// إعدادات الرسائل
export const MESSAGES = {
  MAX_TEXT_LENGTH: 4000,
  MAX_CAPTION_LENGTH: 200,
  TYPING_TIMEOUT: 3000, // 3 ثواني
  READ_RECEIPT_DELAY: 1000, // 1 ثانية
  
  // أولويات الرسائل
  PRIORITY: {
    LOW: 'low',
    NORMAL: 'normal',
    HIGH: 'high',
    URGENT: 'urgent'
  },
  
  // حالات التسليم
  DELIVERY_STATUS: {
    SENDING: 'sending',
    SENT: 'sent',
    DELIVERED: 'delivered',
    READ: 'read',
    FAILED: 'failed'
  }
};

// إعدادات المحادثات
export const CHATS = {
  MAX_NAME_LENGTH: 100,
  MAX_DESCRIPTION_LENGTH: 500,
  MAX_MEMBERS: 1000,
  
  // أنواع المحادثات
  TYPES: {
    PRIVATE: 'Private',
    GROUP: 'Group',
    TEAM: 'Team',
    CHANNEL: 'Channel'
  },
  
  // أدوار الأعضاء
  MEMBER_ROLES: {
    OWNER: 'Owner',
    ADMIN: 'Admin',
    MODERATOR: 'Moderator',
    MEMBER: 'Member'
  }
};

// إعدادات المستخدمين
export const USERS = {
  MIN_USERNAME_LENGTH: 3,
  MAX_USERNAME_LENGTH: 50,
  MAX_BIO_LENGTH: 500,
  MAX_STATUS_MESSAGE_LENGTH: 100,
  
  // حالات المستخدم
  STATUS: {
    ONLINE: 'Online',
    OFFLINE: 'Offline',
    AWAY: 'Away',
    BUSY: 'Busy',
    INVISIBLE: 'Invisible'
  },
  
  // أنواع المستخدمين
  TYPES: {
    REGULAR: 'Regular',
    ADMIN: 'Admin',
    MODERATOR: 'Moderator',
    BOT: 'Bot',
    SYSTEM: 'System'
  }
};

// إعدادات الأمان
export const SECURITY = {
  // كلمات المرور
  PASSWORD: {
    MIN_LENGTH: 8,
    MAX_LENGTH: 128,
    REQUIRE_UPPERCASE: true,
    REQUIRE_LOWERCASE: true,
    REQUIRE_NUMBERS: true,
    REQUIRE_SPECIAL_CHARS: true
  },
  
  // الجلسات
  SESSION: {
    TIMEOUT: 24 * 60 * 60 * 1000, // 24 ساعة
    REFRESH_THRESHOLD: 5 * 60 * 1000, // 5 دقائق
    MAX_CONCURRENT_SESSIONS: 5
  },
  
  // محاولات تسجيل الدخول
  LOGIN: {
    MAX_ATTEMPTS: 5,
    LOCKOUT_DURATION: 30 * 60 * 1000, // 30 دقيقة
    RESET_ATTEMPTS_AFTER: 24 * 60 * 60 * 1000 // 24 ساعة
  }
};

// إعدادات واجهة المستخدم
export const UI = {
  // الثيمات
  THEMES: {
    LIGHT: 'light',
    DARK: 'dark',
    AUTO: 'auto'
  },
  
  // أحجام الخط
  FONT_SIZES: {
    SMALL: 'small',
    MEDIUM: 'medium',
    LARGE: 'large',
    EXTRA_LARGE: 'extra-large'
  },
  
  // اللغات المدعومة
  LANGUAGES: {
    ARABIC: 'ar',
    ENGLISH: 'en'
  },
  
  // إعدادات التمرير
  SCROLL: {
    THRESHOLD: 100, // بكسل
    SMOOTH_DURATION: 300, // مللي ثانية
    INFINITE_SCROLL_THRESHOLD: 200 // بكسل
  },
  
  // إعدادات الرسوم المتحركة
  ANIMATION: {
    DURATION: 300, // مللي ثانية
    EASING: 'ease-in-out',
    FADE_DURATION: 200,
    SLIDE_DURATION: 250
  }
};

// إعدادات الإشعارات
export const NOTIFICATIONS = {
  // أنواع الإشعارات
  TYPES: {
    MESSAGE: 'message',
    CALL: 'call',
    GROUP_INVITE: 'group_invite',
    SYSTEM: 'system',
    UPDATE: 'update'
  },
  
  // مستويات الأولوية
  PRIORITY: {
    LOW: 'low',
    NORMAL: 'normal',
    HIGH: 'high',
    URGENT: 'urgent'
  },
  
  // إعدادات العرض
  DISPLAY: {
    AUTO_CLOSE_DELAY: 5000, // 5 ثواني
    MAX_VISIBLE: 5,
    POSITION: 'top-right'
  }
};

// إعدادات التخزين المؤقت
export const CACHE = {
  // مدد انتهاء الصلاحية (بالمللي ثانية)
  TTL: {
    SHORT: 5 * 60 * 1000, // 5 دقائق
    MEDIUM: 30 * 60 * 1000, // 30 دقيقة
    LONG: 2 * 60 * 60 * 1000, // 2 ساعة
    VERY_LONG: 24 * 60 * 60 * 1000 // 24 ساعة
  },
  
  // أحجام التخزين المؤقت
  MAX_SIZE: {
    SMALL: 50,
    MEDIUM: 100,
    LARGE: 500,
    EXTRA_LARGE: 1000
  }
};

// إعدادات الأداء
export const PERFORMANCE = {
  // حدود التحميل
  PAGINATION: {
    DEFAULT_PAGE_SIZE: 20,
    MAX_PAGE_SIZE: 100,
    MIN_PAGE_SIZE: 5
  },
  
  // إعدادات التحميل الكسول
  LAZY_LOADING: {
    THRESHOLD: 200, // بكسل
    ROOT_MARGIN: '50px'
  },
  
  // إعدادات التمرير الافتراضي
  VIRTUAL_SCROLL: {
    ITEM_HEIGHT: 60, // بكسل
    BUFFER_SIZE: 10,
    OVERSCAN: 5
  }
};

// إعدادات الشبكة
export const NETWORK = {
  // حالات الاتصال
  CONNECTION_STATUS: {
    ONLINE: 'online',
    OFFLINE: 'offline',
    SLOW: 'slow',
    UNSTABLE: 'unstable'
  },
  
  // إعدادات إعادة المحاولة
  RETRY: {
    MAX_ATTEMPTS: 3,
    INITIAL_DELAY: 1000, // 1 ثانية
    MAX_DELAY: 10000, // 10 ثواني
    BACKOFF_FACTOR: 2
  }
};

// إعدادات التطوير والتشخيص
export const DEBUG = {
  // مستويات السجل
  LOG_LEVELS: {
    ERROR: 'error',
    WARN: 'warn',
    INFO: 'info',
    DEBUG: 'debug',
    TRACE: 'trace'
  },
  
  // إعدادات الأداء
  PERFORMANCE_MONITORING: {
    ENABLED: true,
    SAMPLE_RATE: 0.1, // 10%
    MAX_ENTRIES: 1000
  }
};

// أكواد الأخطاء
export const ERROR_CODES = {
  // أخطاء عامة
  UNKNOWN: 'UNKNOWN_ERROR',
  NETWORK: 'NETWORK_ERROR',
  TIMEOUT: 'TIMEOUT_ERROR',
  
  // أخطاء المصادقة
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  
  // أخطاء التحقق
  VALIDATION_FAILED: 'VALIDATION_FAILED',
  INVALID_INPUT: 'INVALID_INPUT',
  MISSING_REQUIRED: 'MISSING_REQUIRED_FIELD',
  
  // أخطاء الموارد
  NOT_FOUND: 'RESOURCE_NOT_FOUND',
  ALREADY_EXISTS: 'RESOURCE_ALREADY_EXISTS',
  CONFLICT: 'RESOURCE_CONFLICT',
  
  // أخطاء قاعدة البيانات
  DATABASE_ERROR: 'DATABASE_ERROR',
  STORAGE_FULL: 'STORAGE_FULL',
  SYNC_FAILED: 'SYNC_FAILED'
};

// أحداث النظام
export const SYSTEM_EVENTS = {
  // أحداث التطبيق
  APP_STARTED: 'app:started',
  APP_STOPPED: 'app:stopped',
  APP_ERROR: 'app:error',
  
  // أحداث المصادقة
  USER_LOGGED_IN: 'user:logged_in',
  USER_LOGGED_OUT: 'user:logged_out',
  SESSION_EXPIRED: 'session:expired',
  
  // أحداث المحادثات
  CHAT_CREATED: 'chat:created',
  CHAT_UPDATED: 'chat:updated',
  CHAT_DELETED: 'chat:deleted',
  
  // أحداث الرسائل
  MESSAGE_SENT: 'message:sent',
  MESSAGE_RECEIVED: 'message:received',
  MESSAGE_READ: 'message:read',
  
  // أحداث الاتصال
  CONNECTION_ESTABLISHED: 'connection:established',
  CONNECTION_LOST: 'connection:lost',
  CONNECTION_RESTORED: 'connection:restored'
};

// مفاتيح التخزين المحلي
export const STORAGE_KEYS = {
  USER_PREFERENCES: 'chatapp_user_preferences',
  THEME: 'chatapp_theme',
  LANGUAGE: 'chatapp_language',
  CACHE_DATA: 'chatapp_cache_data',
  SESSION_DATA: 'chatapp_session_data',
  CUSTOM_CONFIG: 'chatapp_custom_config'
};

// أنماط التعبيرات النمطية
export const REGEX_PATTERNS = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE: /^(\+?966|0)?[5][0-9]{8}$/,
  USERNAME: /^[a-zA-Z0-9_-]{3,50}$/,
  URL: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/,
  HASHTAG: /#[a-zA-Z0-9_\u0600-\u06FF]+/g,
  MENTION: /@[a-zA-Z0-9_\u0600-\u06FF]+/g
};

// رموز الحالة HTTP
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  INTERNAL_SERVER_ERROR: 500,
  SERVICE_UNAVAILABLE: 503
};

// أولويات الأحداث
export const EVENT_PRIORITIES = {
  IMMEDIATE: 0,
  HIGH: 1,
  NORMAL: 2,
  LOW: 3,
  BACKGROUND: 4
};

// إعدادات الوقت
export const TIME = {
  SECOND: 1000,
  MINUTE: 60 * 1000,
  HOUR: 60 * 60 * 1000,
  DAY: 24 * 60 * 60 * 1000,
  WEEK: 7 * 24 * 60 * 60 * 1000,
  MONTH: 30 * 24 * 60 * 60 * 1000,
  YEAR: 365 * 24 * 60 * 60 * 1000
};

// تصدير جميع الثوابت كافتراضي
export default {
  APP_INFO,
  DATABASE,
  API,
  SIGNALR,
  MEDIA,
  MESSAGES,
  CHATS,
  USERS,
  SECURITY,
  UI,
  NOTIFICATIONS,
  CACHE,
  PERFORMANCE,
  NETWORK,
  DEBUG,
  ERROR_CODES,
  SYSTEM_EVENTS,
  STORAGE_KEYS,
  REGEX_PATTERNS,
  HTTP_STATUS,
  EVENT_PRIORITIES,
  TIME
};
