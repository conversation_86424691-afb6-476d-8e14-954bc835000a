/**
 * نموذج استجابة API - ApiResponseModel
 * يمثل بنية موحدة لاستجابات API مع معالجة الأخطاء والبيانات
 * يوفر دوال مساعدة للتحقق من حالة الاستجابة ومعالجة البيانات
 */

import { parseDate } from '../utils/DateUtils.js';

// حالات الاستجابة
export const RESPONSE_STATUS = {
  SUCCESS: 'success',
  ERROR: 'error',
  WARNING: 'warning',
  INFO: 'info'
};

// أكواد الأخطاء الشائعة
export const ERROR_CODES = {
  // أخطاء المصادقة
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
  
  // أخطاء التحقق من صحة البيانات
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  MISSING_REQUIRED_FIELD: 'MISSING_REQUIRED_FIELD',
  INVALID_FORMAT: 'INVALID_FORMAT',
  
  // أخطاء الموارد
  NOT_FOUND: 'NOT_FOUND',
  ALREADY_EXISTS: 'ALREADY_EXISTS',
  RESOURCE_CONFLICT: 'RESOURCE_CONFLICT',
  
  // أخطاء الخادم
  INTERNAL_SERVER_ERROR: 'INTERNAL_SERVER_ERROR',
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',
  DATABASE_ERROR: 'DATABASE_ERROR',
  
  // أخطاء الشبكة
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT: 'TIMEOUT',
  CONNECTION_FAILED: 'CONNECTION_FAILED',
  
  // أخطاء الأعمال
  BUSINESS_RULE_VIOLATION: 'BUSINESS_RULE_VIOLATION',
  INSUFFICIENT_PERMISSIONS: 'INSUFFICIENT_PERMISSIONS',
  QUOTA_EXCEEDED: 'QUOTA_EXCEEDED',
  
  // أخطاء عامة
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
  INVALID_REQUEST: 'INVALID_REQUEST'
};

/**
 * كلاس نموذج استجابة API
 */
export class ApiResponseModel {
  constructor(data = {}) {
    // الخصائص الأساسية
    this.status = data.status || RESPONSE_STATUS.SUCCESS;
    this.message = data.message || '';
    this.data = data.data || null;
    this.errors = data.errors || [];
    
    // معلومات الخطأ
    this.errorCode = data.errorCode || null;
    this.errorDetails = data.errorDetails || null;
    this.stackTrace = data.stackTrace || null;
    
    // معلومات الاستجابة
    this.statusCode = data.statusCode || 200;
    this.timestamp = data.timestamp ? parseDate(data.timestamp) : new Date();
    this.requestId = data.requestId || null;
    this.correlationId = data.correlationId || null;
    
    // معلومات التصفح والترقيم
    this.pagination = data.pagination ? {
      page: data.pagination.page || 1,
      pageSize: data.pagination.pageSize || 10,
      totalItems: data.pagination.totalItems || 0,
      totalPages: data.pagination.totalPages || 0,
      hasNext: data.pagination.hasNext || false,
      hasPrevious: data.pagination.hasPrevious || false,
      ...data.pagination
    } : null;
    
    // معلومات إضافية
    this.metadata = data.metadata || {};
    this.warnings = data.warnings || [];
    this.info = data.info || [];
    
    // معلومات الأداء
    this.performance = data.performance ? {
      executionTime: data.performance.executionTime || 0,
      queryTime: data.performance.queryTime || 0,
      cacheHit: data.performance.cacheHit || false,
      ...data.performance
    } : null;
    
    // معلومات الإصدار
    this.version = data.version || '1.0';
    this.apiVersion = data.apiVersion || 'v1';
    
    // معلومات الأمان
    this.security = data.security ? {
      encrypted: data.security.encrypted || false,
      signed: data.security.signed || false,
      checksum: data.security.checksum || null,
      ...data.security
    } : null;
  }

  /**
   * التحقق من نجاح الاستجابة
   * @returns {boolean} هل الاستجابة ناجحة
   */
  isSuccess() {
    return this.status === RESPONSE_STATUS.SUCCESS && this.statusCode >= 200 && this.statusCode < 300;
  }

  /**
   * التحقق من وجود خطأ في الاستجابة
   * @returns {boolean} هل توجد أخطاء
   */
  isError() {
    return this.status === RESPONSE_STATUS.ERROR || this.statusCode >= 400;
  }

  /**
   * التحقق من وجود تحذيرات
   * @returns {boolean} هل توجد تحذيرات
   */
  hasWarnings() {
    return this.warnings.length > 0 || this.status === RESPONSE_STATUS.WARNING;
  }

  /**
   * التحقق من وجود معلومات إضافية
   * @returns {boolean} هل توجد معلومات
   */
  hasInfo() {
    return this.info.length > 0 || this.status === RESPONSE_STATUS.INFO;
  }

  /**
   * الحصول على الرسالة الرئيسية
   * @returns {string} الرسالة
   */
  getMessage() {
    if (this.message) {
      return this.message;
    }
    
    // رسائل افتراضية حسب الحالة
    switch (this.status) {
      case RESPONSE_STATUS.SUCCESS:
        return 'تمت العملية بنجاح';
      case RESPONSE_STATUS.ERROR:
        return 'حدث خطأ أثناء تنفيذ العملية';
      case RESPONSE_STATUS.WARNING:
        return 'تمت العملية مع تحذيرات';
      case RESPONSE_STATUS.INFO:
        return 'معلومات إضافية متاحة';
      default:
        return 'استجابة غير معروفة';
    }
  }

  /**
   * الحصول على أول خطأ
   * @returns {Object|null} أول خطأ أو null
   */
  getFirstError() {
    return this.errors.length > 0 ? this.errors[0] : null;
  }

  /**
   * الحصول على جميع رسائل الأخطاء
   * @returns {Array} مصفوفة رسائل الأخطاء
   */
  getErrorMessages() {
    return this.errors.map(error => {
      if (typeof error === 'string') {
        return error;
      } else if (error.message) {
        return error.message;
      } else {
        return 'خطأ غير محدد';
      }
    });
  }

  /**
   * الحصول على رسائل التحذيرات
   * @returns {Array} مصفوفة رسائل التحذيرات
   */
  getWarningMessages() {
    return this.warnings.map(warning => {
      if (typeof warning === 'string') {
        return warning;
      } else if (warning.message) {
        return warning.message;
      } else {
        return 'تحذير غير محدد';
      }
    });
  }

  /**
   * الحصول على البيانات مع التحقق من النوع
   * @param {Function} validator - دالة التحقق (اختياري)
   * @returns {*} البيانات أو null
   */
  getData(validator = null) {
    if (!this.isSuccess() || this.data === null) {
      return null;
    }
    
    if (validator && typeof validator === 'function') {
      try {
        return validator(this.data) ? this.data : null;
      } catch (error) {
        console.warn('فشل في التحقق من صحة البيانات:', error);
        return null;
      }
    }
    
    return this.data;
  }

  /**
   * الحصول على البيانات كمصفوفة
   * @returns {Array} البيانات كمصفوفة
   */
  getDataAsArray() {
    const data = this.getData();
    
    if (Array.isArray(data)) {
      return data;
    } else if (data !== null && data !== undefined) {
      return [data];
    } else {
      return [];
    }
  }

  /**
   * الحصول على البيانات ككائن
   * @returns {Object} البيانات ككائن
   */
  getDataAsObject() {
    const data = this.getData();
    
    if (data && typeof data === 'object' && !Array.isArray(data)) {
      return data;
    } else {
      return {};
    }
  }

  /**
   * التحقق من وجود ترقيم
   * @returns {boolean} هل يوجد ترقيم
   */
  hasPagination() {
    return this.pagination !== null;
  }

  /**
   * التحقق من وجود صفحة تالية
   * @returns {boolean} هل توجد صفحة تالية
   */
  hasNextPage() {
    return this.pagination && this.pagination.hasNext;
  }

  /**
   * التحقق من وجود صفحة سابقة
   * @returns {boolean} هل توجد صفحة سابقة
   */
  hasPreviousPage() {
    return this.pagination && this.pagination.hasPrevious;
  }

  /**
   * الحصول على معلومات الصفحة الحالية
   * @returns {Object} معلومات الصفحة
   */
  getPageInfo() {
    if (!this.pagination) {
      return null;
    }
    
    return {
      current: this.pagination.page,
      total: this.pagination.totalPages,
      itemsPerPage: this.pagination.pageSize,
      totalItems: this.pagination.totalItems,
      hasNext: this.pagination.hasNext,
      hasPrevious: this.pagination.hasPrevious
    };
  }

  /**
   * التحقق من كون الاستجابة من التخزين المؤقت
   * @returns {boolean} هل من التخزين المؤقت
   */
  isCached() {
    return this.performance && this.performance.cacheHit;
  }

  /**
   * الحصول على وقت التنفيذ
   * @returns {number} وقت التنفيذ بالمللي ثانية
   */
  getExecutionTime() {
    return this.performance ? this.performance.executionTime : 0;
  }

  /**
   * التحقق من كون الاستجابة مشفرة
   * @returns {boolean} هل الاستجابة مشفرة
   */
  isEncrypted() {
    return this.security && this.security.encrypted;
  }

  /**
   * التحقق من كون الاستجابة موقعة
   * @returns {boolean} هل الاستجابة موقعة
   */
  isSigned() {
    return this.security && this.security.signed;
  }

  /**
   * إضافة خطأ جديد
   * @param {string|Object} error - الخطأ
   */
  addError(error) {
    this.errors.push(error);
    if (this.status === RESPONSE_STATUS.SUCCESS) {
      this.status = RESPONSE_STATUS.ERROR;
    }
  }

  /**
   * إضافة تحذير جديد
   * @param {string|Object} warning - التحذير
   */
  addWarning(warning) {
    this.warnings.push(warning);
    if (this.status === RESPONSE_STATUS.SUCCESS) {
      this.status = RESPONSE_STATUS.WARNING;
    }
  }

  /**
   * إضافة معلومة جديدة
   * @param {string|Object} info - المعلومة
   */
  addInfo(info) {
    this.info.push(info);
  }

  /**
   * تحديث البيانات
   * @param {*} newData - البيانات الجديدة
   */
  updateData(newData) {
    this.data = newData;
  }

  /**
   * دمج بيانات إضافية
   * @param {*} additionalData - البيانات الإضافية
   */
  mergeData(additionalData) {
    if (Array.isArray(this.data) && Array.isArray(additionalData)) {
      this.data = [...this.data, ...additionalData];
    } else if (this.data && typeof this.data === 'object' && additionalData && typeof additionalData === 'object') {
      this.data = { ...this.data, ...additionalData };
    } else {
      this.data = additionalData;
    }
  }

  /**
   * تحويل إلى كائن عادي
   * @returns {Object} كائن البيانات
   */
  toObject() {
    return {
      status: this.status,
      message: this.message,
      data: this.data,
      errors: this.errors,
      errorCode: this.errorCode,
      errorDetails: this.errorDetails,
      stackTrace: this.stackTrace,
      statusCode: this.statusCode,
      timestamp: this.timestamp?.toISOString(),
      requestId: this.requestId,
      correlationId: this.correlationId,
      pagination: this.pagination,
      metadata: this.metadata,
      warnings: this.warnings,
      info: this.info,
      performance: this.performance,
      version: this.version,
      apiVersion: this.apiVersion,
      security: this.security
    };
  }

  /**
   * تحويل إلى JSON
   * @returns {string} نص JSON
   */
  toJSON() {
    return JSON.stringify(this.toObject());
  }

  /**
   * إنشاء نسخة من الاستجابة
   * @returns {ApiResponseModel} نسخة جديدة
   */
  clone() {
    return new ApiResponseModel(this.toObject());
  }

  /**
   * إنشاء استجابة من كائن البيانات
   * @static
   * @param {Object} data - بيانات الاستجابة
   * @returns {ApiResponseModel} نموذج الاستجابة
   */
  static fromObject(data) {
    return new ApiResponseModel(data);
  }

  /**
   * إنشاء استجابة من JSON
   * @static
   * @param {string} jsonString - نص JSON
   * @returns {ApiResponseModel} نموذج الاستجابة
   */
  static fromJSON(jsonString) {
    try {
      const data = JSON.parse(jsonString);
      return new ApiResponseModel(data);
    } catch (error) {
      throw new Error('فشل في تحليل JSON للاستجابة');
    }
  }

  /**
   * إنشاء استجابة ناجحة
   * @static
   * @param {*} data - البيانات
   * @param {string} message - الرسالة
   * @param {Object} options - خيارات إضافية
   * @returns {ApiResponseModel} استجابة ناجحة
   */
  static success(data = null, message = 'تمت العملية بنجاح', options = {}) {
    return new ApiResponseModel({
      status: RESPONSE_STATUS.SUCCESS,
      message,
      data,
      statusCode: 200,
      timestamp: new Date(),
      ...options
    });
  }

  /**
   * إنشاء استجابة خطأ
   * @static
   * @param {string} message - رسالة الخطأ
   * @param {string} errorCode - كود الخطأ
   * @param {number} statusCode - كود الحالة HTTP
   * @param {Object} options - خيارات إضافية
   * @returns {ApiResponseModel} استجابة خطأ
   */
  static error(message = 'حدث خطأ', errorCode = ERROR_CODES.UNKNOWN_ERROR, statusCode = 500, options = {}) {
    return new ApiResponseModel({
      status: RESPONSE_STATUS.ERROR,
      message,
      errorCode,
      statusCode,
      timestamp: new Date(),
      errors: [message],
      ...options
    });
  }

  /**
   * إنشاء استجابة تحذير
   * @static
   * @param {*} data - البيانات
   * @param {string} message - رسالة التحذير
   * @param {Array} warnings - قائمة التحذيرات
   * @param {Object} options - خيارات إضافية
   * @returns {ApiResponseModel} استجابة تحذير
   */
  static warning(data = null, message = 'تمت العملية مع تحذيرات', warnings = [], options = {}) {
    return new ApiResponseModel({
      status: RESPONSE_STATUS.WARNING,
      message,
      data,
      warnings,
      statusCode: 200,
      timestamp: new Date(),
      ...options
    });
  }

  /**
   * إنشاء استجابة معلومات
   * @static
   * @param {*} data - البيانات
   * @param {string} message - رسالة المعلومات
   * @param {Array} info - قائمة المعلومات
   * @param {Object} options - خيارات إضافية
   * @returns {ApiResponseModel} استجابة معلومات
   */
  static info(data = null, message = 'معلومات إضافية', info = [], options = {}) {
    return new ApiResponseModel({
      status: RESPONSE_STATUS.INFO,
      message,
      data,
      info,
      statusCode: 200,
      timestamp: new Date(),
      ...options
    });
  }

  /**
   * إنشاء استجابة مع ترقيم
   * @static
   * @param {Array} data - البيانات
   * @param {Object} paginationInfo - معلومات الترقيم
   * @param {string} message - الرسالة
   * @param {Object} options - خيارات إضافية
   * @returns {ApiResponseModel} استجابة مع ترقيم
   */
  static paginated(data = [], paginationInfo = {}, message = 'تم جلب البيانات بنجاح', options = {}) {
    return new ApiResponseModel({
      status: RESPONSE_STATUS.SUCCESS,
      message,
      data,
      pagination: paginationInfo,
      statusCode: 200,
      timestamp: new Date(),
      ...options
    });
  }
}

// تصدير النموذج والثوابت
export default ApiResponseModel;
export { RESPONSE_STATUS, ERROR_CODES };
