/**
 * خدمة الاتصال الفوري - SignalRService
 * تدير الاتصال المباشر مع الخادم باستخدام SignalR
 * توفر إشعارات فورية للرسائل والأحداث
 */

import eventBus from '../core/EventBus.js';
import errorHandler from '../core/ErrorHandler.js';
import stateManager from '../core/StateManager.js';
import { SIGNALR } from '../config/constants.js';

/**
 * كلاس خدمة SignalR
 */
class SignalRService {
  constructor() {
    // حالة الاتصال
    this.connection = null;
    this.isConnected = false;
    this.isConnecting = false;
    this.isInitialized = false;
    
    // إعدادات الخدمة
    this.config = {
      hubUrl: SIGNALR.HUB_URL,
      automaticReconnect: true,
      reconnectInterval: SIGNALR.RECONNECT_INTERVAL,
      maxReconnectAttempts: SIGNALR.MAX_RECONNECT_ATTEMPTS,
      connectionTimeout: SIGNALR.CONNECTION_TIMEOUT,
      enableLogging: false
    };
    
    // معلومات الاتصال
    this.connectionInfo = {
      connectionId: null,
      startTime: null,
      lastActivity: null,
      reconnectAttempts: 0,
      totalReconnects: 0
    };
    
    // إحصائيات الخدمة
    this.stats = {
      messagesReceived: 0,
      messagesSent: 0,
      eventsReceived: 0,
      connectionDrops: 0,
      totalUptime: 0
    };
    
    // مؤقتات
    this.reconnectTimer = null;
    this.heartbeatTimer = null;
    
    console.log('📡 تم إنشاء خدمة SignalR');
  }

  /**
   * تهيئة الخدمة
   * @param {Object} options - خيارات التهيئة
   * @returns {Promise<boolean>} نجحت التهيئة أم لا
   */
  async initialize(options = {}) {
    if (this.isInitialized) {
      return true;
    }

    try {
      console.log('🚀 تهيئة خدمة SignalR...');

      // دمج الخيارات
      this.config = { ...this.config, ...options };

      // التحقق من توفر SignalR
      if (!this._isSignalRAvailable()) {
        throw new Error('مكتبة SignalR غير متوفرة');
      }

      // إنشاء الاتصال
      this._createConnection();

      // تهيئة معالجات الأحداث
      this._setupEventHandlers();

      // تهيئة مستمعي الأحداث
      this._setupEventListeners();

      this.isInitialized = true;

      console.log('✅ تم تهيئة خدمة SignalR بنجاح');
      return true;

    } catch (error) {
      console.error('❌ فشل في تهيئة خدمة SignalR:', error);
      
      errorHandler.handleError(error, {
        type: 'signalr',
        service: 'SignalRService',
        operation: 'initialize'
      });

      return false;
    }
  }

  /**
   * بدء الاتصال
   * @returns {Promise<boolean>} نجح الاتصال أم لا
   */
  async connect() {
    if (this.isConnected || this.isConnecting) {
      return this.isConnected;
    }

    if (!this.isInitialized) {
      throw new Error('يجب تهيئة الخدمة أولاً');
    }

    try {
      console.log('🔌 بدء الاتصال بـ SignalR...');
      
      this.isConnecting = true;
      this.connectionInfo.startTime = new Date();

      // بدء الاتصال
      await this.connection.start();

      this.isConnected = true;
      this.isConnecting = false;
      this.connectionInfo.connectionId = this.connection.connectionId;
      this.connectionInfo.lastActivity = new Date();
      this.connectionInfo.reconnectAttempts = 0;

      // بدء نبضات القلب
      this._startHeartbeat();

      // تحديث الحالة العامة
      stateManager.set('signalr.isConnected', true);
      stateManager.set('signalr.connectionId', this.connectionInfo.connectionId);

      // إطلاق حدث الاتصال
      eventBus.emit('signalr:connected', {
        connectionId: this.connectionInfo.connectionId,
        timestamp: this.connectionInfo.startTime
      });

      console.log(`✅ تم الاتصال بـ SignalR بنجاح (${this.connectionInfo.connectionId})`);
      return true;

    } catch (error) {
      console.error('❌ فشل في الاتصال بـ SignalR:', error);
      
      this.isConnecting = false;
      this._handleConnectionError(error);
      
      return false;
    }
  }

  /**
   * قطع الاتصال
   * @returns {Promise<boolean>} نجح قطع الاتصال أم لا
   */
  async disconnect() {
    if (!this.isConnected) {
      return true;
    }

    try {
      console.log('🔌 قطع الاتصال بـ SignalR...');

      // إيقاف المؤقتات
      this._stopHeartbeat();
      this._stopReconnectTimer();

      // قطع الاتصال
      await this.connection.stop();

      this.isConnected = false;
      this.connectionInfo.connectionId = null;

      // تحديث الحالة العامة
      stateManager.set('signalr.isConnected', false);
      stateManager.set('signalr.connectionId', null);

      // إطلاق حدث قطع الاتصال
      eventBus.emit('signalr:disconnected', {
        timestamp: new Date()
      });

      console.log('✅ تم قطع الاتصال بـ SignalR بنجاح');
      return true;

    } catch (error) {
      console.error('❌ فشل في قطع الاتصال بـ SignalR:', error);
      return false;
    }
  }

  /**
   * إرسال رسالة إلى مجموعة
   * @param {string} groupName - اسم المجموعة
   * @param {string} method - اسم الطريقة
   * @param {*} data - البيانات
   * @returns {Promise<boolean>} نجح الإرسال أم لا
   */
  async sendToGroup(groupName, method, data) {
    if (!this.isConnected) {
      throw new Error('غير متصل بـ SignalR');
    }

    try {
      await this.connection.invoke('SendToGroup', groupName, method, data);
      
      this.stats.messagesSent++;
      this.connectionInfo.lastActivity = new Date();

      eventBus.emit('signalr:message:sent', {
        groupName,
        method,
        data,
        timestamp: new Date()
      });

      return true;

    } catch (error) {
      console.error('❌ فشل في إرسال رسالة إلى المجموعة:', error);
      
      errorHandler.handleError(error, {
        type: 'signalr',
        operation: 'sendToGroup',
        groupName,
        method
      });

      return false;
    }
  }

  /**
   * إرسال رسالة إلى مستخدم محدد
   * @param {string} userId - معرف المستخدم
   * @param {string} method - اسم الطريقة
   * @param {*} data - البيانات
   * @returns {Promise<boolean>} نجح الإرسال أم لا
   */
  async sendToUser(userId, method, data) {
    if (!this.isConnected) {
      throw new Error('غير متصل بـ SignalR');
    }

    try {
      await this.connection.invoke('SendToUser', userId, method, data);
      
      this.stats.messagesSent++;
      this.connectionInfo.lastActivity = new Date();

      eventBus.emit('signalr:message:sent', {
        userId,
        method,
        data,
        timestamp: new Date()
      });

      return true;

    } catch (error) {
      console.error('❌ فشل في إرسال رسالة إلى المستخدم:', error);
      
      errorHandler.handleError(error, {
        type: 'signalr',
        operation: 'sendToUser',
        userId,
        method
      });

      return false;
    }
  }

  /**
   * الانضمام إلى مجموعة
   * @param {string} groupName - اسم المجموعة
   * @returns {Promise<boolean>} نجح الانضمام أم لا
   */
  async joinGroup(groupName) {
    if (!this.isConnected) {
      throw new Error('غير متصل بـ SignalR');
    }

    try {
      await this.connection.invoke('JoinGroup', groupName);

      eventBus.emit('signalr:group:joined', {
        groupName,
        timestamp: new Date()
      });

      console.log(`📥 تم الانضمام إلى المجموعة: ${groupName}`);
      return true;

    } catch (error) {
      console.error('❌ فشل في الانضمام إلى المجموعة:', error);
      
      errorHandler.handleError(error, {
        type: 'signalr',
        operation: 'joinGroup',
        groupName
      });

      return false;
    }
  }

  /**
   * مغادرة مجموعة
   * @param {string} groupName - اسم المجموعة
   * @returns {Promise<boolean>} نجحت المغادرة أم لا
   */
  async leaveGroup(groupName) {
    if (!this.isConnected) {
      throw new Error('غير متصل بـ SignalR');
    }

    try {
      await this.connection.invoke('LeaveGroup', groupName);

      eventBus.emit('signalr:group:left', {
        groupName,
        timestamp: new Date()
      });

      console.log(`📤 تم مغادرة المجموعة: ${groupName}`);
      return true;

    } catch (error) {
      console.error('❌ فشل في مغادرة المجموعة:', error);
      
      errorHandler.handleError(error, {
        type: 'signalr',
        operation: 'leaveGroup',
        groupName
      });

      return false;
    }
  }

  /**
   * تحديث حالة المستخدم
   * @param {string} status - الحالة الجديدة
   * @returns {Promise<boolean>} نجح التحديث أم لا
   */
  async updateUserStatus(status) {
    if (!this.isConnected) {
      throw new Error('غير متصل بـ SignalR');
    }

    try {
      await this.connection.invoke('UpdateUserStatus', status);

      eventBus.emit('signalr:user:status:updated', {
        status,
        timestamp: new Date()
      });

      return true;

    } catch (error) {
      console.error('❌ فشل في تحديث حالة المستخدم:', error);
      
      errorHandler.handleError(error, {
        type: 'signalr',
        operation: 'updateUserStatus',
        status
      });

      return false;
    }
  }

  /**
   * إرسال إشارة كتابة
   * @param {string} chatId - معرف المحادثة
   * @param {boolean} isTyping - هل يكتب المستخدم
   * @returns {Promise<boolean>} نجح الإرسال أم لا
   */
  async sendTypingIndicator(chatId, isTyping) {
    if (!this.isConnected) {
      return false;
    }

    try {
      await this.connection.invoke('SendTypingIndicator', chatId, isTyping);
      return true;

    } catch (error) {
      console.error('❌ فشل في إرسال إشارة الكتابة:', error);
      return false;
    }
  }

  /**
   * الحصول على حالة الاتصال
   * @returns {Object} معلومات الاتصال
   */
  getConnectionInfo() {
    return {
      ...this.connectionInfo,
      isConnected: this.isConnected,
      isConnecting: this.isConnecting,
      uptime: this.connectionInfo.startTime 
        ? Date.now() - this.connectionInfo.startTime.getTime() 
        : 0
    };
  }

  /**
   * الحصول على إحصائيات الخدمة
   * @returns {Object} إحصائيات الخدمة
   */
  getStats() {
    return {
      ...this.stats,
      ...this.getConnectionInfo(),
      config: this.config
    };
  }

  // ==================== دوال خاصة ====================

  /**
   * التحقق من توفر SignalR
   * @private
   */
  _isSignalRAvailable() {
    return typeof signalR !== 'undefined' && signalR.HubConnectionBuilder;
  }

  /**
   * إنشاء الاتصال
   * @private
   */
  _createConnection() {
    const connectionBuilder = new signalR.HubConnectionBuilder()
      .withUrl(this.config.hubUrl)
      .withAutomaticReconnect({
        nextRetryDelayInMilliseconds: (retryContext) => {
          if (retryContext.previousRetryCount < this.config.maxReconnectAttempts) {
            return this.config.reconnectInterval;
          }
          return null; // إيقاف إعادة المحاولة
        }
      });

    // تفعيل التسجيل إذا كان مطلوباً
    if (this.config.enableLogging) {
      connectionBuilder.configureLogging(signalR.LogLevel.Information);
    }

    this.connection = connectionBuilder.build();
  }

  /**
   * إعداد معالجات الأحداث
   * @private
   */
  _setupEventHandlers() {
    // معالج استقبال الرسائل
    this.connection.on('ReceiveMessage', (message) => {
      this._handleReceivedMessage(message);
    });

    // معالج تحديث حالة الرسالة
    this.connection.on('MessageStatusUpdated', (messageId, status) => {
      this._handleMessageStatusUpdate(messageId, status);
    });

    // معالج تحديث حالة المستخدم
    this.connection.on('UserStatusUpdated', (userId, status) => {
      this._handleUserStatusUpdate(userId, status);
    });

    // معالج إشارة الكتابة
    this.connection.on('TypingIndicator', (chatId, userId, isTyping) => {
      this._handleTypingIndicator(chatId, userId, isTyping);
    });

    // معالج الانضمام/المغادرة
    this.connection.on('UserJoined', (chatId, user) => {
      this._handleUserJoined(chatId, user);
    });

    this.connection.on('UserLeft', (chatId, userId) => {
      this._handleUserLeft(chatId, userId);
    });

    // معالجات الاتصال
    this.connection.onclose((error) => {
      this._handleConnectionClosed(error);
    });

    this.connection.onreconnecting((error) => {
      this._handleReconnecting(error);
    });

    this.connection.onreconnected((connectionId) => {
      this._handleReconnected(connectionId);
    });
  }

  /**
   * إعداد مستمعي الأحداث
   * @private
   */
  _setupEventListeners() {
    // مراقبة أحداث المصادقة
    eventBus.on('auth:login:success', async () => {
      if (this.isInitialized && !this.isConnected) {
        await this.connect();
      }
    });

    eventBus.on('auth:logout:success', async () => {
      if (this.isConnected) {
        await this.disconnect();
      }
    });

    // مراقبة أحداث الشبكة
    eventBus.on('network:online', async () => {
      if (this.isInitialized && !this.isConnected) {
        await this.connect();
      }
    });

    eventBus.on('network:offline', async () => {
      if (this.isConnected) {
        await this.disconnect();
      }
    });
  }

  /**
   * معالجة الرسالة المستقبلة
   * @private
   */
  _handleReceivedMessage(message) {
    this.stats.messagesReceived++;
    this.connectionInfo.lastActivity = new Date();

    eventBus.emit('signalr:message:received', message);
    eventBus.emit('message:received', message);

    console.log('📨 تم استقبال رسالة جديدة:', message.id);
  }

  /**
   * معالجة تحديث حالة الرسالة
   * @private
   */
  _handleMessageStatusUpdate(messageId, status) {
    this.stats.eventsReceived++;
    this.connectionInfo.lastActivity = new Date();

    eventBus.emit('signalr:message:status:updated', { messageId, status });
    eventBus.emit('message:status:updated', { messageId, status });
  }

  /**
   * معالجة تحديث حالة المستخدم
   * @private
   */
  _handleUserStatusUpdate(userId, status) {
    this.stats.eventsReceived++;
    this.connectionInfo.lastActivity = new Date();

    eventBus.emit('signalr:user:status:updated', { userId, status });
    eventBus.emit('user:status:updated', { userId, status });
  }

  /**
   * معالجة إشارة الكتابة
   * @private
   */
  _handleTypingIndicator(chatId, userId, isTyping) {
    eventBus.emit('signalr:typing:indicator', { chatId, userId, isTyping });
    eventBus.emit('typing:indicator', { chatId, userId, isTyping });
  }

  /**
   * معالجة انضمام مستخدم
   * @private
   */
  _handleUserJoined(chatId, user) {
    this.stats.eventsReceived++;
    
    eventBus.emit('signalr:user:joined', { chatId, user });
    eventBus.emit('user:joined', { chatId, user });
  }

  /**
   * معالجة مغادرة مستخدم
   * @private
   */
  _handleUserLeft(chatId, userId) {
    this.stats.eventsReceived++;
    
    eventBus.emit('signalr:user:left', { chatId, userId });
    eventBus.emit('user:left', { chatId, userId });
  }

  /**
   * معالجة إغلاق الاتصال
   * @private
   */
  _handleConnectionClosed(error) {
    console.warn('⚠️ تم إغلاق اتصال SignalR:', error);

    this.isConnected = false;
    this.stats.connectionDrops++;

    // تحديث الحالة العامة
    stateManager.set('signalr.isConnected', false);

    // إطلاق حدث إغلاق الاتصال
    eventBus.emit('signalr:connection:closed', { error });

    // محاولة إعادة الاتصال إذا كان مفعلاً
    if (this.config.automaticReconnect) {
      this._scheduleReconnect();
    }
  }

  /**
   * معالجة بدء إعادة الاتصال
   * @private
   */
  _handleReconnecting(error) {
    console.log('🔄 محاولة إعادة الاتصال بـ SignalR...');

    this.connectionInfo.reconnectAttempts++;

    eventBus.emit('signalr:reconnecting', {
      attempt: this.connectionInfo.reconnectAttempts,
      error
    });
  }

  /**
   * معالجة نجاح إعادة الاتصال
   * @private
   */
  _handleReconnected(connectionId) {
    console.log('✅ تم إعادة الاتصال بـ SignalR بنجاح');

    this.isConnected = true;
    this.connectionInfo.connectionId = connectionId;
    this.connectionInfo.totalReconnects++;
    this.connectionInfo.reconnectAttempts = 0;

    // تحديث الحالة العامة
    stateManager.set('signalr.isConnected', true);
    stateManager.set('signalr.connectionId', connectionId);

    // إطلاق حدث إعادة الاتصال
    eventBus.emit('signalr:reconnected', { connectionId });

    // بدء نبضات القلب
    this._startHeartbeat();
  }

  /**
   * معالجة خطأ الاتصال
   * @private
   */
  _handleConnectionError(error) {
    this.stats.connectionDrops++;

    errorHandler.handleError(error, {
      type: 'signalr',
      operation: 'connect'
    });

    eventBus.emit('signalr:connection:error', { error });

    // محاولة إعادة الاتصال
    if (this.config.automaticReconnect) {
      this._scheduleReconnect();
    }
  }

  /**
   * جدولة إعادة الاتصال
   * @private
   */
  _scheduleReconnect() {
    if (this.connectionInfo.reconnectAttempts >= this.config.maxReconnectAttempts) {
      console.error('❌ تم تجاوز الحد الأقصى لمحاولات إعادة الاتصال');
      return;
    }

    this._stopReconnectTimer();

    this.reconnectTimer = setTimeout(async () => {
      try {
        await this.connect();
      } catch (error) {
        console.error('❌ فشل في إعادة الاتصال:', error);
      }
    }, this.config.reconnectInterval);
  }

  /**
   * إيقاف مؤقت إعادة الاتصال
   * @private
   */
  _stopReconnectTimer() {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
  }

  /**
   * بدء نبضات القلب
   * @private
   */
  _startHeartbeat() {
    this._stopHeartbeat();

    this.heartbeatTimer = setInterval(() => {
      if (this.isConnected) {
        this.connection.invoke('Ping').catch((error) => {
          console.warn('⚠️ فشل في نبضة القلب:', error);
        });
      }
    }, 30000); // كل 30 ثانية
  }

  /**
   * إيقاف نبضات القلب
   * @private
   */
  _stopHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }
}

// إنشاء مثيل واحد من خدمة SignalR
const signalRService = new SignalRService();

// تصدير المثيل والكلاس
export default signalRService;
export { SignalRService };
