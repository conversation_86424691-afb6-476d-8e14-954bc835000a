/**
 * معالج الأخطاء المركزي - <PERSON><PERSON>r<PERSON><PERSON>ler
 * يوفر نظام شامل لمعالجة وتسجيل الأخطاء في التطبيق
 * يدعم أنواع مختلفة من الأخطاء مع رسائل مفيدة للمستخدم
 */

import eventBus from './EventBus.js';
import stateManager from './StateManager.js';

// أنواع الأخطاء المختلفة
export const ERROR_TYPES = {
  NETWORK: 'network',
  VALIDATION: 'validation',
  AUTHENTICATION: 'authentication',
  PERMISSION: 'permission',
  DATABASE: 'database',
  SIGNALR: 'signalr',
  UI: 'ui',
  UNKNOWN: 'unknown'
};

// مستويات الخطورة
export const ERROR_SEVERITY = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical'
};

class ErrorHandler {
  constructor() {
    // إعدادات معالج الأخطاء
    this.config = {
      maxErrorsInMemory: 100,
      enableConsoleLogging: true,
      enableRemoteLogging: false,
      remoteEndpoint: null,
      showUserNotifications: true,
      retryAttempts: 3,
      retryDelay: 1000
    };

    // سجل الأخطاء
    this.errorLog = [];
    this.errorStats = new Map();

    // رسائل الأخطاء باللغة العربية
    this.errorMessages = {
      [ERROR_TYPES.NETWORK]: {
        title: 'خطأ في الاتصال',
        message: 'تعذر الاتصال بالخادم. يرجى التحقق من اتصال الإنترنت والمحاولة مرة أخرى.',
        action: 'إعادة المحاولة'
      },
      [ERROR_TYPES.VALIDATION]: {
        title: 'خطأ في البيانات',
        message: 'البيانات المدخلة غير صحيحة. يرجى مراجعة المعلومات والمحاولة مرة أخرى.',
        action: 'تصحيح البيانات'
      },
      [ERROR_TYPES.AUTHENTICATION]: {
        title: 'خطأ في المصادقة',
        message: 'انتهت صلاحية جلسة العمل. يرجى تسجيل الدخول مرة أخرى.',
        action: 'تسجيل الدخول'
      },
      [ERROR_TYPES.PERMISSION]: {
        title: 'غير مصرح',
        message: 'ليس لديك صلاحية للوصول إلى هذه الميزة.',
        action: 'العودة'
      },
      [ERROR_TYPES.DATABASE]: {
        title: 'خطأ في قاعدة البيانات',
        message: 'حدث خطأ في حفظ أو استرجاع البيانات.',
        action: 'إعادة المحاولة'
      },
      [ERROR_TYPES.SIGNALR]: {
        title: 'خطأ في الاتصال المباشر',
        message: 'تعذر الاتصال بخدمة الرسائل المباشرة.',
        action: 'إعادة الاتصال'
      },
      [ERROR_TYPES.UI]: {
        title: 'خطأ في الواجهة',
        message: 'حدث خطأ في عرض الواجهة.',
        action: 'تحديث الصفحة'
      },
      [ERROR_TYPES.UNKNOWN]: {
        title: 'خطأ غير متوقع',
        message: 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.',
        action: 'إعادة المحاولة'
      }
    };

    // تهيئة معالج الأخطاء العام
    this._initializeGlobalErrorHandling();
  }

  /**
   * معالجة خطأ جديد
   * @param {Error|string} error - الخطأ أو رسالة الخطأ
   * @param {Object} context - سياق الخطأ
   * @returns {string} معرف الخطأ
   */
  handleError(error, context = {}) {
    try {
      // إنشاء كائن الخطأ المنظم
      const errorObject = this._createErrorObject(error, context);
      
      // تسجيل الخطأ
      this._logError(errorObject);
      
      // تحديث الإحصائيات
      this._updateErrorStats(errorObject);
      
      // إضافة الخطأ إلى الحالة العامة
      this._addErrorToState(errorObject);
      
      // إطلاق حدث الخطأ
      eventBus.emit('error:occurred', errorObject);
      
      // عرض إشعار للمستخدم إذا كان مطلوباً
      if (this.config.showUserNotifications && errorObject.showToUser) {
        this._showUserNotification(errorObject);
      }
      
      // إرسال الخطأ للخادم إذا كان مفعلاً
      if (this.config.enableRemoteLogging) {
        this._sendErrorToRemote(errorObject);
      }
      
      return errorObject.id;
    } catch (handlingError) {
      console.error('خطأ في معالج الأخطاء نفسه:', handlingError);
      return null;
    }
  }

  /**
   * معالجة خطأ شبكة
   * @param {Error} error - خطأ الشبكة
   * @param {Object} requestInfo - معلومات الطلب
   */
  handleNetworkError(error, requestInfo = {}) {
    return this.handleError(error, {
      type: ERROR_TYPES.NETWORK,
      severity: ERROR_SEVERITY.HIGH,
      requestInfo,
      retryable: true,
      showToUser: true
    });
  }

  /**
   * معالجة خطأ التحقق من صحة البيانات
   * @param {string|Object} validationErrors - أخطاء التحقق
   * @param {Object} formData - بيانات النموذج
   */
  handleValidationError(validationErrors, formData = {}) {
    return this.handleError(validationErrors, {
      type: ERROR_TYPES.VALIDATION,
      severity: ERROR_SEVERITY.MEDIUM,
      formData,
      retryable: false,
      showToUser: true
    });
  }

  /**
   * معالجة خطأ المصادقة
   * @param {Error} error - خطأ المصادقة
   */
  handleAuthenticationError(error) {
    return this.handleError(error, {
      type: ERROR_TYPES.AUTHENTICATION,
      severity: ERROR_SEVERITY.HIGH,
      retryable: false,
      showToUser: true,
      requiresAction: true,
      action: 'logout'
    });
  }

  /**
   * معالجة خطأ SignalR
   * @param {Error} error - خطأ SignalR
   * @param {Object} connectionInfo - معلومات الاتصال
   */
  handleSignalRError(error, connectionInfo = {}) {
    return this.handleError(error, {
      type: ERROR_TYPES.SIGNALR,
      severity: ERROR_SEVERITY.HIGH,
      connectionInfo,
      retryable: true,
      showToUser: true
    });
  }

  /**
   * إعادة المحاولة لخطأ معين
   * @param {string} errorId - معرف الخطأ
   * @param {Function} retryFunction - دالة إعادة المحاولة
   */
  async retry(errorId, retryFunction) {
    const errorObject = this.errorLog.find(err => err.id === errorId);
    
    if (!errorObject || !errorObject.retryable) {
      return false;
    }

    if (errorObject.retryCount >= this.config.retryAttempts) {
      this.handleError('تم تجاوز عدد المحاولات المسموح', {
        type: ERROR_TYPES.UNKNOWN,
        severity: ERROR_SEVERITY.HIGH,
        originalErrorId: errorId
      });
      return false;
    }

    try {
      // زيادة عداد المحاولات
      errorObject.retryCount++;
      errorObject.lastRetryAt = new Date();

      // انتظار قبل إعادة المحاولة
      await new Promise(resolve => 
        setTimeout(resolve, this.config.retryDelay * errorObject.retryCount)
      );

      // تنفيذ إعادة المحاولة
      const result = await retryFunction();
      
      // إذا نجحت، إزالة الخطأ من القائمة
      this._removeErrorFromState(errorId);
      
      eventBus.emit('error:retry:success', {
        errorId,
        result,
        retryCount: errorObject.retryCount
      });

      return true;
    } catch (retryError) {
      // إذا فشلت إعادة المحاولة
      this.handleError(retryError, {
        type: errorObject.type,
        severity: errorObject.severity,
        originalErrorId: errorId,
        isRetry: true
      });

      return false;
    }
  }

  /**
   * مسح خطأ معين
   * @param {string} errorId - معرف الخطأ
   */
  clearError(errorId) {
    this._removeErrorFromState(errorId);
    eventBus.emit('error:cleared', { errorId });
  }

  /**
   * مسح جميع الأخطاء
   */
  clearAllErrors() {
    stateManager.setState('errors', []);
    eventBus.emit('error:all:cleared');
  }

  /**
   * الحصول على إحصائيات الأخطاء
   * @returns {Object} إحصائيات الأخطاء
   */
  getErrorStats() {
    const stats = {};
    
    this.errorStats.forEach((count, type) => {
      stats[type] = count;
    });

    return {
      byType: stats,
      total: this.errorLog.length,
      recent: this.errorLog.filter(err => 
        Date.now() - err.timestamp.getTime() < 24 * 60 * 60 * 1000
      ).length
    };
  }

  /**
   * الحصول على سجل الأخطاء
   * @param {Object} filters - مرشحات البحث
   * @returns {Array} قائمة الأخطاء
   */
  getErrorLog(filters = {}) {
    let filteredErrors = [...this.errorLog];

    if (filters.type) {
      filteredErrors = filteredErrors.filter(err => err.type === filters.type);
    }

    if (filters.severity) {
      filteredErrors = filteredErrors.filter(err => err.severity === filters.severity);
    }

    if (filters.since) {
      filteredErrors = filteredErrors.filter(err => err.timestamp >= filters.since);
    }

    if (filters.limit) {
      filteredErrors = filteredErrors.slice(-filters.limit);
    }

    return filteredErrors;
  }

  /**
   * إنشاء كائن خطأ منظم
   * @private
   */
  _createErrorObject(error, context) {
    const errorId = `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    let message, stack;
    
    if (error instanceof Error) {
      message = error.message;
      stack = error.stack;
    } else if (typeof error === 'string') {
      message = error;
      stack = new Error().stack;
    } else {
      message = JSON.stringify(error);
      stack = new Error().stack;
    }

    return {
      id: errorId,
      message,
      stack,
      type: context.type || ERROR_TYPES.UNKNOWN,
      severity: context.severity || ERROR_SEVERITY.MEDIUM,
      timestamp: new Date(),
      context: { ...context },
      retryable: context.retryable || false,
      retryCount: 0,
      showToUser: context.showToUser !== false,
      userAgent: navigator.userAgent,
      url: window.location.href,
      userId: stateManager.getState('currentUser.id'),
      sessionId: this._getSessionId()
    };
  }

  /**
   * تسجيل الخطأ
   * @private
   */
  _logError(errorObject) {
    // إضافة إلى السجل المحلي
    this.errorLog.push(errorObject);
    
    // الحفاظ على حجم السجل
    if (this.errorLog.length > this.config.maxErrorsInMemory) {
      this.errorLog = this.errorLog.slice(-this.config.maxErrorsInMemory);
    }

    // تسجيل في وحدة التحكم إذا كان مفعلاً
    if (this.config.enableConsoleLogging) {
      console.group(`🚨 خطأ ${errorObject.type} - ${errorObject.severity}`);
      console.error('الرسالة:', errorObject.message);
      console.error('السياق:', errorObject.context);
      console.error('المكدس:', errorObject.stack);
      console.groupEnd();
    }
  }

  /**
   * تحديث إحصائيات الأخطاء
   * @private
   */
  _updateErrorStats(errorObject) {
    const currentCount = this.errorStats.get(errorObject.type) || 0;
    this.errorStats.set(errorObject.type, currentCount + 1);
  }

  /**
   * إضافة الخطأ إلى الحالة العامة
   * @private
   */
  _addErrorToState(errorObject) {
    const currentErrors = stateManager.getState('errors') || [];
    stateManager.setState('errors', [...currentErrors, errorObject]);
  }

  /**
   * إزالة الخطأ من الحالة العامة
   * @private
   */
  _removeErrorFromState(errorId) {
    const currentErrors = stateManager.getState('errors') || [];
    const filteredErrors = currentErrors.filter(err => err.id !== errorId);
    stateManager.setState('errors', filteredErrors);
  }

  /**
   * عرض إشعار للمستخدم
   * @private
   */
  _showUserNotification(errorObject) {
    const errorMessage = this.errorMessages[errorObject.type] || this.errorMessages[ERROR_TYPES.UNKNOWN];
    
    const notification = {
      id: `notification_${errorObject.id}`,
      type: 'error',
      title: errorMessage.title,
      message: errorMessage.message,
      action: errorMessage.action,
      errorId: errorObject.id,
      timestamp: new Date(),
      autoClose: errorObject.severity !== ERROR_SEVERITY.CRITICAL
    };

    // إضافة الإشعار إلى الحالة
    const currentNotifications = stateManager.getState('notifications') || [];
    stateManager.setState('notifications', [...currentNotifications, notification]);

    // إطلاق حدث الإشعار
    eventBus.emit('notification:show', notification);
  }

  /**
   * إرسال الخطأ إلى الخادم
   * @private
   */
  async _sendErrorToRemote(errorObject) {
    if (!this.config.remoteEndpoint) {
      return;
    }

    try {
      await fetch(this.config.remoteEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(errorObject)
      });
    } catch (remoteError) {
      console.error('فشل في إرسال الخطأ إلى الخادم:', remoteError);
    }
  }

  /**
   * الحصول على معرف الجلسة
   * @private
   */
  _getSessionId() {
    let sessionId = sessionStorage.getItem('chatapp_session_id');
    
    if (!sessionId) {
      sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      sessionStorage.setItem('chatapp_session_id', sessionId);
    }
    
    return sessionId;
  }

  /**
   * تهيئة معالجة الأخطاء العامة
   * @private
   */
  _initializeGlobalErrorHandling() {
    // معالجة الأخطاء غير المعالجة
    window.addEventListener('error', (event) => {
      this.handleError(event.error || event.message, {
        type: ERROR_TYPES.UNKNOWN,
        severity: ERROR_SEVERITY.HIGH,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno
      });
    });

    // معالجة الوعود المرفوضة
    window.addEventListener('unhandledrejection', (event) => {
      this.handleError(event.reason, {
        type: ERROR_TYPES.UNKNOWN,
        severity: ERROR_SEVERITY.HIGH,
        isPromiseRejection: true
      });
    });

    // معالجة أخطاء الموارد
    window.addEventListener('error', (event) => {
      if (event.target !== window) {
        this.handleError(`فشل في تحميل المورد: ${event.target.src || event.target.href}`, {
          type: ERROR_TYPES.NETWORK,
          severity: ERROR_SEVERITY.MEDIUM,
          resourceType: event.target.tagName,
          resourceUrl: event.target.src || event.target.href
        });
      }
    }, true);
  }
}

// إنشاء مثيل عام للاستخدام في التطبيق
const errorHandler = new ErrorHandler();

// تصدير الكلاس والمثيل والثوابت
export { ErrorHandler, errorHandler, ERROR_TYPES, ERROR_SEVERITY };
export default errorHandler;
