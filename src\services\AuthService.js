/**
 * خدمة المصادقة - AuthService
 * تدير عمليات تسجيل الدخول والخروج والمصادقة
 * تتعامل مع إدارة الرموز المميزة والجلسات
 */

import apiClient from '../core/ApiClient.js';
import databaseManager from '../core/DatabaseManager.js';
import stateManager from '../core/StateManager.js';
import eventBus from '../core/EventBus.js';
import errorHandler from '../core/ErrorHandler.js';
import { AUTH_ENDPOINTS } from '../config/apiEndpoints.js';
import { STORAGE_KEYS } from '../config/constants.js';
import UserModel from '../models/UserModel.js';

/**
 * كلاس خدمة المصادقة
 */
class AuthService {
  constructor() {
    // حالة المصادقة
    this.isAuthenticated = false;
    this.currentUser = null;
    this.authToken = null;
    this.refreshToken = null;
    
    // إعدادات الجلسة
    this.sessionTimeout = 24 * 60 * 60 * 1000; // 24 ساعة
    this.refreshThreshold = 5 * 60 * 1000; // 5 دقائق
    this.autoRefreshEnabled = true;
    
    // مؤقتات
    this.sessionTimer = null;
    this.refreshTimer = null;
    
    // إعدادات التشخيص
    this.debug = false;
    
    console.log('🔐 تم تهيئة خدمة المصادقة');
    
    // تهيئة المستمعين
    this._initializeEventListeners();
  }

  /**
   * تهيئة الخدمة
   * @returns {Promise<boolean>} نجحت التهيئة أم لا
   */
  async initialize() {
    try {
      console.log('🚀 تهيئة خدمة المصادقة...');

      // استعادة الجلسة المحفوظة
      await this._restoreSession();

      // التحقق من صحة الرمز المميز
      if (this.authToken) {
        const isValid = await this._validateToken();
        if (!isValid) {
          await this._clearSession();
        }
      }

      // بدء مراقبة الجلسة
      this._startSessionMonitoring();

      console.log('✅ تم تهيئة خدمة المصادقة بنجاح');
      return true;

    } catch (error) {
      console.error('❌ فشل في تهيئة خدمة المصادقة:', error);
      
      errorHandler.handleError(error, {
        type: 'authentication',
        operation: 'initialize'
      });

      return false;
    }
  }

  /**
   * تسجيل الدخول
   * @param {string} email - البريد الإلكتروني
   * @param {string} password - كلمة المرور
   * @param {Object} options - خيارات إضافية
   * @returns {Promise<Object>} نتيجة تسجيل الدخول
   */
  async login(email, password, options = {}) {
    try {
      console.log('🔑 محاولة تسجيل الدخول...');

      // التحقق من صحة البيانات
      if (!email || !password) {
        throw new Error('البريد الإلكتروني وكلمة المرور مطلوبان');
      }

      // إعداد بيانات الطلب
      const loginData = {
        email: email.toLowerCase().trim(),
        password,
        rememberMe: options.rememberMe || false,
        deviceInfo: this._getDeviceInfo()
      };

      // إرسال طلب تسجيل الدخول
      const response = await apiClient.post(AUTH_ENDPOINTS.LOGIN, loginData);

      if (response.isSuccess()) {
        const data = response.getData();
        
        // حفظ معلومات المصادقة
        await this._setAuthData(data);
        
        // حفظ معلومات المستخدم
        await this._setCurrentUser(data.user);
        
        // بدء مراقبة الجلسة
        this._startSessionMonitoring();
        
        // إطلاق أحداث النجاح
        eventBus.emit('auth:login:success', {
          user: this.currentUser,
          loginTime: new Date()
        });

        console.log('✅ تم تسجيل الدخول بنجاح');

        return {
          success: true,
          user: this.currentUser,
          message: 'تم تسجيل الدخول بنجاح'
        };

      } else {
        const error = response.getFirstError();
        throw new Error(error?.message || 'فشل في تسجيل الدخول');
      }

    } catch (error) {
      console.error('❌ فشل في تسجيل الدخول:', error);
      
      // إطلاق حدث الفشل
      eventBus.emit('auth:login:failed', {
        email,
        error: error.message,
        timestamp: new Date()
      });

      errorHandler.handleAuthenticationError(error.message, {
        email,
        operation: 'login'
      });

      return {
        success: false,
        error: error.message,
        message: 'فشل في تسجيل الدخول'
      };
    }
  }

  /**
   * تسجيل الخروج
   * @param {boolean} everywhere - تسجيل الخروج من جميع الأجهزة
   * @returns {Promise<boolean>} نجح تسجيل الخروج أم لا
   */
  async logout(everywhere = false) {
    try {
      console.log('🚪 تسجيل الخروج...');

      // إرسال طلب تسجيل الخروج للخادم
      if (this.authToken) {
        try {
          await apiClient.post(AUTH_ENDPOINTS.LOGOUT, {
            everywhere,
            refreshToken: this.refreshToken
          });
        } catch (error) {
          // تجاهل أخطاء الخادم في تسجيل الخروج
          console.warn('⚠️ خطأ في تسجيل الخروج من الخادم:', error.message);
        }
      }

      // مسح الجلسة المحلية
      await this._clearSession();

      // إطلاق حدث تسجيل الخروج
      eventBus.emit('auth:logout:success', {
        everywhere,
        logoutTime: new Date()
      });

      console.log('✅ تم تسجيل الخروج بنجاح');
      return true;

    } catch (error) {
      console.error('❌ فشل في تسجيل الخروج:', error);
      
      errorHandler.handleError(error, {
        type: 'authentication',
        operation: 'logout'
      });

      return false;
    }
  }

  /**
   * تحديث الرمز المميز
   * @returns {Promise<boolean>} نجح التحديث أم لا
   */
  async refreshAuthToken() {
    if (!this.refreshToken) {
      console.warn('⚠️ رمز التحديث غير متوفر');
      return false;
    }

    try {
      console.log('🔄 تحديث الرمز المميز...');

      const response = await apiClient.post(AUTH_ENDPOINTS.REFRESH_TOKEN, {
        refreshToken: this.refreshToken
      });

      if (response.isSuccess()) {
        const data = response.getData();
        
        // تحديث الرموز
        await this._setAuthData(data);
        
        // إطلاق حدث التحديث
        eventBus.emit('auth:token:refreshed', {
          newToken: this.authToken,
          refreshTime: new Date()
        });

        console.log('✅ تم تحديث الرمز المميز بنجاح');
        return true;

      } else {
        throw new Error('فشل في تحديث الرمز المميز');
      }

    } catch (error) {
      console.error('❌ فشل في تحديث الرمز المميز:', error);
      
      // مسح الجلسة في حالة فشل التحديث
      await this._clearSession();
      
      eventBus.emit('auth:token:refresh:failed', {
        error: error.message,
        timestamp: new Date()
      });

      return false;
    }
  }

  /**
   * تغيير كلمة المرور
   * @param {string} currentPassword - كلمة المرور الحالية
   * @param {string} newPassword - كلمة المرور الجديدة
   * @returns {Promise<Object>} نتيجة التغيير
   */
  async changePassword(currentPassword, newPassword) {
    try {
      if (!this.isAuthenticated) {
        throw new Error('يجب تسجيل الدخول أولاً');
      }

      const response = await apiClient.post(AUTH_ENDPOINTS.CHANGE_PASSWORD, {
        currentPassword,
        newPassword
      });

      if (response.isSuccess()) {
        eventBus.emit('auth:password:changed', {
          userId: this.currentUser?.id,
          timestamp: new Date()
        });

        return {
          success: true,
          message: 'تم تغيير كلمة المرور بنجاح'
        };
      } else {
        const error = response.getFirstError();
        throw new Error(error?.message || 'فشل في تغيير كلمة المرور');
      }

    } catch (error) {
      errorHandler.handleError(error, {
        type: 'authentication',
        operation: 'changePassword'
      });

      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * تحديث الملف الشخصي
   * @param {Object} profileData - بيانات الملف الشخصي
   * @returns {Promise<Object>} نتيجة التحديث
   */
  async updateProfile(profileData) {
    try {
      if (!this.isAuthenticated) {
        throw new Error('يجب تسجيل الدخول أولاً');
      }

      const response = await apiClient.put(AUTH_ENDPOINTS.UPDATE_PROFILE, profileData);

      if (response.isSuccess()) {
        const updatedUser = response.getData();
        
        // تحديث المستخدم الحالي
        await this._setCurrentUser(updatedUser);

        eventBus.emit('auth:profile:updated', {
          user: this.currentUser,
          changes: profileData,
          timestamp: new Date()
        });

        return {
          success: true,
          user: this.currentUser,
          message: 'تم تحديث الملف الشخصي بنجاح'
        };
      } else {
        const error = response.getFirstError();
        throw new Error(error?.message || 'فشل في تحديث الملف الشخصي');
      }

    } catch (error) {
      errorHandler.handleError(error, {
        type: 'authentication',
        operation: 'updateProfile'
      });

      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * التحقق من صحة الجلسة
   * @returns {Promise<boolean>} هل الجلسة صحيحة
   */
  async validateSession() {
    if (!this.authToken) {
      return false;
    }

    try {
      const response = await apiClient.get(AUTH_ENDPOINTS.PROFILE);
      
      if (response.isSuccess()) {
        const userData = response.getData();
        await this._setCurrentUser(userData);
        return true;
      }

      return false;

    } catch (error) {
      console.warn('⚠️ فشل في التحقق من صحة الجلسة:', error.message);
      return false;
    }
  }

  /**
   * الحصول على المستخدم الحالي
   * @returns {UserModel|null} المستخدم الحالي
   */
  getCurrentUser() {
    return this.currentUser;
  }

  /**
   * التحقق من حالة المصادقة
   * @returns {boolean} هل المستخدم مصادق عليه
   */
  isUserAuthenticated() {
    return this.isAuthenticated && !!this.authToken && !!this.currentUser;
  }

  /**
   * التحقق من صلاحية
   * @param {string} permission - الصلاحية المطلوبة
   * @returns {boolean} هل المستخدم يملك الصلاحية
   */
  hasPermission(permission) {
    return this.currentUser ? this.currentUser.hasPermission(permission) : false;
  }

  /**
   * التحقق من دور
   * @param {string} role - الدور المطلوب
   * @returns {boolean} هل المستخدم يملك الدور
   */
  hasRole(role) {
    return this.currentUser ? this.currentUser.hasRole(role) : false;
  }

  /**
   * الحصول على الرمز المميز
   * @returns {string|null} الرمز المميز
   */
  getAuthToken() {
    return this.authToken;
  }

  /**
   * تفعيل/إلغاء التحديث التلقائي
   * @param {boolean} enabled - تفعيل التحديث التلقائي
   */
  setAutoRefresh(enabled) {
    this.autoRefreshEnabled = enabled;
    
    if (enabled && this.isAuthenticated) {
      this._scheduleTokenRefresh();
    } else {
      this._clearRefreshTimer();
    }
  }

  /**
   * تفعيل/إلغاء وضع التشخيص
   * @param {boolean} enabled - تفعيل التشخيص
   */
  setDebug(enabled) {
    this.debug = enabled;
    console.log(`🔍 وضع التشخيص لخدمة المصادقة: ${enabled ? 'مفعل' : 'معطل'}`);
  }

  // دوال خاصة

  /**
   * تهيئة مستمعي الأحداث
   * @private
   */
  _initializeEventListeners() {
    // مراقبة انتهاء الجلسة
    eventBus.on('auth:session:expired', () => {
      this._handleSessionExpired();
    });

    // مراقبة أخطاء المصادقة
    eventBus.on('auth:error', (error) => {
      if (error.code === 'TOKEN_EXPIRED') {
        this._handleTokenExpired();
      }
    });
  }

  /**
   * استعادة الجلسة المحفوظة
   * @private
   */
  async _restoreSession() {
    try {
      // استعادة من التخزين المحلي
      const sessionData = localStorage.getItem(STORAGE_KEYS.SESSION_DATA);
      if (sessionData) {
        const parsed = JSON.parse(sessionData);
        
        this.authToken = parsed.authToken;
        this.refreshToken = parsed.refreshToken;
        
        // تعيين الرمز في عميل API
        apiClient.setAuthToken(this.authToken);
        apiClient.setRefreshToken(this.refreshToken);
      }

      // استعادة المستخدم من قاعدة البيانات
      const userData = await databaseManager.get('currentUser', 'user');
      if (userData) {
        this.currentUser = new UserModel(userData);
        this.isAuthenticated = true;
        
        // تحديث الحالة العامة
        stateManager.set('auth.currentUser', this.currentUser.toObject());
        stateManager.set('auth.isAuthenticated', true);
      }

    } catch (error) {
      console.warn('⚠️ فشل في استعادة الجلسة:', error.message);
      await this._clearSession();
    }
  }

  /**
   * حفظ بيانات المصادقة
   * @private
   */
  async _setAuthData(authData) {
    this.authToken = authData.accessToken;
    this.refreshToken = authData.refreshToken;
    this.isAuthenticated = true;

    // تعيين الرموز في عميل API
    apiClient.setAuthToken(this.authToken, authData.tokenType);
    apiClient.setRefreshToken(this.refreshToken);

    // حفظ في التخزين المحلي
    const sessionData = {
      authToken: this.authToken,
      refreshToken: this.refreshToken,
      tokenType: authData.tokenType,
      expiresAt: authData.expiresAt,
      savedAt: new Date().toISOString()
    };

    localStorage.setItem(STORAGE_KEYS.SESSION_DATA, JSON.stringify(sessionData));

    // تحديث الحالة العامة
    stateManager.set('auth.isAuthenticated', true);
    stateManager.set('auth.authToken', this.authToken);
  }

  /**
   * تعيين المستخدم الحالي
   * @private
   */
  async _setCurrentUser(userData) {
    this.currentUser = new UserModel(userData);

    // حفظ في قاعدة البيانات المحلية
    await databaseManager.update('currentUser', 'user', this.currentUser.toObject(), true);

    // تحديث الحالة العامة
    stateManager.set('auth.currentUser', this.currentUser.toObject());

    // تحديث آخر نشاط
    this.currentUser.updateLastActivity();
  }

  /**
   * مسح الجلسة
   * @private
   */
  async _clearSession() {
    // مسح البيانات المحلية
    this.isAuthenticated = false;
    this.currentUser = null;
    this.authToken = null;
    this.refreshToken = null;

    // مسح من التخزين المحلي
    localStorage.removeItem(STORAGE_KEYS.SESSION_DATA);

    // مسح من قاعدة البيانات
    await databaseManager.delete('currentUser', 'user');

    // مسح من عميل API
    apiClient.setAuthToken(null);
    apiClient.setRefreshToken(null);

    // إيقاف المؤقتات
    this._clearSessionTimer();
    this._clearRefreshTimer();

    // تحديث الحالة العامة
    stateManager.set('auth.isAuthenticated', false);
    stateManager.set('auth.currentUser', null);
    stateManager.set('auth.authToken', null);
  }

  /**
   * التحقق من صحة الرمز المميز
   * @private
   */
  async _validateToken() {
    try {
      const response = await apiClient.get(AUTH_ENDPOINTS.PROFILE);
      return response.isSuccess();
    } catch (error) {
      return false;
    }
  }

  /**
   * بدء مراقبة الجلسة
   * @private
   */
  _startSessionMonitoring() {
    this._clearSessionTimer();
    this._clearRefreshTimer();

    if (this.isAuthenticated) {
      // مؤقت انتهاء الجلسة
      this.sessionTimer = setTimeout(() => {
        this._handleSessionExpired();
      }, this.sessionTimeout);

      // جدولة تحديث الرمز المميز
      if (this.autoRefreshEnabled) {
        this._scheduleTokenRefresh();
      }
    }
  }

  /**
   * جدولة تحديث الرمز المميز
   * @private
   */
  _scheduleTokenRefresh() {
    this._clearRefreshTimer();

    if (this.refreshToken) {
      this.refreshTimer = setTimeout(async () => {
        await this.refreshAuthToken();
        
        // جدولة التحديث التالي
        if (this.autoRefreshEnabled && this.isAuthenticated) {
          this._scheduleTokenRefresh();
        }
      }, this.refreshThreshold);
    }
  }

  /**
   * معالجة انتهاء الجلسة
   * @private
   */
  async _handleSessionExpired() {
    console.warn('⚠️ انتهت صلاحية الجلسة');

    await this._clearSession();

    eventBus.emit('auth:session:expired', {
      timestamp: new Date()
    });
  }

  /**
   * معالجة انتهاء صلاحية الرمز المميز
   * @private
   */
  async _handleTokenExpired() {
    if (this.refreshToken) {
      const refreshed = await this.refreshAuthToken();
      if (!refreshed) {
        await this._handleSessionExpired();
      }
    } else {
      await this._handleSessionExpired();
    }
  }

  /**
   * مسح مؤقت الجلسة
   * @private
   */
  _clearSessionTimer() {
    if (this.sessionTimer) {
      clearTimeout(this.sessionTimer);
      this.sessionTimer = null;
    }
  }

  /**
   * مسح مؤقت التحديث
   * @private
   */
  _clearRefreshTimer() {
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
      this.refreshTimer = null;
    }
  }

  /**
   * الحصول على معلومات الجهاز
   * @private
   */
  _getDeviceInfo() {
    return {
      userAgent: navigator.userAgent,
      platform: navigator.platform,
      language: navigator.language,
      screenResolution: `${screen.width}x${screen.height}`,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      timestamp: new Date().toISOString()
    };
  }
}

// إنشاء مثيل واحد من خدمة المصادقة
const authService = new AuthService();

// تصدير المثيل والكلاس
export default authService;
export { AuthService };
