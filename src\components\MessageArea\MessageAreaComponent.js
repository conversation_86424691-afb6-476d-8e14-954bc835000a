/**
 * مكون منطقة الرسائل - MessageAreaComponent
 * يعرض الرسائل ويدير التفاعل معها
 * يوفر واجهة لإرسال واستقبال الرسائل
 */

import BaseComponent from '../Common/BaseComponent.js';
import MessageRenderer from './MessageRenderer.js';
import MessageInput from './MessageInput.js';
import eventBus from '../../core/EventBus.js';
import stateManager from '../../core/StateManager.js';
import { DOMUtils } from '../../utils/DOMUtils.js';
import { DateUtils } from '../../utils/DateUtils.js';

/**
 * كلاس مكون منطقة الرسائل
 */
class MessageAreaComponent extends BaseComponent {
  constructor(options = {}) {
    super({
      className: 'message-area-component',
      template: 'message-area-template',
      ...options
    });

    // إعدادات المكون
    this.config = {
      enableVirtualScrolling: true,
      messagePageSize: 50,
      autoScrollToBottom: true,
      enableMessageSelection: true,
      enableContextMenu: true,
      loadMoreThreshold: 100,
      typingIndicatorTimeout: 3000,
      ...options.config
    };

    // حالة المكون
    this.state = {
      currentChatId: null,
      messages: [],
      isLoading: false,
      hasMoreMessages: true,
      isAtBottom: true,
      selectedMessages: new Set(),
      typingUsers: new Map(),
      lastSeenMessageId: null,
      currentPage: 1
    };

    // مراجع العناصر
    this.elements = {
      container: null,
      header: null,
      messagesContainer: null,
      messagesList: null,
      loadingIndicator: null,
      emptyState: null,
      scrollToBottomBtn: null,
      typingIndicator: null,
      messageInput: null
    };

    // المكونات الفرعية
    this.messageRenderer = null;
    this.messageInput = null;

    // مؤقتات
    this.scrollThrottleTimer = null;
    this.typingTimer = null;
    this.autoScrollTimer = null;

    // متغيرات التمرير
    this.lastScrollTop = 0;
    this.isUserScrolling = false;

    console.log('💬 تم إنشاء مكون منطقة الرسائل');
  }

  /**
   * الحصول على قالب HTML للمكون
   * @returns {string} قالب HTML
   */
  getTemplate() {
    return `
      <div class="message-area-component" data-component="message-area">
        <!-- رأس منطقة الرسائل -->
        <div class="message-area-header" data-element="header">
          ${this._getHeaderTemplate()}
        </div>

        <!-- منطقة الرسائل -->
        <div class="message-area-content">
          <div class="messages-container" data-element="messages-container">
            <!-- مؤشر التحميل العلوي -->
            <div class="messages-loading-top" data-loading="top" style="display: none;">
              <div class="loading-spinner small"></div>
              <span>جاري تحميل الرسائل...</span>
            </div>

            <!-- قائمة الرسائل -->
            <div class="messages-list" data-element="messages-list" role="log" aria-live="polite">
              <!-- الرسائل ستُدرج هنا -->
            </div>

            <!-- مؤشر الكتابة -->
            <div class="typing-indicator" data-element="typing-indicator" style="display: none;">
              <div class="typing-avatar">
                <img src="" alt="" class="typing-user-avatar">
              </div>
              <div class="typing-content">
                <div class="typing-bubble">
                  <span class="typing-text"></span>
                  <div class="typing-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                  </div>
                </div>
              </div>
            </div>

            <!-- حالة فارغة -->
            <div class="messages-empty" data-element="empty-state" style="display: none;">
              <div class="empty-icon">💬</div>
              <h3>ابدأ المحادثة</h3>
              <p>اكتب رسالتك الأولى لبدء المحادثة</p>
            </div>
          </div>

          <!-- زر التمرير للأسفل -->
          <button class="scroll-to-bottom-btn" 
                  data-element="scroll-to-bottom" 
                  style="display: none;"
                  title="التمرير للأسفل">
            <i class="icon-chevron-down"></i>
            <span class="unread-count" style="display: none;">0</span>
          </button>
        </div>

        <!-- منطقة إدخال الرسائل -->
        <div class="message-input-area" data-element="message-input">
          <!-- سيتم إدراج مكون إدخال الرسائل هنا -->
        </div>
      </div>
    `;
  }

  /**
   * تهيئة المكون بعد العرض
   */
  async onRender() {
    try {
      // الحصول على مراجع العناصر
      this._getElementReferences();

      // تهيئة المكونات الفرعية
      await this._initializeSubComponents();

      // تهيئة الأحداث
      this._setupEventListeners();

      // تحميل المحادثة الحالية إن وجدت
      const currentChatId = stateManager.get('chats.selected');
      if (currentChatId) {
        await this.loadChat(currentChatId);
      } else {
        this._showEmptyState();
      }

      console.log('✅ تم تهيئة مكون منطقة الرسائل');

    } catch (error) {
      console.error('❌ فشل في تهيئة مكون منطقة الرسائل:', error);
      this._showError('فشل في تحميل منطقة الرسائل');
    }
  }

  /**
   * تنظيف المكون عند الإزالة
   */
  onDestroy() {
    // إزالة المؤقتات
    if (this.scrollThrottleTimer) {
      clearTimeout(this.scrollThrottleTimer);
    }
    if (this.typingTimer) {
      clearTimeout(this.typingTimer);
    }
    if (this.autoScrollTimer) {
      clearTimeout(this.autoScrollTimer);
    }

    // تنظيف المكونات الفرعية
    if (this.messageRenderer) {
      this.messageRenderer.destroy();
    }
    if (this.messageInput) {
      this.messageInput.destroy();
    }

    // مسح الحالة
    this.state.selectedMessages.clear();
    this.state.typingUsers.clear();

    console.log('🧹 تم تنظيف مكون منطقة الرسائل');
  }

  // ==================== طرق عامة ====================

  /**
   * تحميل محادثة
   * @param {string} chatId - معرف المحادثة
   */
  async loadChat(chatId) {
    if (this.state.currentChatId === chatId) {
      return;
    }

    try {
      console.log(`📥 تحميل محادثة: ${chatId}`);

      // إعادة تعيين الحالة
      this._resetState();
      this.state.currentChatId = chatId;

      // تحديث الرأس
      await this._updateHeader(chatId);

      // تحميل الرسائل
      await this._loadMessages(chatId);

      // تفعيل إدخال الرسائل
      if (this.messageInput) {
        this.messageInput.enable();
        this.messageInput.setChatId(chatId);
      }

      // إطلاق حدث تحميل المحادثة
      this.emit('chat:loaded', { chatId });

    } catch (error) {
      console.error('❌ فشل في تحميل المحادثة:', error);
      this._showError('فشل في تحميل المحادثة');
    }
  }

  /**
   * إضافة رسالة جديدة
   * @param {Object} message - بيانات الرسالة
   */
  addMessage(message) {
    if (!message || message.chatID !== this.state.currentChatId) {
      return;
    }

    // التحقق من عدم وجود الرسالة مسبقاً
    const existingIndex = this.state.messages.findIndex(m => m.id === message.id);
    
    if (existingIndex === -1) {
      // إضافة الرسالة في المكان المناسب حسب التاريخ
      this._insertMessageInOrder(message);
    } else {
      // تحديث الرسالة الموجودة
      this.state.messages[existingIndex] = message;
    }

    // إعادة عرض الرسائل
    this._renderMessages();

    // التمرير للأسفل إذا كان المستخدم في الأسفل
    if (this.state.isAtBottom && this.config.autoScrollToBottom) {
      this._scrollToBottom(true);
    } else {
      // إظهار زر التمرير للأسفل مع عداد الرسائل الجديدة
      this._showScrollToBottomButton(true);
    }

    // تحديث آخر رسالة مرئية
    this._updateLastSeenMessage();
  }

  /**
   * تحديث رسالة موجودة
   * @param {Object} updatedMessage - بيانات الرسالة المحدثة
   */
  updateMessage(updatedMessage) {
    const index = this.state.messages.findIndex(m => m.id === updatedMessage.id);
    
    if (index !== -1) {
      this.state.messages[index] = { ...this.state.messages[index], ...updatedMessage };
      
      // إعادة عرض الرسالة المحدثة
      if (this.messageRenderer) {
        this.messageRenderer.updateMessage(updatedMessage);
      }
    }
  }

  /**
   * حذف رسالة
   * @param {string} messageId - معرف الرسالة
   */
  removeMessage(messageId) {
    this.state.messages = this.state.messages.filter(m => m.id !== messageId);
    
    // إعادة عرض الرسائل
    this._renderMessages();
  }

  /**
   * تحديد رسائل
   * @param {Array} messageIds - معرفات الرسائل
   */
  selectMessages(messageIds) {
    this.state.selectedMessages.clear();
    messageIds.forEach(id => this.state.selectedMessages.add(id));
    
    // تحديث عرض الرسائل المحددة
    this._updateSelectedMessages();
    
    // إطلاق حدث التحديد
    this.emit('messages:selected', { 
      messageIds: Array.from(this.state.selectedMessages) 
    });
  }

  /**
   * مسح تحديد الرسائل
   */
  clearSelection() {
    this.state.selectedMessages.clear();
    this._updateSelectedMessages();
    
    this.emit('messages:selection:cleared');
  }

  /**
   * تحديث مؤشر الكتابة
   * @param {string} userId - معرف المستخدم
   * @param {boolean} isTyping - هل يكتب المستخدم
   * @param {Object} userInfo - معلومات المستخدم
   */
  updateTypingIndicator(userId, isTyping, userInfo = {}) {
    if (isTyping) {
      this.state.typingUsers.set(userId, {
        ...userInfo,
        timestamp: Date.now()
      });
    } else {
      this.state.typingUsers.delete(userId);
    }

    this._updateTypingIndicator();
  }

  /**
   * التمرير إلى رسالة محددة
   * @param {string} messageId - معرف الرسالة
   * @param {boolean} highlight - تمييز الرسالة
   */
  scrollToMessage(messageId, highlight = true) {
    if (this.messageRenderer) {
      this.messageRenderer.scrollToMessage(messageId, highlight);
    }
  }

  /**
   * تحميل المزيد من الرسائل
   */
  async loadMoreMessages() {
    if (this.state.isLoading || !this.state.hasMoreMessages) {
      return;
    }

    try {
      this.state.isLoading = true;
      this.state.currentPage++;

      this._showLoadingIndicator(true);

      // تحميل الرسائل الأقدم
      const olderMessages = await this._loadMessagesPage(
        this.state.currentChatId, 
        this.state.currentPage
      );

      if (olderMessages.length === 0) {
        this.state.hasMoreMessages = false;
      } else {
        // إضافة الرسائل في المقدمة
        this.state.messages = [...olderMessages, ...this.state.messages];
        this._renderMessages();
      }

      this._showLoadingIndicator(false);
      this.state.isLoading = false;

    } catch (error) {
      console.error('❌ فشل في تحميل المزيد من الرسائل:', error);
      this.state.isLoading = false;
      this._showLoadingIndicator(false);
    }
  }

  // ==================== دوال خاصة ====================

  /**
   * الحصول على قالب الرأس
   * @private
   */
  _getHeaderTemplate() {
    return `
      <div class="chat-header-info">
        <div class="chat-avatar">
          <img src="" alt="" class="chat-avatar-img">
          <div class="chat-status-indicator"></div>
        </div>
        <div class="chat-details">
          <h3 class="chat-name">اختر محادثة</h3>
          <p class="chat-status">غير متصل</p>
        </div>
      </div>
      
      <div class="chat-header-actions">
        <button class="header-btn" data-action="search" title="البحث">
          <i class="icon-search"></i>
        </button>
        <button class="header-btn" data-action="call" title="مكالمة">
          <i class="icon-phone"></i>
        </button>
        <button class="header-btn" data-action="video-call" title="مكالمة فيديو">
          <i class="icon-video"></i>
        </button>
        <button class="header-btn" data-action="info" title="معلومات المحادثة">
          <i class="icon-info"></i>
        </button>
        <button class="header-btn" data-action="menu" title="المزيد">
          <i class="icon-more-vertical"></i>
        </button>
      </div>
    `;
  }

  /**
   * الحصول على مراجع العناصر
   * @private
   */
  _getElementReferences() {
    const container = this.getElement();
    
    this.elements = {
      container,
      header: container.querySelector('[data-element="header"]'),
      messagesContainer: container.querySelector('[data-element="messages-container"]'),
      messagesList: container.querySelector('[data-element="messages-list"]'),
      loadingIndicator: container.querySelector('[data-loading="top"]'),
      emptyState: container.querySelector('[data-element="empty-state"]'),
      scrollToBottomBtn: container.querySelector('[data-element="scroll-to-bottom"]'),
      typingIndicator: container.querySelector('[data-element="typing-indicator"]'),
      messageInputArea: container.querySelector('[data-element="message-input"]')
    };
  }

  /**
   * تهيئة المكونات الفرعية
   * @private
   */
  async _initializeSubComponents() {
    // تهيئة عارض الرسائل
    this.messageRenderer = new MessageRenderer({
      container: this.elements.messagesList,
      enableVirtualScrolling: this.config.enableVirtualScrolling,
      enableSelection: this.config.enableMessageSelection,
      onMessageClick: (message) => this._handleMessageClick(message),
      onMessageContextMenu: (message, event) => this._handleMessageContextMenu(message, event),
      onSelectionChange: (selectedIds) => this._handleSelectionChange(selectedIds)
    });

    await this.messageRenderer.initialize();

    // تهيئة مكون إدخال الرسائل
    this.messageInput = new MessageInput({
      container: this.elements.messageInputArea,
      onSendMessage: (messageData) => this._handleSendMessage(messageData),
      onTyping: (isTyping) => this._handleTyping(isTyping),
      onFileSelect: (files) => this._handleFileSelect(files)
    });

    await this.messageInput.render();
  }

  /**
   * تهيئة مستمعي الأحداث
   * @private
   */
  _setupEventListeners() {
    // أحداث التمرير
    if (this.elements.messagesContainer) {
      this.elements.messagesContainer.addEventListener('scroll', (e) => {
        this._handleScroll(e);
      });
    }

    // أحداث الرأس
    if (this.elements.header) {
      this.elements.header.addEventListener('click', (e) => {
        const action = e.target.closest('[data-action]')?.dataset.action;
        if (action) {
          this._handleHeaderAction(action);
        }
      });
    }

    // زر التمرير للأسفل
    if (this.elements.scrollToBottomBtn) {
      this.elements.scrollToBottomBtn.addEventListener('click', () => {
        this._scrollToBottom(true);
      });
    }

    // أحداث النظام
    eventBus.on('chat:selected', (data) => {
      this.loadChat(data.chatId);
    });

    eventBus.on('message:received', (data) => {
      this.addMessage(data.message);
    });

    eventBus.on('message:status:updated', (data) => {
      this.updateMessage({ 
        id: data.messageId, 
        messageStatus: data.status 
      });
    });

    eventBus.on('typing:indicator', (data) => {
      if (data.chatId === this.state.currentChatId) {
        this.updateTypingIndicator(data.userId, data.isTyping, data.userInfo);
      }
    });

    eventBus.on('user:status:updated', (data) => {
      this._updateUserStatus(data.userId, data.status);
    });

    // أحداث لوحة المفاتيح
    document.addEventListener('keydown', (e) => {
      this._handleKeyboardShortcuts(e);
    });

    // أحداث الحالة العامة
    stateManager.subscribe('chats.selected', (chatId) => {
      if (chatId !== this.state.currentChatId) {
        this.loadChat(chatId);
      }
    });
  }

  /**
   * إعادة تعيين الحالة
   * @private
   */
  _resetState() {
    this.state.messages = [];
    this.state.isLoading = false;
    this.state.hasMoreMessages = true;
    this.state.isAtBottom = true;
    this.state.selectedMessages.clear();
    this.state.typingUsers.clear();
    this.state.currentPage = 1;
    this.lastScrollTop = 0;
    this.isUserScrolling = false;
  }

  /**
   * تحديث الرأس
   * @private
   */
  async _updateHeader(chatId) {
    try {
      // الحصول على بيانات المحادثة
      const chat = stateManager.get('chats.list')?.find(c => c.id === chatId);
      
      if (!chat) return;

      const headerInfo = this.elements.header.querySelector('.chat-header-info');
      if (!headerInfo) return;

      // تحديث الصورة
      const avatarImg = headerInfo.querySelector('.chat-avatar-img');
      if (avatarImg) {
        avatarImg.src = chat.avatarUrl || this._getDefaultAvatar(chat);
        avatarImg.alt = chat.name;
      }

      // تحديث الاسم
      const nameElement = headerInfo.querySelector('.chat-name');
      if (nameElement) {
        nameElement.textContent = chat.name;
      }

      // تحديث الحالة
      const statusElement = headerInfo.querySelector('.chat-status');
      if (statusElement) {
        statusElement.textContent = this._getChatStatus(chat);
      }

      // تحديث مؤشر الحالة
      const statusIndicator = headerInfo.querySelector('.chat-status-indicator');
      if (statusIndicator) {
        statusIndicator.className = `chat-status-indicator ${chat.isOnline ? 'online' : 'offline'}`;
      }

    } catch (error) {
      console.error('❌ فشل في تحديث رأس المحادثة:', error);
    }
  }

  /**
   * تحميل الرسائل
   * @private
   */
  async _loadMessages(chatId) {
    try {
      this.state.isLoading = true;
      this._showLoadingIndicator(true);

      // تحميل الصفحة الأولى من الرسائل
      const messages = await this._loadMessagesPage(chatId, 1);

      this.state.messages = messages;
      this.state.hasMoreMessages = messages.length === this.config.messagePageSize;

      // عرض الرسائل
      this._renderMessages();

      // التمرير للأسفل
      this._scrollToBottom(false);

      // إخفاء الحالة الفارغة
      this._showEmptyState(false);

      this.state.isLoading = false;
      this._showLoadingIndicator(false);

    } catch (error) {
      console.error('❌ فشل في تحميل الرسائل:', error);
      this.state.isLoading = false;
      this._showLoadingIndicator(false);
      this._showError('فشل في تحميل الرسائل');
    }
  }

  /**
   * تحميل صفحة من الرسائل
   * @private
   */
  async _loadMessagesPage(chatId, page) {
    // هنا يتم استدعاء خدمة الرسائل
    // مؤقتاً سنعيد مصفوفة فارغة
    return [];
  }

  /**
   * عرض الرسائل
   * @private
   */
  _renderMessages() {
    if (this.messageRenderer) {
      this.messageRenderer.renderMessages(this.state.messages);
    }

    // إظهار/إخفاء الحالة الفارغة
    this._showEmptyState(this.state.messages.length === 0);
  }

  /**
   * إدراج رسالة في المكان المناسب
   * @private
   */
  _insertMessageInOrder(message) {
    const messageDate = new Date(message.createdDate);
    
    // البحث عن المكان المناسب للإدراج
    let insertIndex = this.state.messages.length;
    
    for (let i = this.state.messages.length - 1; i >= 0; i--) {
      const existingDate = new Date(this.state.messages[i].createdDate);
      
      if (messageDate >= existingDate) {
        insertIndex = i + 1;
        break;
      }
    }

    // إدراج الرسالة
    this.state.messages.splice(insertIndex, 0, message);
  }

  /**
   * معالجة التمرير
   * @private
   */
  _handleScroll(event) {
    if (this.scrollThrottleTimer) return;

    this.scrollThrottleTimer = setTimeout(() => {
      const container = event.target;
      const scrollTop = container.scrollTop;
      const scrollHeight = container.scrollHeight;
      const clientHeight = container.clientHeight;

      // تحديد اتجاه التمرير
      const isScrollingUp = scrollTop < this.lastScrollTop;
      const isScrollingDown = scrollTop > this.lastScrollTop;

      // تحديد ما إذا كان المستخدم في الأسفل
      const isAtBottom = scrollHeight - scrollTop - clientHeight < 50;
      this.state.isAtBottom = isAtBottom;

      // إظهار/إخفاء زر التمرير للأسفل
      this._showScrollToBottomButton(!isAtBottom);

      // تحميل المزيد من الرسائل عند الوصول للأعلى
      if (isScrollingUp && scrollTop < this.config.loadMoreThreshold) {
        this.loadMoreMessages();
      }

      // تحديث آخر رسالة مرئية
      if (isAtBottom) {
        this._updateLastSeenMessage();
      }

      // تحديد ما إذا كان المستخدم يتمرر
      this.isUserScrolling = true;
      clearTimeout(this.autoScrollTimer);
      this.autoScrollTimer = setTimeout(() => {
        this.isUserScrolling = false;
      }, 1000);

      this.lastScrollTop = scrollTop;
      this.scrollThrottleTimer = null;
    }, 100);
  }

  /**
   * التمرير للأسفل
   * @private
   */
  _scrollToBottom(smooth = true) {
    if (!this.elements.messagesContainer) return;

    const container = this.elements.messagesContainer;
    
    if (smooth) {
      container.scrollTo({
        top: container.scrollHeight,
        behavior: 'smooth'
      });
    } else {
      container.scrollTop = container.scrollHeight;
    }

    this.state.isAtBottom = true;
    this._showScrollToBottomButton(false);
  }

  /**
   * إظهار/إخفاء زر التمرير للأسفل
   * @private
   */
  _showScrollToBottomButton(show) {
    if (this.elements.scrollToBottomBtn) {
      this.elements.scrollToBottomBtn.style.display = show ? 'flex' : 'none';
    }
  }

  /**
   * إظهار/إخفاء مؤشر التحميل
   * @private
   */
  _showLoadingIndicator(show) {
    if (this.elements.loadingIndicator) {
      this.elements.loadingIndicator.style.display = show ? 'flex' : 'none';
    }
  }

  /**
   * إظهار/إخفاء الحالة الفارغة
   * @private
   */
  _showEmptyState(show = true) {
    if (this.elements.emptyState) {
      this.elements.emptyState.style.display = show ? 'flex' : 'none';
    }
    
    if (this.elements.messagesList) {
      this.elements.messagesList.style.display = show ? 'none' : 'block';
    }
  }

  /**
   * تحديث مؤشر الكتابة
   * @private
   */
  _updateTypingIndicator() {
    const typingUsers = Array.from(this.state.typingUsers.values());
    const isAnyoneTyping = typingUsers.length > 0;

    if (!this.elements.typingIndicator) return;

    if (isAnyoneTyping) {
      const user = typingUsers[0]; // إظهار أول مستخدم يكتب
      
      // تحديث الصورة
      const avatar = this.elements.typingIndicator.querySelector('.typing-user-avatar');
      if (avatar) {
        avatar.src = user.avatarUrl || this._getDefaultAvatar(user);
        avatar.alt = user.name;
      }

      // تحديث النص
      const text = this.elements.typingIndicator.querySelector('.typing-text');
      if (text) {
        if (typingUsers.length === 1) {
          text.textContent = `${user.name} يكتب`;
        } else {
          text.textContent = `${user.name} و ${typingUsers.length - 1} آخرون يكتبون`;
        }
      }

      this.elements.typingIndicator.style.display = 'flex';
      
      // التمرير للأسفل إذا كان المستخدم في الأسفل
      if (this.state.isAtBottom) {
        this._scrollToBottom(true);
      }
    } else {
      this.elements.typingIndicator.style.display = 'none';
    }

    // تنظيف المستخدمين القدامى
    this._cleanupTypingUsers();
  }

  /**
   * تنظيف مستخدمي الكتابة القدامى
   * @private
   */
  _cleanupTypingUsers() {
    const now = Date.now();
    const timeout = this.config.typingIndicatorTimeout;

    for (const [userId, userInfo] of this.state.typingUsers) {
      if (now - userInfo.timestamp > timeout) {
        this.state.typingUsers.delete(userId);
      }
    }
  }

  /**
   * تحديث الرسائل المحددة
   * @private
   */
  _updateSelectedMessages() {
    if (this.messageRenderer) {
      this.messageRenderer.updateSelection(Array.from(this.state.selectedMessages));
    }
  }

  /**
   * تحديث آخر رسالة مرئية
   * @private
   */
  _updateLastSeenMessage() {
    if (this.state.messages.length > 0) {
      const lastMessage = this.state.messages[this.state.messages.length - 1];
      this.state.lastSeenMessageId = lastMessage.id;
      
      // إطلاق حدث قراءة الرسائل
      this.emit('messages:read', {
        chatId: this.state.currentChatId,
        lastMessageId: lastMessage.id
      });
    }
  }

  /**
   * معالجة النقر على رسالة
   * @private
   */
  _handleMessageClick(message) {
    if (this.config.enableMessageSelection) {
      // تبديل تحديد الرسالة
      if (this.state.selectedMessages.has(message.id)) {
        this.state.selectedMessages.delete(message.id);
      } else {
        this.state.selectedMessages.add(message.id);
      }
      
      this._updateSelectedMessages();
      
      this.emit('message:clicked', { message });
    }
  }

  /**
   * معالجة القائمة السياقية للرسالة
   * @private
   */
  _handleMessageContextMenu(message, event) {
    if (this.config.enableContextMenu) {
      event.preventDefault();
      
      this.emit('message:context-menu', {
        message,
        x: event.clientX,
        y: event.clientY
      });
    }
  }

  /**
   * معالجة تغيير التحديد
   * @private
   */
  _handleSelectionChange(selectedIds) {
    this.state.selectedMessages = new Set(selectedIds);
    this.emit('messages:selection:changed', { selectedIds });
  }

  /**
   * معالجة إرسال رسالة
   * @private
   */
  _handleSendMessage(messageData) {
    this.emit('message:send', {
      chatId: this.state.currentChatId,
      ...messageData
    });
  }

  /**
   * معالجة الكتابة
   * @private
   */
  _handleTyping(isTyping) {
    this.emit('typing:indicator:send', {
      chatId: this.state.currentChatId,
      isTyping
    });
  }

  /**
   * معالجة اختيار الملفات
   * @private
   */
  _handleFileSelect(files) {
    this.emit('files:selected', {
      chatId: this.state.currentChatId,
      files
    });
  }

  /**
   * معالجة إجراءات الرأس
   * @private
   */
  _handleHeaderAction(action) {
    this.emit('header:action', {
      action,
      chatId: this.state.currentChatId
    });
  }

  /**
   * معالجة اختصارات لوحة المفاتيح
   * @private
   */
  _handleKeyboardShortcuts(event) {
    // Ctrl/Cmd + A: تحديد جميع الرسائل
    if ((event.ctrlKey || event.metaKey) && event.key === 'a') {
      if (this.config.enableMessageSelection && this.state.messages.length > 0) {
        event.preventDefault();
        const allIds = this.state.messages.map(m => m.id);
        this.selectMessages(allIds);
      }
    }

    // Escape: مسح التحديد
    if (event.key === 'Escape') {
      if (this.state.selectedMessages.size > 0) {
        this.clearSelection();
      }
    }

    // End: التمرير للأسفل
    if (event.key === 'End') {
      this._scrollToBottom(true);
    }

    // Home: التمرير للأعلى
    if (event.key === 'Home') {
      if (this.elements.messagesContainer) {
        this.elements.messagesContainer.scrollTo({
          top: 0,
          behavior: 'smooth'
        });
      }
    }
  }

  /**
   * تحديث حالة المستخدم
   * @private
   */
  _updateUserStatus(userId, status) {
    // تحديث حالة المستخدم في الرأس إذا كانت محادثة خاصة
    const chat = stateManager.get('chats.list')?.find(c => c.id === this.state.currentChatId);
    
    if (chat && chat.type === 'Private' && chat.userId === userId) {
      const statusElement = this.elements.header?.querySelector('.chat-status');
      if (statusElement) {
        statusElement.textContent = this._getStatusText(status);
      }

      const statusIndicator = this.elements.header?.querySelector('.chat-status-indicator');
      if (statusIndicator) {
        statusIndicator.className = `chat-status-indicator ${status === 'online' ? 'online' : 'offline'}`;
      }
    }
  }

  /**
   * الحصول على صورة افتراضية
   * @private
   */
  _getDefaultAvatar(entity) {
    const initial = entity.name ? entity.name.charAt(0).toUpperCase() : '?';
    return `data:image/svg+xml,${encodeURIComponent(`
      <svg width="40" height="40" xmlns="http://www.w3.org/2000/svg">
        <circle cx="20" cy="20" r="20" fill="#007bff"/>
        <text x="20" y="26" text-anchor="middle" fill="white" font-size="16" font-family="Arial">
          ${initial}
        </text>
      </svg>
    `)}`;
  }

  /**
   * الحصول على حالة المحادثة
   * @private
   */
  _getChatStatus(chat) {
    if (chat.type === 'Private') {
      return chat.isOnline ? 'متصل' : 'غير متصل';
    } else {
      return `${chat.membersCount || 0} عضو`;
    }
  }

  /**
   * الحصول على نص الحالة
   * @private
   */
  _getStatusText(status) {
    switch (status) {
      case 'online':
        return 'متصل';
      case 'away':
        return 'غائب';
      case 'busy':
        return 'مشغول';
      case 'offline':
      default:
        return 'غير متصل';
    }
  }

  /**
   * إظهار رسالة خطأ
   * @private
   */
  _showError(message) {
    console.error('خطأ في مكون منطقة الرسائل:', message);
    // يمكن تطوير نظام إظهار الأخطاء هنا
  }
}

// تصدير الكلاس
export default MessageAreaComponent;
