/**
 * أدوات DOM - DOMUtils
 * يوفر دوال مساعدة للتعامل مع DOM بطريقة محسنة وآمنة
 * يدعم العمليات الشائعة مع معالجة الأخطاء
 */

/**
 * البحث عن عنصر بالمعرف مع التحقق من الوجود
 * @param {string} id - معرف العنصر
 * @param {Element} parent - العنصر الأب (اختياري)
 * @returns {Element|null} العنصر أو null
 */
export function getElementById(id, parent = document) {
  try {
    return parent.getElementById ? parent.getElementById(id) : parent.querySelector(`#${id}`);
  } catch (error) {
    console.warn(`فشل في العثور على العنصر بالمعرف: ${id}`, error);
    return null;
  }
}

/**
 * البحث عن عنصر بالكلاس
 * @param {string} className - اسم الكلاس
 * @param {Element} parent - العنصر الأب (اختياري)
 * @returns {Element|null} أول عنصر أو null
 */
export function getElementByClass(className, parent = document) {
  try {
    return parent.querySelector(`.${className}`);
  } catch (error) {
    console.warn(`فشل في العثور على العنصر بالكلاس: ${className}`, error);
    return null;
  }
}

/**
 * البحث عن جميع العناصر بالكلاس
 * @param {string} className - اسم الكلاس
 * @param {Element} parent - العنصر الأب (اختياري)
 * @returns {NodeList} قائمة العناصر
 */
export function getElementsByClass(className, parent = document) {
  try {
    return parent.querySelectorAll(`.${className}`);
  } catch (error) {
    console.warn(`فشل في العثور على العناصر بالكلاس: ${className}`, error);
    return [];
  }
}

/**
 * البحث عن عنصر بالمحدد
 * @param {string} selector - المحدد CSS
 * @param {Element} parent - العنصر الأب (اختياري)
 * @returns {Element|null} العنصر أو null
 */
export function querySelector(selector, parent = document) {
  try {
    return parent.querySelector(selector);
  } catch (error) {
    console.warn(`فشل في العثور على العنصر بالمحدد: ${selector}`, error);
    return null;
  }
}

/**
 * البحث عن جميع العناصر بالمحدد
 * @param {string} selector - المحدد CSS
 * @param {Element} parent - العنصر الأب (اختياري)
 * @returns {NodeList} قائمة العناصر
 */
export function querySelectorAll(selector, parent = document) {
  try {
    return parent.querySelectorAll(selector);
  } catch (error) {
    console.warn(`فشل في العثور على العناصر بالمحدد: ${selector}`, error);
    return [];
  }
}

/**
 * إنشاء عنصر HTML جديد
 * @param {string} tagName - اسم العلامة
 * @param {Object} attributes - الخصائص (اختياري)
 * @param {string} textContent - النص الداخلي (اختياري)
 * @returns {Element} العنصر الجديد
 */
export function createElement(tagName, attributes = {}, textContent = '') {
  try {
    const element = document.createElement(tagName);
    
    // إضافة الخصائص
    Object.entries(attributes).forEach(([key, value]) => {
      if (key === 'className' || key === 'class') {
        element.className = value;
      } else if (key === 'dataset') {
        Object.entries(value).forEach(([dataKey, dataValue]) => {
          element.dataset[dataKey] = dataValue;
        });
      } else if (key.startsWith('data-')) {
        element.setAttribute(key, value);
      } else {
        element[key] = value;
      }
    });
    
    // إضافة النص
    if (textContent) {
      element.textContent = textContent;
    }
    
    return element;
  } catch (error) {
    console.error(`فشل في إنشاء العنصر: ${tagName}`, error);
    return document.createElement('div'); // عنصر افتراضي
  }
}

/**
 * إنشاء عنصر من HTML string
 * @param {string} htmlString - نص HTML
 * @returns {Element|null} العنصر الجديد
 */
export function createElementFromHTML(htmlString) {
  try {
    const template = document.createElement('template');
    template.innerHTML = htmlString.trim();
    return template.content.firstChild;
  } catch (error) {
    console.error('فشل في إنشاء العنصر من HTML:', error);
    return null;
  }
}

/**
 * إنشاء Document Fragment
 * @returns {DocumentFragment} الـ fragment الجديد
 */
export function createFragment() {
  return document.createDocumentFragment();
}

/**
 * إضافة كلاس إلى عنصر
 * @param {Element} element - العنصر
 * @param {string|Array} className - اسم الكلاس أو مصفوفة أسماء
 */
export function addClass(element, className) {
  if (!element) return;
  
  try {
    if (Array.isArray(className)) {
      element.classList.add(...className);
    } else {
      element.classList.add(className);
    }
  } catch (error) {
    console.warn('فشل في إضافة الكلاس:', error);
  }
}

/**
 * إزالة كلاس من عنصر
 * @param {Element} element - العنصر
 * @param {string|Array} className - اسم الكلاس أو مصفوفة أسماء
 */
export function removeClass(element, className) {
  if (!element) return;
  
  try {
    if (Array.isArray(className)) {
      element.classList.remove(...className);
    } else {
      element.classList.remove(className);
    }
  } catch (error) {
    console.warn('فشل في إزالة الكلاس:', error);
  }
}

/**
 * تبديل كلاس في عنصر
 * @param {Element} element - العنصر
 * @param {string} className - اسم الكلاس
 * @param {boolean} force - إجبار الإضافة أو الإزالة (اختياري)
 * @returns {boolean} حالة الكلاس بعد التبديل
 */
export function toggleClass(element, className, force) {
  if (!element) return false;
  
  try {
    return element.classList.toggle(className, force);
  } catch (error) {
    console.warn('فشل في تبديل الكلاس:', error);
    return false;
  }
}

/**
 * التحقق من وجود كلاس في عنصر
 * @param {Element} element - العنصر
 * @param {string} className - اسم الكلاس
 * @returns {boolean} هل الكلاس موجود
 */
export function hasClass(element, className) {
  if (!element) return false;
  
  try {
    return element.classList.contains(className);
  } catch (error) {
    console.warn('فشل في التحقق من الكلاس:', error);
    return false;
  }
}

/**
 * تعيين خاصية لعنصر
 * @param {Element} element - العنصر
 * @param {string} attribute - اسم الخاصية
 * @param {string} value - قيمة الخاصية
 */
export function setAttribute(element, attribute, value) {
  if (!element) return;
  
  try {
    element.setAttribute(attribute, value);
  } catch (error) {
    console.warn('فشل في تعيين الخاصية:', error);
  }
}

/**
 * الحصول على خاصية من عنصر
 * @param {Element} element - العنصر
 * @param {string} attribute - اسم الخاصية
 * @returns {string|null} قيمة الخاصية
 */
export function getAttribute(element, attribute) {
  if (!element) return null;
  
  try {
    return element.getAttribute(attribute);
  } catch (error) {
    console.warn('فشل في الحصول على الخاصية:', error);
    return null;
  }
}

/**
 * إزالة خاصية من عنصر
 * @param {Element} element - العنصر
 * @param {string} attribute - اسم الخاصية
 */
export function removeAttribute(element, attribute) {
  if (!element) return;
  
  try {
    element.removeAttribute(attribute);
  } catch (error) {
    console.warn('فشل في إزالة الخاصية:', error);
  }
}

/**
 * تعيين نمط CSS لعنصر
 * @param {Element} element - العنصر
 * @param {string|Object} property - اسم الخاصية أو كائن الأنماط
 * @param {string} value - قيمة النمط (إذا كان property نص)
 */
export function setStyle(element, property, value) {
  if (!element) return;
  
  try {
    if (typeof property === 'object') {
      Object.entries(property).forEach(([prop, val]) => {
        element.style[prop] = val;
      });
    } else {
      element.style[property] = value;
    }
  } catch (error) {
    console.warn('فشل في تعيين النمط:', error);
  }
}

/**
 * الحصول على نمط CSS من عنصر
 * @param {Element} element - العنصر
 * @param {string} property - اسم الخاصية
 * @returns {string} قيمة النمط
 */
export function getStyle(element, property) {
  if (!element) return '';
  
  try {
    return window.getComputedStyle(element)[property];
  } catch (error) {
    console.warn('فشل في الحصول على النمط:', error);
    return '';
  }
}

/**
 * إضافة مستمع حدث إلى عنصر
 * @param {Element} element - العنصر
 * @param {string} event - نوع الحدث
 * @param {Function} handler - دالة المعالجة
 * @param {Object|boolean} options - خيارات المستمع
 */
export function addEventListener(element, event, handler, options = false) {
  if (!element || !handler) return;
  
  try {
    element.addEventListener(event, handler, options);
  } catch (error) {
    console.warn('فشل في إضافة مستمع الحدث:', error);
  }
}

/**
 * إزالة مستمع حدث من عنصر
 * @param {Element} element - العنصر
 * @param {string} event - نوع الحدث
 * @param {Function} handler - دالة المعالجة
 * @param {Object|boolean} options - خيارات المستمع
 */
export function removeEventListener(element, event, handler, options = false) {
  if (!element || !handler) return;
  
  try {
    element.removeEventListener(event, handler, options);
  } catch (error) {
    console.warn('فشل في إزالة مستمع الحدث:', error);
  }
}

/**
 * إطلاق حدث مخصص على عنصر
 * @param {Element} element - العنصر
 * @param {string} eventName - اسم الحدث
 * @param {*} detail - بيانات الحدث
 * @param {Object} options - خيارات الحدث
 */
export function dispatchEvent(element, eventName, detail = null, options = {}) {
  if (!element) return;
  
  try {
    const event = new CustomEvent(eventName, {
      detail,
      bubbles: options.bubbles !== false,
      cancelable: options.cancelable !== false,
      ...options
    });
    
    element.dispatchEvent(event);
  } catch (error) {
    console.warn('فشل في إطلاق الحدث:', error);
  }
}

/**
 * إضافة عنصر إلى عنصر آخر
 * @param {Element} parent - العنصر الأب
 * @param {Element|Array} child - العنصر الفرعي أو مصفوفة عناصر
 */
export function appendChild(parent, child) {
  if (!parent) return;
  
  try {
    if (Array.isArray(child)) {
      child.forEach(c => parent.appendChild(c));
    } else if (child) {
      parent.appendChild(child);
    }
  } catch (error) {
    console.warn('فشل في إضافة العنصر الفرعي:', error);
  }
}

/**
 * إدراج عنصر قبل عنصر آخر
 * @param {Element} parent - العنصر الأب
 * @param {Element} newElement - العنصر الجديد
 * @param {Element} referenceElement - العنصر المرجعي
 */
export function insertBefore(parent, newElement, referenceElement) {
  if (!parent || !newElement) return;
  
  try {
    parent.insertBefore(newElement, referenceElement);
  } catch (error) {
    console.warn('فشل في إدراج العنصر:', error);
  }
}

/**
 * إزالة عنصر من DOM
 * @param {Element} element - العنصر المراد إزالته
 */
export function removeElement(element) {
  if (!element) return;
  
  try {
    if (element.parentNode) {
      element.parentNode.removeChild(element);
    } else {
      element.remove();
    }
  } catch (error) {
    console.warn('فشل في إزالة العنصر:', error);
  }
}

/**
 * استبدال عنصر بآخر
 * @param {Element} oldElement - العنصر القديم
 * @param {Element} newElement - العنصر الجديد
 */
export function replaceElement(oldElement, newElement) {
  if (!oldElement || !newElement || !oldElement.parentNode) return;
  
  try {
    oldElement.parentNode.replaceChild(newElement, oldElement);
  } catch (error) {
    console.warn('فشل في استبدال العنصر:', error);
  }
}

/**
 * مسح محتوى عنصر
 * @param {Element} element - العنصر
 */
export function clearElement(element) {
  if (!element) return;
  
  try {
    element.innerHTML = '';
  } catch (error) {
    console.warn('فشل في مسح العنصر:', error);
  }
}

/**
 * الحصول على أبعاد عنصر
 * @param {Element} element - العنصر
 * @returns {Object} كائن يحتوي على الأبعاد
 */
export function getElementDimensions(element) {
  if (!element) return { width: 0, height: 0, top: 0, left: 0 };
  
  try {
    const rect = element.getBoundingClientRect();
    return {
      width: rect.width,
      height: rect.height,
      top: rect.top,
      left: rect.left,
      right: rect.right,
      bottom: rect.bottom
    };
  } catch (error) {
    console.warn('فشل في الحصول على أبعاد العنصر:', error);
    return { width: 0, height: 0, top: 0, left: 0 };
  }
}

/**
 * التحقق من رؤية عنصر في النافذة
 * @param {Element} element - العنصر
 * @param {number} threshold - نسبة الرؤية المطلوبة (0-1)
 * @returns {boolean} هل العنصر مرئي
 */
export function isElementVisible(element, threshold = 0) {
  if (!element) return false;
  
  try {
    const rect = element.getBoundingClientRect();
    const windowHeight = window.innerHeight || document.documentElement.clientHeight;
    const windowWidth = window.innerWidth || document.documentElement.clientWidth;
    
    const verticalVisible = rect.top < windowHeight && rect.bottom > 0;
    const horizontalVisible = rect.left < windowWidth && rect.right > 0;
    
    if (!verticalVisible || !horizontalVisible) return false;
    
    if (threshold > 0) {
      const visibleHeight = Math.min(rect.bottom, windowHeight) - Math.max(rect.top, 0);
      const visibleWidth = Math.min(rect.right, windowWidth) - Math.max(rect.left, 0);
      const visibleArea = visibleHeight * visibleWidth;
      const totalArea = rect.height * rect.width;
      
      return (visibleArea / totalArea) >= threshold;
    }
    
    return true;
  } catch (error) {
    console.warn('فشل في التحقق من رؤية العنصر:', error);
    return false;
  }
}

/**
 * التمرير إلى عنصر
 * @param {Element} element - العنصر
 * @param {Object} options - خيارات التمرير
 */
export function scrollToElement(element, options = {}) {
  if (!element) return;
  
  try {
    const defaultOptions = {
      behavior: 'smooth',
      block: 'start',
      inline: 'nearest'
    };
    
    element.scrollIntoView({ ...defaultOptions, ...options });
  } catch (error) {
    console.warn('فشل في التمرير إلى العنصر:', error);
  }
}

/**
 * تنظيف مستمعي الأحداث من عنصر
 * @param {Element} element - العنصر
 */
export function cleanupElement(element) {
  if (!element) return;
  
  try {
    // إزالة جميع مستمعي الأحداث (يتطلب تتبع يدوي)
    // هذه دالة مساعدة للتذكير بضرورة تنظيف المستمعين
    
    // مسح المراجع
    element._eventListeners = null;
    element._componentRef = null;
    
    console.log('تم تنظيف العنصر');
  } catch (error) {
    console.warn('فشل في تنظيف العنصر:', error);
  }
}

/**
 * تحويل NodeList إلى مصفوفة
 * @param {NodeList} nodeList - قائمة العقد
 * @returns {Array} مصفوفة العناصر
 */
export function nodeListToArray(nodeList) {
  try {
    return Array.from(nodeList);
  } catch (error) {
    console.warn('فشل في تحويل NodeList:', error);
    return [];
  }
}

/**
 * البحث عن أقرب عنصر أب بمحدد معين
 * @param {Element} element - العنصر
 * @param {string} selector - المحدد
 * @returns {Element|null} العنصر الأب أو null
 */
export function closest(element, selector) {
  if (!element) return null;
  
  try {
    return element.closest(selector);
  } catch (error) {
    console.warn('فشل في البحث عن العنصر الأب:', error);
    return null;
  }
}

// تصدير جميع الدوال
export default {
  getElementById,
  getElementByClass,
  getElementsByClass,
  querySelector,
  querySelectorAll,
  createElement,
  createElementFromHTML,
  createFragment,
  addClass,
  removeClass,
  toggleClass,
  hasClass,
  setAttribute,
  getAttribute,
  removeAttribute,
  setStyle,
  getStyle,
  addEventListener,
  removeEventListener,
  dispatchEvent,
  appendChild,
  insertBefore,
  removeElement,
  replaceElement,
  clearElement,
  getElementDimensions,
  isElementVisible,
  scrollToElement,
  cleanupElement,
  nodeListToArray,
  closest
};
