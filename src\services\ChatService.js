/**
 * خدمة المحادثات - ChatService
 * تدير عمليات المحادثات والرسائل
 * تتعامل مع إنشاء وتحديث وحذف المحادثات
 */

import apiClient from '../core/ApiClient.js';
import databaseManager from '../core/DatabaseManager.js';
import stateManager from '../core/StateManager.js';
import eventBus from '../core/EventBus.js';
import errorHandler from '../core/ErrorHandler.js';
import { CHAT_ENDPOINTS, MESSAGE_ENDPOINTS } from '../config/apiEndpoints.js';
import { CHATS, MESSAGES } from '../config/constants.js';
import ChatModel from '../models/ChatModel.js';
import MessageModel from '../models/MessageModel.js';

/**
 * كلاس خدمة المحادثات
 */
class ChatService {
  constructor() {
    // حالة الخدمة
    this.isInitialized = false;
    this.isOnline = navigator.onLine;
    
    // قوائم البيانات
    this.chats = new Map();
    this.messages = new Map();
    
    // إعدادات المزامنة
    this.syncEnabled = true;
    this.autoSync = true;
    this.syncInterval = 30000; // 30 ثانية
    this.syncTimer = null;
    
    // إعدادات التخزين المؤقت
    this.cacheEnabled = true;
    this.maxCachedMessages = 1000;
    
    // إعدادات التشخيص
    this.debug = false;
    
    // إحصائيات
    this.stats = {
      totalChats: 0,
      totalMessages: 0,
      unreadMessages: 0,
      lastSync: null,
      syncErrors: 0
    };
    
    console.log('💬 تم تهيئة خدمة المحادثات');
    
    // تهيئة المستمعين
    this._initializeEventListeners();
  }

  /**
   * تهيئة الخدمة
   * @returns {Promise<boolean>} نجحت التهيئة أم لا
   */
  async initialize() {
    if (this.isInitialized) {
      return true;
    }

    try {
      console.log('🚀 تهيئة خدمة المحادثات...');

      // تحميل البيانات من قاعدة البيانات المحلية
      await this._loadLocalData();

      // مزامنة مع الخادم إذا كان متصل
      if (this.isOnline && this.syncEnabled) {
        await this._syncWithServer();
      }

      // بدء المزامنة التلقائية
      if (this.autoSync) {
        this._startAutoSync();
      }

      this.isInitialized = true;

      // تحديث الحالة العامة
      this._updateGlobalState();

      console.log('✅ تم تهيئة خدمة المحادثات بنجاح');
      return true;

    } catch (error) {
      console.error('❌ فشل في تهيئة خدمة المحادثات:', error);
      
      errorHandler.handleError(error, {
        type: 'service',
        service: 'chat',
        operation: 'initialize'
      });

      return false;
    }
  }

  /**
   * الحصول على جميع المحادثات
   * @param {Object} options - خيارات الاستعلام
   * @returns {Promise<Array>} قائمة المحادثات
   */
  async getChats(options = {}) {
    try {
      let chats = Array.from(this.chats.values());

      // تطبيق الفلاتر
      if (options.archived !== undefined) {
        chats = chats.filter(chat => chat.isArchived === options.archived);
      }

      if (options.pinned !== undefined) {
        chats = chats.filter(chat => chat.isPinned === options.pinned);
      }

      if (options.type) {
        chats = chats.filter(chat => chat.type === options.type);
      }

      // ترتيب المحادثات
      chats.sort((a, b) => {
        // المثبتة أولاً
        if (a.isPinned && !b.isPinned) return -1;
        if (!a.isPinned && b.isPinned) return 1;
        
        // ثم حسب آخر نشاط
        return new Date(b.lastActivity) - new Date(a.lastActivity);
      });

      // تطبيق الحد الأقصى
      if (options.limit) {
        chats = chats.slice(0, options.limit);
      }

      return chats;

    } catch (error) {
      errorHandler.handleError(error, {
        type: 'service',
        service: 'chat',
        operation: 'getChats'
      });

      return [];
    }
  }

  /**
   * الحصول على محادثة بالمعرف
   * @param {string} chatId - معرف المحادثة
   * @returns {Promise<ChatModel|null>} المحادثة
   */
  async getChatById(chatId) {
    try {
      // البحث في التخزين المؤقت أولاً
      let chat = this.chats.get(chatId);
      
      if (!chat && this.isOnline) {
        // جلب من الخادم
        const response = await apiClient.get(CHAT_ENDPOINTS.GET_BY_ID(chatId));
        
        if (response.isSuccess()) {
          const chatData = response.getData();
          chat = new ChatModel(chatData);
          
          // حفظ في التخزين المؤقت وقاعدة البيانات
          await this._cacheChat(chat);
        }
      }

      return chat || null;

    } catch (error) {
      errorHandler.handleError(error, {
        type: 'service',
        service: 'chat',
        operation: 'getChatById',
        chatId
      });

      return null;
    }
  }

  /**
   * إنشاء محادثة جديدة
   * @param {Object} chatData - بيانات المحادثة
   * @returns {Promise<Object>} نتيجة الإنشاء
   */
  async createChat(chatData) {
    try {
      // التحقق من صحة البيانات
      const validation = this._validateChatData(chatData);
      if (!validation.isValid) {
        throw new Error(validation.errors.join(', '));
      }

      // إنشاء نموذج المحادثة
      const chat = new ChatModel(chatData);
      
      if (this.isOnline) {
        // إرسال إلى الخادم
        const response = await apiClient.post(CHAT_ENDPOINTS.CREATE, chat.toObject());
        
        if (response.isSuccess()) {
          const serverChat = new ChatModel(response.getData());
          await this._cacheChat(serverChat);
          
          eventBus.emit('chat:created', { chat: serverChat });
          
          return {
            success: true,
            chat: serverChat,
            message: 'تم إنشاء المحادثة بنجاح'
          };
        } else {
          throw new Error(response.getMessage());
        }
      } else {
        // حفظ محلياً للمزامنة لاحقاً
        chat._isLocal = true;
        chat._needsSync = true;
        
        await this._cacheChat(chat);
        
        return {
          success: true,
          chat,
          message: 'تم إنشاء المحادثة محلياً (ستتم المزامنة عند الاتصال)'
        };
      }

    } catch (error) {
      errorHandler.handleError(error, {
        type: 'service',
        service: 'chat',
        operation: 'createChat'
      });

      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * تحديث محادثة
   * @param {string} chatId - معرف المحادثة
   * @param {Object} updates - التحديثات
   * @returns {Promise<Object>} نتيجة التحديث
   */
  async updateChat(chatId, updates) {
    try {
      const chat = this.chats.get(chatId);
      if (!chat) {
        throw new Error('المحادثة غير موجودة');
      }

      // تطبيق التحديثات
      Object.assign(chat, updates);
      chat.updatedDate = new Date();
      chat._version++;

      if (this.isOnline) {
        // إرسال إلى الخادم
        const response = await apiClient.put(
          CHAT_ENDPOINTS.UPDATE(chatId), 
          updates
        );
        
        if (response.isSuccess()) {
          const serverChat = new ChatModel(response.getData());
          await this._cacheChat(serverChat);
          
          eventBus.emit('chat:updated', { chat: serverChat, updates });
          
          return {
            success: true,
            chat: serverChat,
            message: 'تم تحديث المحادثة بنجاح'
          };
        } else {
          throw new Error(response.getMessage());
        }
      } else {
        // حفظ محلياً
        chat._needsSync = true;
        await this._cacheChat(chat);
        
        return {
          success: true,
          chat,
          message: 'تم تحديث المحادثة محلياً'
        };
      }

    } catch (error) {
      errorHandler.handleError(error, {
        type: 'service',
        service: 'chat',
        operation: 'updateChat',
        chatId
      });

      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * حذف محادثة
   * @param {string} chatId - معرف المحادثة
   * @param {boolean} permanent - حذف نهائي
   * @returns {Promise<Object>} نتيجة الحذف
   */
  async deleteChat(chatId, permanent = false) {
    try {
      const chat = this.chats.get(chatId);
      if (!chat) {
        throw new Error('المحادثة غير موجودة');
      }

      if (this.isOnline) {
        // حذف من الخادم
        const response = await apiClient.delete(CHAT_ENDPOINTS.DELETE(chatId));
        
        if (response.isSuccess()) {
          // حذف من التخزين المحلي
          await this._removeChatFromCache(chatId);
          
          eventBus.emit('chat:deleted', { chatId, permanent });
          
          return {
            success: true,
            message: 'تم حذف المحادثة بنجاح'
          };
        } else {
          throw new Error(response.getMessage());
        }
      } else {
        // وضع علامة للحذف
        chat.isDeleted = true;
        chat.deletedDate = new Date();
        chat._needsSync = true;
        
        await this._cacheChat(chat);
        
        return {
          success: true,
          message: 'تم وضع علامة حذف على المحادثة'
        };
      }

    } catch (error) {
      errorHandler.handleError(error, {
        type: 'service',
        service: 'chat',
        operation: 'deleteChat',
        chatId
      });

      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * الحصول على رسائل محادثة
   * @param {string} chatId - معرف المحادثة
   * @param {Object} options - خيارات الاستعلام
   * @returns {Promise<Array>} قائمة الرسائل
   */
  async getMessages(chatId, options = {}) {
    try {
      // الحصول من التخزين المؤقت
      let messages = Array.from(this.messages.values())
        .filter(msg => msg.chatID === chatId && !msg.isDeleted);

      // جلب من الخادم إذا لزم الأمر
      if (this.isOnline && (!messages.length || options.refresh)) {
        const response = await apiClient.get(
          MESSAGE_ENDPOINTS.GET_BY_CHAT(chatId),
          { params: options }
        );
        
        if (response.isSuccess()) {
          const serverMessages = response.getData().map(msg => new MessageModel(msg));
          
          // حفظ في التخزين المؤقت
          for (const message of serverMessages) {
            await this._cacheMessage(message);
          }
          
          messages = serverMessages;
        }
      }

      // ترتيب الرسائل حسب التاريخ
      messages.sort((a, b) => new Date(a.createdDate) - new Date(b.createdDate));

      // تطبيق الحد الأقصى
      if (options.limit) {
        messages = messages.slice(-options.limit);
      }

      return messages;

    } catch (error) {
      errorHandler.handleError(error, {
        type: 'service',
        service: 'chat',
        operation: 'getMessages',
        chatId
      });

      return [];
    }
  }

  /**
   * إرسال رسالة
   * @param {string} chatId - معرف المحادثة
   * @param {Object} messageData - بيانات الرسالة
   * @returns {Promise<Object>} نتيجة الإرسال
   */
  async sendMessage(chatId, messageData) {
    try {
      // التحقق من وجود المحادثة
      const chat = this.chats.get(chatId);
      if (!chat) {
        throw new Error('المحادثة غير موجودة');
      }

      // إنشاء نموذج الرسالة
      const message = new MessageModel({
        ...messageData,
        chatID: chatId,
        direction: 'Outgoing',
        messageStatus: 'Sending'
      });

      // حفظ محلياً أولاً
      await this._cacheMessage(message);

      // إطلاق حدث الإرسال
      eventBus.emit('message:sending', { message });

      if (this.isOnline) {
        try {
          // إرسال إلى الخادم
          const response = await apiClient.post(
            MESSAGE_ENDPOINTS.SEND_TEXT,
            message.toObject()
          );
          
          if (response.isSuccess()) {
            const serverMessage = new MessageModel(response.getData());
            await this._cacheMessage(serverMessage);
            
            // تحديث المحادثة
            chat.updateLastMessage(serverMessage);
            await this._cacheChat(chat);
            
            eventBus.emit('message:sent', { message: serverMessage });
            
            return {
              success: true,
              message: serverMessage
            };
          } else {
            // تحديث حالة الرسالة إلى فاشلة
            message.updateStatus('Failed');
            await this._cacheMessage(message);
            
            throw new Error(response.getMessage());
          }
        } catch (error) {
          // تحديث حالة الرسالة إلى فاشلة
          message.updateStatus('Failed');
          await this._cacheMessage(message);
          
          throw error;
        }
      } else {
        // وضع علامة للمزامنة لاحقاً
        message._needsSync = true;
        await this._cacheMessage(message);
        
        return {
          success: true,
          message,
          offline: true
        };
      }

    } catch (error) {
      errorHandler.handleError(error, {
        type: 'service',
        service: 'chat',
        operation: 'sendMessage',
        chatId
      });

      eventBus.emit('message:failed', { 
        chatId, 
        messageData, 
        error: error.message 
      });

      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * تحديث حالة الرسالة
   * @param {string} messageId - معرف الرسالة
   * @param {string} status - الحالة الجديدة
   * @returns {Promise<boolean>} نجح التحديث أم لا
   */
  async updateMessageStatus(messageId, status) {
    try {
      const message = this.messages.get(messageId);
      if (!message) {
        return false;
      }

      message.updateStatus(status);
      await this._cacheMessage(message);

      if (this.isOnline) {
        await apiClient.put(
          MESSAGE_ENDPOINTS.UPDATE_STATUS(messageId),
          { status }
        );
      }

      eventBus.emit('message:status:updated', { messageId, status });
      
      return true;

    } catch (error) {
      errorHandler.handleError(error, {
        type: 'service',
        service: 'chat',
        operation: 'updateMessageStatus',
        messageId
      });

      return false;
    }
  }

  /**
   * وضع علامة قراءة على المحادثة
   * @param {string} chatId - معرف المحادثة
   * @returns {Promise<boolean>} نجح التحديث أم لا
   */
  async markChatAsRead(chatId) {
    try {
      const chat = this.chats.get(chatId);
      if (!chat) {
        return false;
      }

      // مسح عدد الرسائل غير المقروءة
      chat.clearUnreadCount();
      await this._cacheChat(chat);

      // تحديث حالة الرسائل إلى مقروءة
      const messages = Array.from(this.messages.values())
        .filter(msg => msg.chatID === chatId && msg.direction === 'Incoming');

      for (const message of messages) {
        if (message.messageStatus !== 'Read') {
          message.updateStatus('Read');
          await this._cacheMessage(message);
        }
      }

      if (this.isOnline) {
        await apiClient.post(CHAT_ENDPOINTS.MARK_READ(chatId));
      }

      eventBus.emit('chat:marked:read', { chatId });
      this._updateGlobalState();
      
      return true;

    } catch (error) {
      errorHandler.handleError(error, {
        type: 'service',
        service: 'chat',
        operation: 'markChatAsRead',
        chatId
      });

      return false;
    }
  }

  /**
   * البحث في المحادثات
   * @param {string} query - نص البحث
   * @param {Object} options - خيارات البحث
   * @returns {Promise<Array>} نتائج البحث
   */
  async searchChats(query, options = {}) {
    try {
      if (!query || query.trim().length < 2) {
        return [];
      }

      const searchTerm = query.toLowerCase().trim();
      const chats = Array.from(this.chats.values());

      const results = chats.filter(chat => {
        return chat.name.toLowerCase().includes(searchTerm) ||
               chat.description.toLowerCase().includes(searchTerm) ||
               (chat.lastMessage && chat.lastMessage.toLowerCase().includes(searchTerm));
      });

      return results.slice(0, options.limit || 20);

    } catch (error) {
      errorHandler.handleError(error, {
        type: 'service',
        service: 'chat',
        operation: 'searchChats'
      });

      return [];
    }
  }

  /**
   * البحث في الرسائل
   * @param {string} query - نص البحث
   * @param {string} chatId - معرف المحادثة (اختياري)
   * @param {Object} options - خيارات البحث
   * @returns {Promise<Array>} نتائج البحث
   */
  async searchMessages(query, chatId = null, options = {}) {
    try {
      if (!query || query.trim().length < 2) {
        return [];
      }

      const searchTerm = query.toLowerCase().trim();
      let messages = Array.from(this.messages.values());

      // فلترة حسب المحادثة إذا تم تحديدها
      if (chatId) {
        messages = messages.filter(msg => msg.chatID === chatId);
      }

      const results = messages.filter(msg => {
        return msg.messageText.toLowerCase().includes(searchTerm) &&
               !msg.isDeleted;
      });

      // ترتيب حسب التاريخ
      results.sort((a, b) => new Date(b.createdDate) - new Date(a.createdDate));

      return results.slice(0, options.limit || 50);

    } catch (error) {
      errorHandler.handleError(error, {
        type: 'service',
        service: 'chat',
        operation: 'searchMessages'
      });

      return [];
    }
  }

  /**
   * الحصول على إحصائيات الخدمة
   * @returns {Object} الإحصائيات
   */
  getStats() {
    const unreadCount = Array.from(this.chats.values())
      .reduce((total, chat) => total + chat.unreadCount, 0);

    return {
      ...this.stats,
      totalChats: this.chats.size,
      totalMessages: this.messages.size,
      unreadMessages: unreadCount,
      isOnline: this.isOnline,
      isInitialized: this.isInitialized
    };
  }

  /**
   * مزامنة مع الخادم
   * @returns {Promise<boolean>} نجحت المزامنة أم لا
   */
  async syncWithServer() {
    if (!this.isOnline || !this.syncEnabled) {
      return false;
    }

    return this._syncWithServer();
  }

  /**
   * تفعيل/إلغاء وضع التشخيص
   * @param {boolean} enabled - تفعيل التشخيص
   */
  setDebug(enabled) {
    this.debug = enabled;
    console.log(`🔍 وضع التشخيص لخدمة المحادثات: ${enabled ? 'مفعل' : 'معطل'}`);
  }

  // دوال خاصة

  /**
   * تهيئة مستمعي الأحداث
   * @private
   */
  _initializeEventListeners() {
    // مراقبة حالة الاتصال
    window.addEventListener('online', () => {
      this.isOnline = true;
      if (this.syncEnabled) {
        this._syncWithServer();
      }
    });

    window.addEventListener('offline', () => {
      this.isOnline = false;
    });

    // مراقبة أحداث المصادقة
    eventBus.on('auth:logout:success', () => {
      this._clearAllData();
    });
  }

  /**
   * تحميل البيانات المحلية
   * @private
   */
  async _loadLocalData() {
    try {
      // تحميل المحادثات
      const chats = await databaseManager.getAll('chats');
      for (const chatData of chats) {
        const chat = new ChatModel(chatData);
        this.chats.set(chat.id, chat);
      }

      // تحميل الرسائل
      const messages = await databaseManager.getAll('messages');
      for (const messageData of messages) {
        const message = new MessageModel(messageData);
        this.messages.set(message.id, message);
      }

      if (this.debug) {
        console.log(`📚 تم تحميل ${this.chats.size} محادثة و ${this.messages.size} رسالة`);
      }

    } catch (error) {
      console.error('❌ فشل في تحميل البيانات المحلية:', error);
    }
  }

  /**
   * مزامنة مع الخادم
   * @private
   */
  async _syncWithServer() {
    try {
      console.log('🔄 بدء المزامنة مع الخادم...');

      // مزامنة المحادثات
      await this._syncChats();

      // مزامنة الرسائل
      await this._syncMessages();

      this.stats.lastSync = new Date();
      
      eventBus.emit('chat:sync:completed', {
        timestamp: this.stats.lastSync
      });

      console.log('✅ تمت المزامنة بنجاح');
      return true;

    } catch (error) {
      this.stats.syncErrors++;
      
      console.error('❌ فشل في المزامنة:', error);
      
      eventBus.emit('chat:sync:failed', {
        error: error.message,
        timestamp: new Date()
      });

      return false;
    }
  }

  /**
   * مزامنة المحادثات
   * @private
   */
  async _syncChats() {
    const response = await apiClient.get(CHAT_ENDPOINTS.GET_ALL);
    
    if (response.isSuccess()) {
      const serverChats = response.getData();
      
      for (const chatData of serverChats) {
        const chat = new ChatModel(chatData);
        await this._cacheChat(chat);
      }
    }
  }

  /**
   * مزامنة الرسائل
   * @private
   */
  async _syncMessages() {
    // مزامنة الرسائل المحلية التي تحتاج مزامنة
    const localMessages = Array.from(this.messages.values())
      .filter(msg => msg._needsSync);

    for (const message of localMessages) {
      try {
        if (message.messageStatus === 'Sending') {
          // إرسال الرسالة
          const response = await apiClient.post(
            MESSAGE_ENDPOINTS.SEND_TEXT,
            message.toObject()
          );
          
          if (response.isSuccess()) {
            const serverMessage = new MessageModel(response.getData());
            await this._cacheMessage(serverMessage);
          }
        }
      } catch (error) {
        console.warn('⚠️ فشل في مزامنة الرسالة:', message.id, error.message);
      }
    }
  }

  /**
   * حفظ محادثة في التخزين المؤقت
   * @private
   */
  async _cacheChat(chat) {
    this.chats.set(chat.id, chat);
    
    // حفظ في قاعدة البيانات
    await databaseManager.update('chats', chat.id, chat.toObject(), true);
    
    // تحديث الحالة العامة
    this._updateGlobalState();
  }

  /**
   * حفظ رسالة في التخزين المؤقت
   * @private
   */
  async _cacheMessage(message) {
    this.messages.set(message.id, message);
    
    // حفظ في قاعدة البيانات
    await databaseManager.update('messages', message.id, message.toObject(), true);
    
    // إدارة حجم التخزين المؤقت
    if (this.messages.size > this.maxCachedMessages) {
      await this._cleanupOldMessages();
    }
  }

  /**
   * إزالة محادثة من التخزين المؤقت
   * @private
   */
  async _removeChatFromCache(chatId) {
    this.chats.delete(chatId);
    
    // حذف من قاعدة البيانات
    await databaseManager.delete('chats', chatId);
    
    // حذف الرسائل المرتبطة
    const chatMessages = Array.from(this.messages.values())
      .filter(msg => msg.chatID === chatId);
    
    for (const message of chatMessages) {
      this.messages.delete(message.id);
      await databaseManager.delete('messages', message.id);
    }
    
    this._updateGlobalState();
  }

  /**
   * تنظيف الرسائل القديمة
   * @private
   */
  async _cleanupOldMessages() {
    const messages = Array.from(this.messages.values());
    messages.sort((a, b) => new Date(a.createdDate) - new Date(b.createdDate));
    
    const toRemove = messages.slice(0, messages.length - this.maxCachedMessages);
    
    for (const message of toRemove) {
      this.messages.delete(message.id);
      await databaseManager.delete('messages', message.id);
    }
  }

  /**
   * تحديث الحالة العامة
   * @private
   */
  _updateGlobalState() {
    const chatsArray = Array.from(this.chats.values())
      .filter(chat => !chat.isDeleted)
      .map(chat => chat.toObject());

    stateManager.set('chats.list', chatsArray);
    stateManager.set('chats.stats', this.getStats());
  }

  /**
   * بدء المزامنة التلقائية
   * @private
   */
  _startAutoSync() {
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
    }

    this.syncTimer = setInterval(() => {
      if (this.isOnline && this.syncEnabled) {
        this._syncWithServer();
      }
    }, this.syncInterval);
  }

  /**
   * التحقق من صحة بيانات المحادثة
   * @private
   */
  _validateChatData(chatData) {
    const errors = [];

    if (!chatData.name || chatData.name.trim().length === 0) {
      errors.push('اسم المحادثة مطلوب');
    }

    if (chatData.name && chatData.name.length > CHATS.MAX_NAME_LENGTH) {
      errors.push(`اسم المحادثة يجب أن يكون أقل من ${CHATS.MAX_NAME_LENGTH} حرف`);
    }

    if (chatData.description && chatData.description.length > CHATS.MAX_DESCRIPTION_LENGTH) {
      errors.push(`وصف المحادثة يجب أن يكون أقل من ${CHATS.MAX_DESCRIPTION_LENGTH} حرف`);
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * مسح جميع البيانات
   * @private
   */
  async _clearAllData() {
    this.chats.clear();
    this.messages.clear();
    
    await databaseManager.clear('chats');
    await databaseManager.clear('messages');
    
    this._updateGlobalState();
  }
}

// إنشاء مثيل واحد من خدمة المحادثات
const chatService = new ChatService();

// تصدير المثيل والكلاس
export default chatService;
export { ChatService };
