/**
 * خدمة قاعدة البيانات المحسنة - DatabaseService
 * توفر واجهة محسنة للتعامل مع IndexedDB مع دعم للمعاملات والفهرسة
 * تدعم التخزين المؤقت والبحث المتقدم ومزامنة البيانات
 */

import eventBus from '../core/EventBus.js';
import stateManager from '../core/StateManager.js';
import errorHandler, { ERROR_TYPES, ERROR_SEVERITY } from '../core/ErrorHandler.js';

// إعدادات قاعدة البيانات
export const DB_CONFIG = {
  name: 'ChatAppCloneDB',
  version: 2,
  stores: {
    currentUser: {
      keyPath: 'id',
      autoIncrement: false,
      indexes: [
        { name: 'userName', keyPath: 'userName', unique: false },
        { name: 'phoneNumber', keyPath: 'phoneNumber', unique: true }
      ]
    },
    chats: {
      keyPath: 'id',
      autoIncrement: false,
      indexes: [
        { name: 'lastMessageDate', keyPath: 'lastMessageDate', unique: false },
        { name: 'type', keyPath: 'type', unique: false },
        { name: 'unreadCount', keyPath: 'unreadCount', unique: false },
        { name: 'name', keyPath: 'name', unique: false }
      ]
    },
    messages: {
      keyPath: 'locId',
      autoIncrement: false,
      indexes: [
        { name: 'chatID', keyPath: 'chatID', unique: false },
        { name: 'senderID', keyPath: 'senderID', unique: false },
        { name: 'createdDate', keyPath: 'createdDate', unique: false },
        { name: 'messageType', keyPath: 'messageType', unique: false },
        { name: 'messageStatus', keyPath: 'messageStatus', unique: false },
        { name: 'chatID_createdDate', keyPath: ['chatID', 'createdDate'], unique: false }
      ]
    },
    contacts: {
      keyPath: 'id',
      autoIncrement: false,
      indexes: [
        { name: 'userName', keyPath: 'userName', unique: false },
        { name: 'phoneNumber', keyPath: 'phoneNumber', unique: false }
      ]
    },
    users: {
      keyPath: 'id',
      autoIncrement: false,
      indexes: [
        { name: 'userName', keyPath: 'userName', unique: false },
        { name: 'phoneNumber', keyPath: 'phoneNumber', unique: false }
      ]
    },
    teams: {
      keyPath: 'id',
      autoIncrement: false,
      indexes: [
        { name: 'name', keyPath: 'name', unique: false },
        { name: 'companyID', keyPath: 'companyID', unique: false }
      ]
    },
    pageData: {
      keyPath: 'id',
      autoIncrement: false,
      indexes: []
    }
  }
};

// أنواع العمليات
export const OPERATION_TYPES = {
  CREATE: 'create',
  READ: 'read',
  UPDATE: 'update',
  DELETE: 'delete',
  SEARCH: 'search',
  BULK: 'bulk'
};

class DatabaseService {
  constructor() {
    // حالة قاعدة البيانات
    this.db = null;
    this.isInitialized = false;
    this.isInitializing = false;
    
    // إعدادات الخدمة
    this.config = {
      enableTransactions: true,
      enableCaching: true,
      cacheTimeout: 5 * 60 * 1000, // 5 دقائق
      maxCacheSize: 1000,
      enablePerformanceTracking: true,
      enableDataValidation: true
    };

    // التخزين المؤقت
    this.cache = new Map();
    this.cacheTimestamps = new Map();

    // إحصائيات الأداء
    this.stats = {
      totalOperations: 0,
      operationsByType: new Map(),
      operationsByStore: new Map(),
      averageOperationTime: 0,
      cacheHits: 0,
      cacheMisses: 0
    };

    // قائمة انتظار العمليات أثناء التهيئة
    this.pendingOperations = [];
  }

  /**
   * تهيئة قاعدة البيانات
   * @returns {Promise<boolean>} نجح التهيئة أم لا
   */
  async initialize() {
    if (this.isInitialized) {
      return true;
    }

    if (this.isInitializing) {
      // انتظار انتهاء التهيئة الجارية
      return new Promise((resolve) => {
        const checkInitialization = () => {
          if (this.isInitialized) {
            resolve(true);
          } else if (!this.isInitializing) {
            resolve(false);
          } else {
            setTimeout(checkInitialization, 100);
          }
        };
        checkInitialization();
      });
    }

    this.isInitializing = true;

    try {
      console.log('🗄️ بدء تهيئة قاعدة البيانات...');
      
      // فتح قاعدة البيانات
      this.db = await this._openDatabase();
      
      // تنفيذ العمليات المؤجلة
      await this._processPendingOperations();
      
      this.isInitialized = true;
      this.isInitializing = false;
      
      // إطلاق حدث التهيئة
      eventBus.emit('database:initialized');
      
      console.log('✅ تم تهيئة قاعدة البيانات بنجاح');
      
      return true;
    } catch (error) {
      this.isInitializing = false;
      
      errorHandler.handleError(error, {
        type: ERROR_TYPES.DATABASE,
        severity: ERROR_SEVERITY.CRITICAL,
        context: 'database_initialization'
      });
      
      console.error('❌ فشل في تهيئة قاعدة البيانات:', error);
      
      return false;
    }
  }

  /**
   * حفظ عنصر واحد
   * @param {string} storeName - اسم المخزن
   * @param {Object} item - العنصر المراد حفظه
   * @returns {Promise<Object>} العنصر المحفوظ
   */
  async save(storeName, item) {
    return this._executeOperation(OPERATION_TYPES.CREATE, storeName, async (store) => {
      // التحقق من صحة البيانات
      if (this.config.enableDataValidation) {
        this._validateItem(storeName, item);
      }

      // إضافة معرف محلي إذا لم يكن موجوداً
      if (!item.locId && storeName === 'messages') {
        item.locId = this._generateLocalId();
      }

      // إضافة طابع زمني
      item._savedAt = new Date();
      item._version = (item._version || 0) + 1;

      const request = store.put(item);
      
      return new Promise((resolve, reject) => {
        request.onsuccess = () => {
          // تحديث التخزين المؤقت
          this._updateCache(storeName, item);
          
          // إطلاق حدث الحفظ
          eventBus.emit('database:item:saved', {
            storeName,
            item,
            operation: 'save'
          });
          
          resolve(item);
        };
        
        request.onerror = () => reject(request.error);
      });
    });
  }

  /**
   * حفظ عدة عناصر
   * @param {string} storeName - اسم المخزن
   * @param {Array} items - العناصر المراد حفظها
   * @returns {Promise<Array>} العناصر المحفوظة
   */
  async saveMany(storeName, items) {
    return this._executeOperation(OPERATION_TYPES.BULK, storeName, async (store) => {
      const savedItems = [];
      
      // استخدام معاملة واحدة لجميع العمليات
      for (const item of items) {
        // التحقق من صحة البيانات
        if (this.config.enableDataValidation) {
          this._validateItem(storeName, item);
        }

        // إضافة معرف محلي إذا لم يكن موجوداً
        if (!item.locId && storeName === 'messages') {
          item.locId = this._generateLocalId();
        }

        // إضافة طابع زمني
        item._savedAt = new Date();
        item._version = (item._version || 0) + 1;

        const request = store.put(item);
        
        await new Promise((resolve, reject) => {
          request.onsuccess = () => {
            savedItems.push(item);
            this._updateCache(storeName, item);
            resolve();
          };
          request.onerror = () => reject(request.error);
        });
      }

      // إطلاق حدث الحفظ المتعدد
      eventBus.emit('database:items:saved', {
        storeName,
        items: savedItems,
        count: savedItems.length
      });

      return savedItems;
    });
  }

  /**
   * الحصول على عنصر بالمعرف
   * @param {string} storeName - اسم المخزن
   * @param {*} id - معرف العنصر
   * @returns {Promise<Object|null>} العنصر أو null
   */
  async getById(storeName, id) {
    // التحقق من التخزين المؤقت أولاً
    const cacheKey = `${storeName}:${id}`;
    const cachedItem = this._getCachedItem(cacheKey);
    
    if (cachedItem) {
      this.stats.cacheHits++;
      return cachedItem;
    }

    this.stats.cacheMisses++;

    return this._executeOperation(OPERATION_TYPES.READ, storeName, async (store) => {
      const request = store.get(id);
      
      return new Promise((resolve, reject) => {
        request.onsuccess = () => {
          const item = request.result;
          
          if (item) {
            this._setCachedItem(cacheKey, item);
          }
          
          resolve(item || null);
        };
        
        request.onerror = () => reject(request.error);
      });
    });
  }

  /**
   * الحصول على جميع العناصر من مخزن
   * @param {string} storeName - اسم المخزن
   * @param {Object} options - خيارات الاستعلام
   * @returns {Promise<Array>} قائمة العناصر
   */
  async getAll(storeName, options = {}) {
    return this._executeOperation(OPERATION_TYPES.READ, storeName, async (store) => {
      const {
        limit = null,
        offset = 0,
        sortBy = null,
        sortOrder = 'asc',
        filter = null
      } = options;

      let request;
      
      if (sortBy && store.indexNames.contains(sortBy)) {
        // استخدام الفهرس للترتيب
        const index = store.index(sortBy);
        request = index.openCursor(null, sortOrder === 'desc' ? 'prev' : 'next');
      } else {
        // استخدام المؤشر العادي
        request = store.openCursor();
      }

      const items = [];
      let currentOffset = 0;

      return new Promise((resolve, reject) => {
        request.onsuccess = (event) => {
          const cursor = event.target.result;
          
          if (!cursor) {
            resolve(items);
            return;
          }

          const item = cursor.value;
          
          // تطبيق المرشح إذا كان موجوداً
          if (filter && !filter(item)) {
            cursor.continue();
            return;
          }

          // تخطي العناصر حسب الإزاحة
          if (currentOffset < offset) {
            currentOffset++;
            cursor.continue();
            return;
          }

          // إضافة العنصر إلى النتائج
          items.push(item);

          // التحقق من الحد الأقصى
          if (limit && items.length >= limit) {
            resolve(items);
            return;
          }

          cursor.continue();
        };
        
        request.onerror = () => reject(request.error);
      });
    });
  }

  /**
   * البحث في مخزن باستخدام فهرس
   * @param {string} storeName - اسم المخزن
   * @param {string} indexName - اسم الفهرس
   * @param {*} value - القيمة المراد البحث عنها
   * @param {Object} options - خيارات البحث
   * @returns {Promise<Array>} نتائج البحث
   */
  async searchByIndex(storeName, indexName, value, options = {}) {
    return this._executeOperation(OPERATION_TYPES.SEARCH, storeName, async (store) => {
      if (!store.indexNames.contains(indexName)) {
        throw new Error(`الفهرس ${indexName} غير موجود في المخزن ${storeName}`);
      }

      const index = store.index(indexName);
      const {
        limit = null,
        exact = true,
        sortOrder = 'asc'
      } = options;

      let request;
      
      if (exact) {
        request = index.openCursor(IDBKeyRange.only(value), sortOrder === 'desc' ? 'prev' : 'next');
      } else {
        // بحث جزئي (يبدأ بـ)
        const range = IDBKeyRange.bound(value, value + '\uffff', false, true);
        request = index.openCursor(range, sortOrder === 'desc' ? 'prev' : 'next');
      }

      const results = [];

      return new Promise((resolve, reject) => {
        request.onsuccess = (event) => {
          const cursor = event.target.result;
          
          if (!cursor) {
            resolve(results);
            return;
          }

          results.push(cursor.value);

          if (limit && results.length >= limit) {
            resolve(results);
            return;
          }

          cursor.continue();
        };
        
        request.onerror = () => reject(request.error);
      });
    });
  }

  /**
   * البحث النصي المتقدم
   * @param {string} storeName - اسم المخزن
   * @param {string} searchTerm - مصطلح البحث
   * @param {Array} searchFields - الحقول المراد البحث فيها
   * @param {Object} options - خيارات البحث
   * @returns {Promise<Array>} نتائج البحث
   */
  async searchText(storeName, searchTerm, searchFields, options = {}) {
    const {
      limit = null,
      caseSensitive = false,
      exactMatch = false
    } = options;

    const normalizedTerm = caseSensitive ? searchTerm : searchTerm.toLowerCase();

    return this._executeOperation(OPERATION_TYPES.SEARCH, storeName, async (store) => {
      const request = store.openCursor();
      const results = [];

      return new Promise((resolve, reject) => {
        request.onsuccess = (event) => {
          const cursor = event.target.result;
          
          if (!cursor) {
            resolve(results);
            return;
          }

          const item = cursor.value;
          let matches = false;

          // البحث في الحقول المحددة
          for (const field of searchFields) {
            const fieldValue = this._getNestedValue(item, field);
            
            if (fieldValue) {
              const normalizedValue = caseSensitive ? 
                fieldValue.toString() : 
                fieldValue.toString().toLowerCase();

              if (exactMatch) {
                matches = normalizedValue === normalizedTerm;
              } else {
                matches = normalizedValue.includes(normalizedTerm);
              }

              if (matches) break;
            }
          }

          if (matches) {
            results.push(item);
            
            if (limit && results.length >= limit) {
              resolve(results);
              return;
            }
          }

          cursor.continue();
        };
        
        request.onerror = () => reject(request.error);
      });
    });
  }

  /**
   * تحديث عنصر
   * @param {string} storeName - اسم المخزن
   * @param {*} id - معرف العنصر
   * @param {Object} updates - التحديثات
   * @returns {Promise<Object|null>} العنصر المحدث
   */
  async update(storeName, id, updates) {
    return this._executeOperation(OPERATION_TYPES.UPDATE, storeName, async (store) => {
      // الحصول على العنصر الحالي
      const getRequest = store.get(id);
      
      const currentItem = await new Promise((resolve, reject) => {
        getRequest.onsuccess = () => resolve(getRequest.result);
        getRequest.onerror = () => reject(getRequest.error);
      });

      if (!currentItem) {
        return null;
      }

      // دمج التحديثات
      const updatedItem = {
        ...currentItem,
        ...updates,
        _updatedAt: new Date(),
        _version: (currentItem._version || 0) + 1
      };

      // التحقق من صحة البيانات
      if (this.config.enableDataValidation) {
        this._validateItem(storeName, updatedItem);
      }

      // حفظ العنصر المحدث
      const putRequest = store.put(updatedItem);
      
      return new Promise((resolve, reject) => {
        putRequest.onsuccess = () => {
          // تحديث التخزين المؤقت
          this._updateCache(storeName, updatedItem);
          
          // إطلاق حدث التحديث
          eventBus.emit('database:item:updated', {
            storeName,
            id,
            item: updatedItem,
            updates
          });
          
          resolve(updatedItem);
        };
        
        putRequest.onerror = () => reject(putRequest.error);
      });
    });
  }

  /**
   * حذف عنصر
   * @param {string} storeName - اسم المخزن
   * @param {*} id - معرف العنصر
   * @returns {Promise<boolean>} نجح الحذف أم لا
   */
  async delete(storeName, id) {
    return this._executeOperation(OPERATION_TYPES.DELETE, storeName, async (store) => {
      const request = store.delete(id);
      
      return new Promise((resolve, reject) => {
        request.onsuccess = () => {
          // إزالة من التخزين المؤقت
          this._removeCachedItem(`${storeName}:${id}`);
          
          // إطلاق حدث الحذف
          eventBus.emit('database:item:deleted', {
            storeName,
            id
          });
          
          resolve(true);
        };
        
        request.onerror = () => reject(request.error);
      });
    });
  }

  /**
   * مسح جميع البيانات من مخزن
   * @param {string} storeName - اسم المخزن
   * @returns {Promise<boolean>} نجح المسح أم لا
   */
  async clear(storeName) {
    return this._executeOperation(OPERATION_TYPES.DELETE, storeName, async (store) => {
      const request = store.clear();
      
      return new Promise((resolve, reject) => {
        request.onsuccess = () => {
          // مسح التخزين المؤقت المتعلق بهذا المخزن
          this._clearCacheForStore(storeName);
          
          // إطلاق حدث المسح
          eventBus.emit('database:store:cleared', { storeName });
          
          resolve(true);
        };
        
        request.onerror = () => reject(request.error);
      });
    });
  }

  /**
   * حذف قاعدة البيانات بالكامل
   * @returns {Promise<boolean>} نجح الحذف أم لا
   */
  async deleteDatabase() {
    try {
      if (this.db) {
        this.db.close();
        this.db = null;
      }

      this.isInitialized = false;
      
      const deleteRequest = indexedDB.deleteDatabase(DB_CONFIG.name);
      
      return new Promise((resolve, reject) => {
        deleteRequest.onsuccess = () => {
          // مسح جميع التخزين المؤقت
          this.cache.clear();
          this.cacheTimestamps.clear();
          
          // إطلاق حدث حذف قاعدة البيانات
          eventBus.emit('database:deleted');
          
          console.log('🗑️ تم حذف قاعدة البيانات');
          resolve(true);
        };
        
        deleteRequest.onerror = () => reject(deleteRequest.error);
      });
    } catch (error) {
      errorHandler.handleError(error, {
        type: ERROR_TYPES.DATABASE,
        severity: ERROR_SEVERITY.HIGH,
        context: 'delete_database'
      });
      
      return false;
    }
  }

  /**
   * الحصول على إحصائيات قاعدة البيانات
   * @returns {Promise<Object>} إحصائيات مفصلة
   */
  async getStats() {
    const storeStats = {};
    
    if (this.isInitialized) {
      for (const storeName of Object.keys(DB_CONFIG.stores)) {
        try {
          const count = await this._getStoreCount(storeName);
          storeStats[storeName] = count;
        } catch (error) {
          storeStats[storeName] = 0;
        }
      }
    }

    return {
      ...this.stats,
      storeStats,
      cacheSize: this.cache.size,
      isInitialized: this.isInitialized,
      dbVersion: DB_CONFIG.version
    };
  }

  /**
   * تنفيذ عملية قاعدة بيانات
   * @private
   */
  async _executeOperation(operationType, storeName, operation) {
    // التأكد من تهيئة قاعدة البيانات
    if (!this.isInitialized) {
      if (this.isInitializing) {
        // إضافة العملية إلى قائمة الانتظار
        return new Promise((resolve, reject) => {
          this.pendingOperations.push({
            operationType,
            storeName,
            operation,
            resolve,
            reject
          });
        });
      } else {
        throw new Error('قاعدة البيانات غير مهيأة');
      }
    }

    const startTime = performance.now();

    try {
      // إنشاء معاملة
      const transaction = this.db.transaction([storeName], 
        operationType === OPERATION_TYPES.READ ? 'readonly' : 'readwrite'
      );
      
      const store = transaction.objectStore(storeName);
      
      // تنفيذ العملية
      const result = await operation(store);
      
      // انتظار إكمال المعاملة
      await new Promise((resolve, reject) => {
        transaction.oncomplete = () => resolve();
        transaction.onerror = () => reject(transaction.error);
      });

      // تحديث الإحصائيات
      this._updateStats(operationType, storeName, performance.now() - startTime);

      return result;
    } catch (error) {
      errorHandler.handleError(error, {
        type: ERROR_TYPES.DATABASE,
        severity: ERROR_SEVERITY.HIGH,
        context: {
          operation: operationType,
          storeName,
          duration: performance.now() - startTime
        }
      });
      
      throw error;
    }
  }

  /**
   * فتح قاعدة البيانات
   * @private
   */
  async _openDatabase() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(DB_CONFIG.name, DB_CONFIG.version);

      request.onerror = () => reject(request.error);
      
      request.onsuccess = () => resolve(request.result);

      request.onupgradeneeded = (event) => {
        const db = event.target.result;
        
        // إنشاء المخازن والفهارس
        Object.entries(DB_CONFIG.stores).forEach(([storeName, storeConfig]) => {
          let store;
          
          if (db.objectStoreNames.contains(storeName)) {
            // حذف المخزن الموجود لإعادة إنشائه
            db.deleteObjectStore(storeName);
          }
          
          // إنشاء المخزن
          store = db.createObjectStore(storeName, {
            keyPath: storeConfig.keyPath,
            autoIncrement: storeConfig.autoIncrement
          });

          // إنشاء الفهارس
          storeConfig.indexes.forEach(indexConfig => {
            store.createIndex(
              indexConfig.name,
              indexConfig.keyPath,
              { unique: indexConfig.unique }
            );
          });
        });

        console.log('🔄 تم تحديث هيكل قاعدة البيانات');
      };
    });
  }

  /**
   * معالجة العمليات المؤجلة
   * @private
   */
  async _processPendingOperations() {
    const operations = [...this.pendingOperations];
    this.pendingOperations = [];

    for (const { operationType, storeName, operation, resolve, reject } of operations) {
      try {
        const result = await this._executeOperation(operationType, storeName, operation);
        resolve(result);
      } catch (error) {
        reject(error);
      }
    }
  }

  /**
   * التحقق من صحة العنصر
   * @private
   */
  _validateItem(storeName, item) {
    const storeConfig = DB_CONFIG.stores[storeName];
    
    if (!storeConfig) {
      throw new Error(`مخزن غير معروف: ${storeName}`);
    }

    // التحقق من وجود المفتاح الأساسي
    const keyPath = storeConfig.keyPath;
    if (!item[keyPath]) {
      throw new Error(`المفتاح الأساسي ${keyPath} مطلوب للمخزن ${storeName}`);
    }

    // التحقق من صحة البيانات حسب نوع المخزن
    switch (storeName) {
      case 'messages':
        if (!item.chatID || !item.senderID) {
          throw new Error('chatID و senderID مطلوبان للرسائل');
        }
        break;
      case 'chats':
        if (!item.name) {
          throw new Error('اسم المحادثة مطلوب');
        }
        break;
    }
  }

  /**
   * إنشاء معرف محلي فريد
   * @private
   */
  _generateLocalId() {
    return `local_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * الحصول على قيمة متداخلة من كائن
   * @private
   */
  _getNestedValue(obj, path) {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : null;
    }, obj);
  }

  /**
   * تحديث التخزين المؤقت
   * @private
   */
  _updateCache(storeName, item) {
    if (!this.config.enableCaching) return;

    const keyPath = DB_CONFIG.stores[storeName]?.keyPath;
    if (!keyPath || !item[keyPath]) return;

    const cacheKey = `${storeName}:${item[keyPath]}`;
    this._setCachedItem(cacheKey, item);
  }

  /**
   * الحصول على عنصر من التخزين المؤقت
   * @private
   */
  _getCachedItem(cacheKey) {
    if (!this.config.enableCaching || !this.cache.has(cacheKey)) {
      return null;
    }

    const timestamp = this.cacheTimestamps.get(cacheKey);
    const now = Date.now();

    if (now - timestamp > this.config.cacheTimeout) {
      this.cache.delete(cacheKey);
      this.cacheTimestamps.delete(cacheKey);
      return null;
    }

    return this.cache.get(cacheKey);
  }

  /**
   * حفظ عنصر في التخزين المؤقت
   * @private
   */
  _setCachedItem(cacheKey, item) {
    if (!this.config.enableCaching) return;

    // التحقق من حجم التخزين المؤقت
    if (this.cache.size >= this.config.maxCacheSize) {
      // إزالة أقدم العناصر
      const oldestKey = this.cache.keys().next().value;
      this.cache.delete(oldestKey);
      this.cacheTimestamps.delete(oldestKey);
    }

    this.cache.set(cacheKey, item);
    this.cacheTimestamps.set(cacheKey, Date.now());
  }

  /**
   * إزالة عنصر من التخزين المؤقت
   * @private
   */
  _removeCachedItem(cacheKey) {
    this.cache.delete(cacheKey);
    this.cacheTimestamps.delete(cacheKey);
  }

  /**
   * مسح التخزين المؤقت لمخزن معين
   * @private
   */
  _clearCacheForStore(storeName) {
    const keysToDelete = [];
    
    for (const key of this.cache.keys()) {
      if (key.startsWith(`${storeName}:`)) {
        keysToDelete.push(key);
      }
    }
    
    keysToDelete.forEach(key => {
      this.cache.delete(key);
      this.cacheTimestamps.delete(key);
    });
  }

  /**
   * الحصول على عدد العناصر في مخزن
   * @private
   */
  async _getStoreCount(storeName) {
    return this._executeOperation(OPERATION_TYPES.READ, storeName, async (store) => {
      const request = store.count();
      
      return new Promise((resolve, reject) => {
        request.onsuccess = () => resolve(request.result);
        request.onerror = () => reject(request.error);
      });
    });
  }

  /**
   * تحديث الإحصائيات
   * @private
   */
  _updateStats(operationType, storeName, duration) {
    if (!this.config.enablePerformanceTracking) return;

    this.stats.totalOperations++;
    
    // إحصائيات نوع العملية
    const currentTypeCount = this.stats.operationsByType.get(operationType) || 0;
    this.stats.operationsByType.set(operationType, currentTypeCount + 1);
    
    // إحصائيات المخزن
    const currentStoreCount = this.stats.operationsByStore.get(storeName) || 0;
    this.stats.operationsByStore.set(storeName, currentStoreCount + 1);
    
    // متوسط وقت العملية
    const totalOps = this.stats.totalOperations;
    const currentAverage = this.stats.averageOperationTime;
    this.stats.averageOperationTime = 
      (currentAverage * (totalOps - 1) + duration) / totalOps;
  }
}

// إنشاء مثيل عام للاستخدام في التطبيق
const databaseService = new DatabaseService();

// تصدير الكلاس والمثيل والثوابت
export { DatabaseService, databaseService, DB_CONFIG, OPERATION_TYPES };
export default databaseService;
