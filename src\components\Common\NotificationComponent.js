/**
 * مكون الإشعارات - NotificationComponent
 * يعرض إشعارات مختلفة الأنواع مع إمكانيات تخصيص متقدمة
 * يدعم الإشعارات المؤقتة والدائمة مع رسوم متحركة
 */

import BaseComponent from './BaseComponent.js';

// أنواع الإشعارات
export const NOTIFICATION_TYPES = {
  SUCCESS: 'success',
  ERROR: 'error',
  WARNING: 'warning',
  INFO: 'info',
  DEFAULT: 'default'
};

// مواضع الإشعارات
export const NOTIFICATION_POSITIONS = {
  TOP_RIGHT: 'top-right',
  TOP_LEFT: 'top-left',
  TOP_CENTER: 'top-center',
  BOTTOM_RIGHT: 'bottom-right',
  BOTTOM_LEFT: 'bottom-left',
  BOTTOM_CENTER: 'bottom-center',
  CENTER: 'center'
};

// أحجام الإشعارات
export const NOTIFICATION_SIZES = {
  SMALL: 'small',
  MEDIUM: 'medium',
  LARGE: 'large'
};

/**
 * كلاس مكون الإشعار
 */
export class NotificationComponent extends BaseComponent {
  constructor(options = {}) {
    super(options);
    
    // إعدادات الإشعار
    this.type = options.type || NOTIFICATION_TYPES.DEFAULT;
    this.title = options.title || '';
    this.message = options.message || '';
    this.icon = options.icon || null;
    this.image = options.image || null;
    
    // إعدادات العرض
    this.position = options.position || NOTIFICATION_POSITIONS.TOP_RIGHT;
    this.size = options.size || NOTIFICATION_SIZES.MEDIUM;
    this.autoClose = options.autoClose !== false;
    this.duration = options.duration || 5000; // 5 ثواني
    this.showCloseButton = options.showCloseButton !== false;
    this.showProgress = options.showProgress || false;
    
    // إعدادات التفاعل
    this.clickable = options.clickable || false;
    this.dismissible = options.dismissible !== false;
    this.pauseOnHover = options.pauseOnHover !== false;
    
    // إعدادات الرسوم المتحركة
    this.animation = options.animation || 'slide';
    this.animationDuration = options.animationDuration || 300;
    
    // الإجراءات
    this.actions = options.actions || [];
    this.onClose = options.onClose || null;
    this.onClick = options.onClick || null;
    
    // حالة الإشعار
    this.isShown = false;
    this.isPaused = false;
    this.timeoutId = null;
    this.progressInterval = null;
    this.remainingTime = this.duration;
    this.startTime = null;
  }

  /**
   * الحصول على قالب HTML
   * @returns {string} قالب HTML
   */
  getTemplate() {
    const typeClass = `notification-${this.type}`;
    const sizeClass = `notification-${this.size}`;
    const positionClass = `notification-${this.position}`;
    
    return `
      <div class="notification-component ${typeClass} ${sizeClass} ${positionClass}" 
           data-type="${this.type}" 
           data-position="${this.position}"
           role="alert"
           aria-live="polite">
        
        ${this._getIconElement()}
        
        <div class="notification-content">
          ${this.title ? `<div class="notification-title">${this.title}</div>` : ''}
          ${this.message ? `<div class="notification-message">${this.message}</div>` : ''}
          ${this.actions.length > 0 ? this._getActionsElement() : ''}
        </div>
        
        ${this.image ? `<div class="notification-image"><img src="${this.image}" alt="صورة الإشعار"></div>` : ''}
        
        ${this.showCloseButton ? this._getCloseButtonElement() : ''}
        
        ${this.showProgress ? this._getProgressElement() : ''}
      </div>
    `;
  }

  /**
   * عرض مخصص
   */
  async onRender() {
    this._applyStyles();
    this._bindEvents();
    
    // تطبيق الرسوم المتحركة
    this._applyAnimation('enter');
    
    // بدء العد التنازلي
    if (this.autoClose) {
      this._startAutoClose();
    }
    
    // بدء شريط التقدم
    if (this.showProgress) {
      this._startProgress();
    }
    
    this.isShown = true;
    this.emit('notification:shown');
  }

  /**
   * إظهار الإشعار
   */
  show() {
    if (!this.isShown) {
      this.render();
    }
  }

  /**
   * إخفاء الإشعار
   * @param {boolean} animate - تطبيق الرسوم المتحركة
   */
  hide(animate = true) {
    if (!this.isShown) return;

    this._stopAutoClose();
    this._stopProgress();
    
    if (animate) {
      this._applyAnimation('exit').then(() => {
        this._removeFromDOM();
      });
    } else {
      this._removeFromDOM();
    }
    
    this.isShown = false;
    this.emit('notification:hidden');
    
    // استدعاء callback الإغلاق
    if (this.onClose && typeof this.onClose === 'function') {
      this.onClose(this);
    }
  }

  /**
   * تحديث محتوى الإشعار
   * @param {Object} updates - التحديثات
   */
  updateContent(updates) {
    if (updates.title !== undefined) {
      this.title = updates.title;
      this._updateTitle();
    }
    
    if (updates.message !== undefined) {
      this.message = updates.message;
      this._updateMessage();
    }
    
    if (updates.type !== undefined) {
      this._updateType(updates.type);
    }
    
    if (updates.icon !== undefined) {
      this.icon = updates.icon;
      this._updateIcon();
    }
    
    this.emit('notification:updated', updates);
  }

  /**
   * إيقاف مؤقت للعد التنازلي
   */
  pause() {
    if (this.autoClose && !this.isPaused) {
      this.isPaused = true;
      this._stopAutoClose();
      this._stopProgress();
      
      this.emit('notification:paused');
    }
  }

  /**
   * استئناف العد التنازلي
   */
  resume() {
    if (this.autoClose && this.isPaused) {
      this.isPaused = false;
      this._startAutoClose();
      
      if (this.showProgress) {
        this._startProgress();
      }
      
      this.emit('notification:resumed');
    }
  }

  /**
   * إعادة تعيين المؤقت
   */
  resetTimer() {
    this.remainingTime = this.duration;
    
    if (this.autoClose && this.isShown) {
      this._stopAutoClose();
      this._startAutoClose();
      
      if (this.showProgress) {
        this._stopProgress();
        this._startProgress();
      }
    }
    
    this.emit('notification:timer:reset');
  }

  /**
   * الحصول على عنصر الأيقونة
   * @private
   */
  _getIconElement() {
    let iconContent = '';
    
    if (this.icon) {
      iconContent = this.icon;
    } else {
      // أيقونات افتراضية حسب النوع
      switch (this.type) {
        case NOTIFICATION_TYPES.SUCCESS:
          iconContent = '✓';
          break;
        case NOTIFICATION_TYPES.ERROR:
          iconContent = '✕';
          break;
        case NOTIFICATION_TYPES.WARNING:
          iconContent = '⚠';
          break;
        case NOTIFICATION_TYPES.INFO:
          iconContent = 'ℹ';
          break;
        default:
          iconContent = '●';
      }
    }
    
    return `<div class="notification-icon">${iconContent}</div>`;
  }

  /**
   * الحصول على عنصر الإجراءات
   * @private
   */
  _getActionsElement() {
    const actionsHtml = this.actions.map(action => `
      <button class="notification-action" data-action="${action.id}">
        ${action.label}
      </button>
    `).join('');
    
    return `<div class="notification-actions">${actionsHtml}</div>`;
  }

  /**
   * الحصول على زر الإغلاق
   * @private
   */
  _getCloseButtonElement() {
    return `
      <button class="notification-close" aria-label="إغلاق الإشعار">
        <span aria-hidden="true">×</span>
      </button>
    `;
  }

  /**
   * الحصول على شريط التقدم
   * @private
   */
  _getProgressElement() {
    return `
      <div class="notification-progress">
        <div class="progress-bar"></div>
      </div>
    `;
  }

  /**
   * تطبيق الأنماط
   * @private
   */
  _applyStyles() {
    if (!document.querySelector('#notification-styles')) {
      const style = document.createElement('style');
      style.id = 'notification-styles';
      style.textContent = this._getStyles();
      document.head.appendChild(style);
    }
  }

  /**
   * الحصول على أنماط CSS
   * @private
   */
  _getStyles() {
    return `
      .notification-component {
        position: fixed;
        z-index: 10000;
        max-width: 400px;
        min-width: 300px;
        background: white;
        border-radius: 8px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        display: flex;
        align-items: flex-start;
        gap: 12px;
        padding: 16px;
        margin: 8px;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        font-size: 14px;
        line-height: 1.4;
        border-left: 4px solid #007bff;
        transition: all 0.3s ease;
      }

      /* مواضع الإشعارات */
      .notification-top-right { top: 20px; right: 20px; }
      .notification-top-left { top: 20px; left: 20px; }
      .notification-top-center { top: 20px; left: 50%; transform: translateX(-50%); }
      .notification-bottom-right { bottom: 20px; right: 20px; }
      .notification-bottom-left { bottom: 20px; left: 20px; }
      .notification-bottom-center { bottom: 20px; left: 50%; transform: translateX(-50%); }
      .notification-center { 
        top: 50%; 
        left: 50%; 
        transform: translate(-50%, -50%); 
      }

      /* أحجام الإشعارات */
      .notification-small {
        min-width: 250px;
        max-width: 300px;
        padding: 12px;
        font-size: 13px;
      }

      .notification-medium {
        min-width: 300px;
        max-width: 400px;
        padding: 16px;
        font-size: 14px;
      }

      .notification-large {
        min-width: 400px;
        max-width: 500px;
        padding: 20px;
        font-size: 15px;
      }

      /* أنواع الإشعارات */
      .notification-success {
        border-left-color: #28a745;
        background: linear-gradient(135deg, #f8fff9 0%, #f0fff4 100%);
      }

      .notification-error {
        border-left-color: #dc3545;
        background: linear-gradient(135deg, #fff8f8 0%, #fff0f0 100%);
      }

      .notification-warning {
        border-left-color: #ffc107;
        background: linear-gradient(135deg, #fffef8 0%, #fffbf0 100%);
      }

      .notification-info {
        border-left-color: #17a2b8;
        background: linear-gradient(135deg, #f8fcff 0%, #f0f9ff 100%);
      }

      /* الأيقونة */
      .notification-icon {
        flex-shrink: 0;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 14px;
        color: white;
      }

      .notification-success .notification-icon { background: #28a745; }
      .notification-error .notification-icon { background: #dc3545; }
      .notification-warning .notification-icon { background: #ffc107; color: #333; }
      .notification-info .notification-icon { background: #17a2b8; }
      .notification-default .notification-icon { background: #6c757d; }

      /* المحتوى */
      .notification-content {
        flex: 1;
        min-width: 0;
      }

      .notification-title {
        font-weight: 600;
        margin-bottom: 4px;
        color: #333;
      }

      .notification-message {
        color: #666;
        word-wrap: break-word;
      }

      /* الإجراءات */
      .notification-actions {
        display: flex;
        gap: 8px;
        margin-top: 12px;
      }

      .notification-action {
        padding: 6px 12px;
        border: 1px solid #ddd;
        background: white;
        border-radius: 4px;
        cursor: pointer;
        font-size: 12px;
        transition: all 0.2s ease;
      }

      .notification-action:hover {
        background: #f8f9fa;
        border-color: #adb5bd;
      }

      /* الصورة */
      .notification-image {
        flex-shrink: 0;
        width: 60px;
        height: 60px;
        border-radius: 6px;
        overflow: hidden;
      }

      .notification-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      /* زر الإغلاق */
      .notification-close {
        position: absolute;
        top: 8px;
        right: 8px;
        background: none;
        border: none;
        font-size: 18px;
        cursor: pointer;
        color: #999;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        transition: all 0.2s ease;
      }

      .notification-close:hover {
        background: rgba(0, 0, 0, 0.1);
        color: #666;
      }

      /* شريط التقدم */
      .notification-progress {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: rgba(0, 0, 0, 0.1);
        border-radius: 0 0 8px 8px;
        overflow: hidden;
      }

      .notification-progress .progress-bar {
        height: 100%;
        background: #007bff;
        width: 100%;
        transition: width linear;
      }

      .notification-success .progress-bar { background: #28a745; }
      .notification-error .progress-bar { background: #dc3545; }
      .notification-warning .progress-bar { background: #ffc107; }
      .notification-info .progress-bar { background: #17a2b8; }

      /* الرسوم المتحركة */
      .notification-enter {
        animation: notificationSlideIn 0.3s ease-out;
      }

      .notification-exit {
        animation: notificationSlideOut 0.3s ease-in;
      }

      @keyframes notificationSlideIn {
        from {
          transform: translateX(100%);
          opacity: 0;
        }
        to {
          transform: translateX(0);
          opacity: 1;
        }
      }

      @keyframes notificationSlideOut {
        from {
          transform: translateX(0);
          opacity: 1;
        }
        to {
          transform: translateX(100%);
          opacity: 0;
        }
      }

      /* تأثيرات التفاعل */
      .notification-component:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 25px rgba(0, 0, 0, 0.2);
      }

      .notification-component.clickable {
        cursor: pointer;
      }

      .notification-component.clickable:hover {
        transform: translateY(-3px);
      }

      /* التصميم المتجاوب */
      @media (max-width: 480px) {
        .notification-component {
          left: 10px !important;
          right: 10px !important;
          max-width: none;
          min-width: auto;
          margin: 4px;
        }
        
        .notification-top-center,
        .notification-bottom-center {
          transform: none;
        }
      }
    `;
  }

  /**
   * ربط الأحداث
   * @private
   */
  _bindEvents() {
    if (!this.element) return;

    // زر الإغلاق
    const closeButton = this.element.querySelector('.notification-close');
    if (closeButton) {
      this.addEventListener(closeButton, 'click', (e) => {
        e.stopPropagation();
        this.hide();
      });
    }

    // الإجراءات
    const actionButtons = this.element.querySelectorAll('.notification-action');
    actionButtons.forEach(button => {
      this.addEventListener(button, 'click', (e) => {
        e.stopPropagation();
        const actionId = button.getAttribute('data-action');
        const action = this.actions.find(a => a.id === actionId);
        
        if (action && action.handler) {
          action.handler(this);
        }
        
        this.emit('notification:action:clicked', { actionId, action });
      });
    });

    // النقر على الإشعار
    if (this.clickable) {
      this.element.classList.add('clickable');
      this.addEventListener(this.element, 'click', () => {
        if (this.onClick && typeof this.onClick === 'function') {
          this.onClick(this);
        }
        this.emit('notification:clicked');
      });
    }

    // إيقاف مؤقت عند التمرير
    if (this.pauseOnHover) {
      this.addEventListener(this.element, 'mouseenter', () => {
        this.pause();
      });

      this.addEventListener(this.element, 'mouseleave', () => {
        this.resume();
      });
    }

    // إغلاق بالضغط على Escape
    if (this.dismissible) {
      this.addEventListener(document, 'keydown', (e) => {
        if (e.key === 'Escape') {
          this.hide();
        }
      });
    }
  }

  /**
   * تطبيق الرسوم المتحركة
   * @private
   */
  _applyAnimation(type) {
    return new Promise((resolve) => {
      if (!this.element) {
        resolve();
        return;
      }

      const className = `notification-${type}`;
      this.element.classList.add(className);

      setTimeout(() => {
        this.element.classList.remove(className);
        resolve();
      }, this.animationDuration);
    });
  }

  /**
   * بدء العد التنازلي للإغلاق التلقائي
   * @private
   */
  _startAutoClose() {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
    }

    this.startTime = Date.now();
    this.timeoutId = setTimeout(() => {
      this.hide();
    }, this.remainingTime);
  }

  /**
   * إيقاف العد التنازلي
   * @private
   */
  _stopAutoClose() {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
      
      // حساب الوقت المتبقي
      if (this.startTime) {
        const elapsed = Date.now() - this.startTime;
        this.remainingTime = Math.max(0, this.remainingTime - elapsed);
      }
    }
  }

  /**
   * بدء شريط التقدم
   * @private
   */
  _startProgress() {
    const progressBar = this.element?.querySelector('.progress-bar');
    if (!progressBar) return;

    progressBar.style.transition = `width ${this.remainingTime}ms linear`;
    progressBar.style.width = '0%';
  }

  /**
   * إيقاف شريط التقدم
   * @private
   */
  _stopProgress() {
    const progressBar = this.element?.querySelector('.progress-bar');
    if (!progressBar) return;

    const currentWidth = progressBar.getBoundingClientRect().width;
    const totalWidth = progressBar.parentElement.getBoundingClientRect().width;
    const percentage = (currentWidth / totalWidth) * 100;
    
    progressBar.style.transition = 'none';
    progressBar.style.width = `${percentage}%`;
  }

  /**
   * تحديث العنوان
   * @private
   */
  _updateTitle() {
    const titleElement = this.element?.querySelector('.notification-title');
    if (titleElement) {
      titleElement.textContent = this.title;
    }
  }

  /**
   * تحديث الرسالة
   * @private
   */
  _updateMessage() {
    const messageElement = this.element?.querySelector('.notification-message');
    if (messageElement) {
      messageElement.textContent = this.message;
    }
  }

  /**
   * تحديث النوع
   * @private
   */
  _updateType(newType) {
    if (!this.element || !Object.values(NOTIFICATION_TYPES).includes(newType)) {
      return;
    }

    // إزالة الكلاس القديم
    Object.values(NOTIFICATION_TYPES).forEach(type => {
      this.element.classList.remove(`notification-${type}`);
    });

    // إضافة الكلاس الجديد
    this.type = newType;
    this.element.classList.add(`notification-${newType}`);
    this.element.setAttribute('data-type', newType);
  }

  /**
   * تحديث الأيقونة
   * @private
   */
  _updateIcon() {
    const iconElement = this.element?.querySelector('.notification-icon');
    if (iconElement) {
      iconElement.innerHTML = this.icon || this._getDefaultIcon();
    }
  }

  /**
   * الحصول على الأيقونة الافتراضية
   * @private
   */
  _getDefaultIcon() {
    switch (this.type) {
      case NOTIFICATION_TYPES.SUCCESS: return '✓';
      case NOTIFICATION_TYPES.ERROR: return '✕';
      case NOTIFICATION_TYPES.WARNING: return '⚠';
      case NOTIFICATION_TYPES.INFO: return 'ℹ';
      default: return '●';
    }
  }

  /**
   * إزالة من DOM
   * @private
   */
  _removeFromDOM() {
    if (this.element && this.element.parentNode) {
      this.element.parentNode.removeChild(this.element);
    }
  }

  /**
   * تدمير مخصص
   */
  onDestroy() {
    this._stopAutoClose();
    this._stopProgress();
  }

  /**
   * إنشاء إشعار سريع
   * @static
   * @param {string} type - نوع الإشعار
   * @param {string} message - رسالة الإشعار
   * @param {Object} options - خيارات إضافية
   * @returns {NotificationComponent} الإشعار
   */
  static create(type, message, options = {}) {
    const notification = new NotificationComponent({
      type,
      message,
      ...options
    });
    
    notification.initialize();
    return notification;
  }

  /**
   * إظهار إشعار نجاح
   * @static
   */
  static success(message, options = {}) {
    return NotificationComponent.create(NOTIFICATION_TYPES.SUCCESS, message, options);
  }

  /**
   * إظهار إشعار خطأ
   * @static
   */
  static error(message, options = {}) {
    return NotificationComponent.create(NOTIFICATION_TYPES.ERROR, message, options);
  }

  /**
   * إظهار إشعار تحذير
   * @static
   */
  static warning(message, options = {}) {
    return NotificationComponent.create(NOTIFICATION_TYPES.WARNING, message, options);
  }

  /**
   * إظهار إشعار معلومات
   * @static
   */
  static info(message, options = {}) {
    return NotificationComponent.create(NOTIFICATION_TYPES.INFO, message, options);
  }
}

export default NotificationComponent;
export { NOTIFICATION_TYPES, NOTIFICATION_POSITIONS, NOTIFICATION_SIZES };
