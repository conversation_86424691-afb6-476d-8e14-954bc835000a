/**
 * إعدادات التطبيق الرئيسية - AppConfig
 * يحتوي على جميع الإعدادات والثوابت المستخدمة في التطبيق
 * يدعم البيئات المختلفة والتخصيص المرن
 */

// البيئات المختلفة
export const ENVIRONMENTS = {
  DEVELOPMENT: 'development',
  STAGING: 'staging',
  PRODUCTION: 'production'
};

// البيئة الحالية (يمكن تغييرها حسب الحاجة)
export const CURRENT_ENVIRONMENT = ENVIRONMENTS.DEVELOPMENT;

// إعدادات API حسب البيئة
export const API_CONFIG = {
  [ENVIRONMENTS.DEVELOPMENT]: {
    baseUrl: 'http://localhost:5236/',
    signalRUrl: 'http://localhost:5236',
    timeout: 30000,
    retryAttempts: 3,
    enableLogging: true
  },
  [ENVIRONMENTS.STAGING]: {
    baseUrl: 'http://*************:3000/chatproject/',
    signalRUrl: 'http://*************:3000/chatproject',
    timeout: 30000,
    retryAttempts: 3,
    enableLogging: true
  },
  [ENVIRONMENTS.PRODUCTION]: {
    baseUrl: 'http://*************:3000/chatproject/',
    signalRUrl: 'http://*************:3000/chatproject',
    timeout: 30000,
    retryAttempts: 2,
    enableLogging: false
  }
};

// نقاط النهاية للAPI
export const API_ENDPOINTS = {
  // المصادقة
  AUTH: {
    PROFILE: 'Authentication/profile',
    LOGIN: 'Authentication/Login',
    LOGOUT: 'Authentication/Logout',
    REFRESH_TOKEN: 'Authentication/RefreshToken'
  },
  
  // المحادثات
  CHAT: {
    GET_ALL: 'Chat/GetAllChats',
    GET_BY_ID: (chatId) => `Chat/GetChat/${chatId}`,
    CREATE: 'Chat/CreateChat',
    UPDATE: (chatId) => `Chat/UpdateChat/${chatId}`,
    DELETE: (chatId) => `Chat/DeleteChat/${chatId}`,
    MARK_READ: (chatId) => `Chat/MakeRead/${chatId}`,
    GET_MEMBERS: (chatId) => `Chat/GetChatMembers/${chatId}`,
    ADD_MEMBER: (chatId) => `Chat/AddMember/${chatId}`,
    REMOVE_MEMBER: (chatId) => `Chat/RemoveMember/${chatId}`
  },
  
  // الرسائل
  MESSAGE: {
    SEND_TEXT: 'Message/CreateTextMessage',
    SEND_FILE: 'Message/CreateFileMessage',
    SEND_VOICE: 'Message/CreateVoiceMessage',
    UPDATE: (messageId) => `Message/UpdateMessage/${messageId}`,
    DELETE: (messageId) => `Message/DeleteMessage/${messageId}`,
    GET_BY_CHAT: (chatId) => `Message/GetMessagesByChat/${chatId}`,
    MARK_READ: (messageId) => `Message/MarkAsRead/${messageId}`
  },
  
  // جهات الاتصال
  CONTACT: {
    GET_ALL: 'Contact/GetAllContacts',
    GET_BY_ID: (contactId) => `Contact/GetContact/${contactId}`,
    ADD: 'Contact/AddContact',
    UPDATE: (contactId) => `Contact/UpdateContact/${contactId}`,
    DELETE: (contactId) => `Contact/DeleteContact/${contactId}`,
    SEARCH: 'Contact/SearchContacts'
  },
  
  // المستخدمين
  USER: {
    GET_ALL: 'User/GetAllUsers',
    GET_BY_ID: (userId) => `User/GetUser/${userId}`,
    UPDATE_PROFILE: 'User/UpdateProfile',
    UPDATE_STATUS: 'User/UpdateStatus',
    SEARCH: 'User/SearchUsers'
  },
  
  // الفرق
  TEAM: {
    GET_ALL: 'Team/GetAllTeams',
    GET_BY_ID: (teamId) => `Team/GetTeam/${teamId}`,
    CREATE: 'Team/CreateTeam',
    UPDATE: (teamId) => `Team/UpdateTeam/${teamId}`,
    DELETE: (teamId) => `Team/DeleteTeam/${teamId}`,
    GET_MEMBERS: (teamId) => `Team/GetTeamMembers/${teamId}`,
    ADD_MEMBER: (teamId) => `Team/AddMember/${teamId}`,
    REMOVE_MEMBER: (teamId) => `Team/RemoveMember/${teamId}`
  },
  
  // الملفات
  FILE: {
    UPLOAD: 'File/Upload',
    DOWNLOAD: (fileId) => `File/Download/${fileId}`,
    DELETE: (fileId) => `File/Delete/${fileId}`,
    GET_INFO: (fileId) => `File/GetInfo/${fileId}`
  }
};

// إعدادات قاعدة البيانات المحلية
export const DATABASE_CONFIG = {
  name: 'ChatAppCloneDB',
  version: 2,
  enableCaching: true,
  cacheTimeout: 5 * 60 * 1000, // 5 دقائق
  maxCacheSize: 1000,
  enablePerformanceTracking: true
};

// إعدادات SignalR
export const SIGNALR_CONFIG = {
  enableAutoReconnect: true,
  reconnectAttempts: 5,
  reconnectDelay: 2000,
  enableLogging: CURRENT_ENVIRONMENT !== ENVIRONMENTS.PRODUCTION,
  transportTypes: ['WebSockets', 'LongPolling'],
  events: {
    USER_CHAT_ONLINE: 'userChatOnline',
    CHAT_UPDATE: 'chatUpdate',
    GROUP_EVENTS: 'groupEvents',
    NEW_MESSAGE: 'newMessage',
    MESSAGE_STATUS: 'messageStatus',
    MESSAGE_UPDATE_OR_DELETE: 'messageUpdateOrDelete',
    POLL_VOTE: 'pollVote',
    BLOCKING_EVENT: 'blockingEvent',
    USER_INFO: 'userInfo'
  }
};

// إعدادات واجهة المستخدم
export const UI_CONFIG = {
  // الثيمات المتاحة
  themes: {
    LIGHT: 'light',
    DARK: 'dark',
    AUTO: 'auto'
  },
  
  // اللغات المدعومة
  languages: {
    ARABIC: 'ar',
    ENGLISH: 'en'
  },
  
  // إعدادات الرسائل
  messages: {
    maxLength: 4000,
    enableEmojis: true,
    enableMarkdown: false,
    showTypingIndicator: true,
    showReadReceipts: true,
    autoScrollToBottom: true
  },
  
  // إعدادات قائمة المحادثات
  chatList: {
    itemsPerPage: 20,
    enableInfiniteScroll: true,
    showLastMessage: true,
    showUnreadCount: true,
    enableSearch: true,
    searchDebounceTime: 300
  },
  
  // إعدادات الملفات
  files: {
    maxSize: 50 * 1024 * 1024, // 50 ميجابايت
    allowedTypes: {
      images: ['jpg', 'jpeg', 'png', 'gif', 'webp'],
      videos: ['mp4', 'webm', 'ogg'],
      audio: ['mp3', 'wav', 'ogg', 'm4a'],
      documents: ['pdf', 'doc', 'docx', 'txt', 'rtf']
    },
    enablePreview: true,
    enableThumbnails: true
  },
  
  // إعدادات الإشعارات
  notifications: {
    enableBrowser: true,
    enableSound: true,
    enableVibration: true,
    showPreview: true,
    autoClose: true,
    autoCloseDelay: 5000
  },
  
  // إعدادات الأداء
  performance: {
    enableVirtualScrolling: true,
    enableLazyLoading: true,
    enableImageOptimization: true,
    maxCachedMessages: 1000,
    messageCleanupInterval: 30 * 60 * 1000 // 30 دقيقة
  }
};

// إعدادات الأمان
export const SECURITY_CONFIG = {
  // إعدادات الجلسة
  session: {
    timeout: 24 * 60 * 60 * 1000, // 24 ساعة
    refreshThreshold: 5 * 60 * 1000, // 5 دقائق
    enableAutoRefresh: true
  },
  
  // إعدادات التشفير
  encryption: {
    enableLocalEncryption: false,
    algorithm: 'AES-GCM',
    keyLength: 256
  },
  
  // إعدادات التحقق
  validation: {
    enableInputSanitization: true,
    enableXSSProtection: true,
    enableCSRFProtection: true,
    maxRequestSize: 10 * 1024 * 1024 // 10 ميجابايت
  }
};

// إعدادات التطوير والتشخيص
export const DEBUG_CONFIG = {
  enableConsoleLogging: CURRENT_ENVIRONMENT !== ENVIRONMENTS.PRODUCTION,
  enablePerformanceMonitoring: true,
  enableErrorReporting: true,
  enableAnalytics: CURRENT_ENVIRONMENT === ENVIRONMENTS.PRODUCTION,
  logLevel: CURRENT_ENVIRONMENT === ENVIRONMENTS.PRODUCTION ? 'error' : 'debug'
};

// إعدادات التخزين المؤقت
export const CACHE_CONFIG = {
  // تخزين مؤقت للAPI
  api: {
    enabled: true,
    defaultTTL: 5 * 60 * 1000, // 5 دقائق
    maxSize: 100,
    strategies: {
      chats: 10 * 60 * 1000, // 10 دقائق
      messages: 2 * 60 * 1000, // 2 دقيقة
      users: 30 * 60 * 1000, // 30 دقيقة
      contacts: 15 * 60 * 1000 // 15 دقيقة
    }
  },
  
  // تخزين مؤقت للصور
  images: {
    enabled: true,
    maxSize: 50,
    maxFileSize: 5 * 1024 * 1024, // 5 ميجابايت
    enableCompression: true,
    compressionQuality: 0.8
  }
};

// إعدادات الصوت والوسائط
export const MEDIA_CONFIG = {
  // إعدادات تسجيل الصوت
  voice: {
    maxDuration: 5 * 60 * 1000, // 5 دقائق
    sampleRate: 44100,
    bitRate: 128000,
    format: 'webm',
    enableNoiseReduction: true
  },
  
  // إعدادات الصور
  images: {
    maxWidth: 1920,
    maxHeight: 1080,
    quality: 0.9,
    enableResize: true,
    thumbnailSize: 200
  },
  
  // إعدادات الفيديو
  video: {
    maxDuration: 10 * 60 * 1000, // 10 دقائق
    maxSize: 100 * 1024 * 1024, // 100 ميجابايت
    enableCompression: true,
    thumbnailTime: 1 // ثانية واحدة
  }
};

// الحصول على الإعدادات الحالية حسب البيئة
export function getCurrentConfig() {
  const baseConfig = {
    environment: CURRENT_ENVIRONMENT,
    api: API_CONFIG[CURRENT_ENVIRONMENT],
    endpoints: API_ENDPOINTS,
    database: DATABASE_CONFIG,
    signalr: SIGNALR_CONFIG,
    ui: UI_CONFIG,
    security: SECURITY_CONFIG,
    debug: DEBUG_CONFIG,
    cache: CACHE_CONFIG,
    media: MEDIA_CONFIG
  };

  // دمج الإعدادات المخصصة إذا كانت موجودة
  const customConfig = getCustomConfig();
  
  return deepMerge(baseConfig, customConfig);
}

// الحصول على الإعدادات المخصصة من التخزين المحلي
function getCustomConfig() {
  try {
    const stored = localStorage.getItem('chatapp_custom_config');
    return stored ? JSON.parse(stored) : {};
  } catch (error) {
    console.warn('فشل في تحميل الإعدادات المخصصة:', error);
    return {};
  }
}

// حفظ الإعدادات المخصصة
export function saveCustomConfig(config) {
  try {
    localStorage.setItem('chatapp_custom_config', JSON.stringify(config));
    return true;
  } catch (error) {
    console.error('فشل في حفظ الإعدادات المخصصة:', error);
    return false;
  }
}

// دمج عميق للكائنات
function deepMerge(target, source) {
  const result = { ...target };
  
  for (const key in source) {
    if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
      result[key] = deepMerge(result[key] || {}, source[key]);
    } else {
      result[key] = source[key];
    }
  }
  
  return result;
}

// تحديث إعداد معين
export function updateConfig(path, value) {
  const customConfig = getCustomConfig();
  setNestedValue(customConfig, path, value);
  return saveCustomConfig(customConfig);
}

// تعيين قيمة متداخلة في كائن
function setNestedValue(obj, path, value) {
  const keys = path.split('.');
  const lastKey = keys.pop();
  
  const target = keys.reduce((current, key) => {
    if (!current[key] || typeof current[key] !== 'object') {
      current[key] = {};
    }
    return current[key];
  }, obj);
  
  target[lastKey] = value;
}

// إعادة تعيين الإعدادات إلى القيم الافتراضية
export function resetConfig() {
  try {
    localStorage.removeItem('chatapp_custom_config');
    return true;
  } catch (error) {
    console.error('فشل في إعادة تعيين الإعدادات:', error);
    return false;
  }
}

// التحقق من صحة الإعدادات
export function validateConfig(config) {
  const errors = [];
  
  // التحقق من إعدادات API
  if (!config.api?.baseUrl) {
    errors.push('عنوان API الأساسي مطلوب');
  }
  
  // التحقق من إعدادات قاعدة البيانات
  if (!config.database?.name) {
    errors.push('اسم قاعدة البيانات مطلوب');
  }
  
  // التحقق من إعدادات SignalR
  if (!config.signalr?.events) {
    errors.push('أحداث SignalR مطلوبة');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

// تصدير الإعدادات الحالية كافتراضي
export default getCurrentConfig();
