<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="تطبيق المحادثات المطور - نسخة محسنة ومعاد هيكلتها">
    <meta name="author" content="فريق التطوير">
    <title>تطبيق المحادثات - النسخة المحسنة</title>
    
    <!-- أيقونة التطبيق -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="apple-touch-icon" href="/apple-touch-icon.png">
    
    <!-- CSS الأساسي -->
    <style>
        /* إعدادات أساسية للتحميل */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: '<PERSON><PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            overflow: hidden;
        }
        
        /* شاشة التحميل */
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            color: white;
        }
        
        .loading-logo {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 2rem;
            animation: pulse 2s infinite;
        }
        
        .loading-logo svg {
            width: 40px;
            height: 40px;
            fill: white;
        }
        
        .loading-title {
            font-size: 2rem;
            font-weight: 300;
            margin-bottom: 1rem;
            text-align: center;
        }
        
        .loading-message {
            font-size: 1rem;
            opacity: 0.8;
            margin-bottom: 2rem;
            text-align: center;
            min-height: 1.5rem;
        }
        
        .loading-progress {
            width: 300px;
            height: 4px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 2px;
            overflow: hidden;
            margin-bottom: 1rem;
        }
        
        .loading-progress-bar {
            height: 100%;
            background: white;
            border-radius: 2px;
            width: 0%;
            transition: width 0.3s ease;
            animation: loading 2s infinite;
        }
        
        .loading-details {
            font-size: 0.875rem;
            opacity: 0.6;
            text-align: center;
        }
        
        /* شاشة الخطأ */
        .error-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #f8f9fa;
            display: none;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 9998;
        }
        
        .error-content {
            text-align: center;
            padding: 2rem;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            max-width: 500px;
            margin: 0 1rem;
        }
        
        .error-icon {
            width: 64px;
            height: 64px;
            background: #e74c3c;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 0 auto 1.5rem;
        }
        
        .error-icon svg {
            width: 32px;
            height: 32px;
            fill: white;
        }
        
        .error-title {
            font-size: 1.5rem;
            color: #e74c3c;
            margin-bottom: 1rem;
        }
        
        .error-message {
            color: #666;
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        
        .error-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 1rem;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #3498db;
            color: white;
        }
        
        .btn-primary:hover {
            background: #2980b9;
        }
        
        .btn-secondary {
            background: #95a5a6;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #7f8c8d;
        }
        
        /* التطبيق الرئيسي */
        .app-container {
            display: none;
            width: 100%;
            height: 100vh;
            background: #f5f5f5;
        }
        
        .app-container.loaded {
            display: flex;
        }
        
        /* الرسوم المتحركة */
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }
        
        @keyframes loading {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(400px); }
        }
        
        /* التصميم المتجاوب */
        @media (max-width: 768px) {
            .loading-title {
                font-size: 1.5rem;
            }
            
            .loading-progress {
                width: 250px;
            }
            
            .error-content {
                margin: 0 1rem;
                padding: 1.5rem;
            }
            
            .error-actions {
                flex-direction: column;
            }
            
            .btn {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <!-- شاشة التحميل -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-logo">
            <svg viewBox="0 0 24 24">
                <path d="M20,2H4A2,2 0 0,0 2,4V22L6,18H20A2,2 0 0,0 22,16V4A2,2 0 0,0 20,2M6,9V7H18V9H6M14,11V13H6V11H14M16,15V17H6V15H16Z"/>
            </svg>
        </div>
        <h1 class="loading-title">تطبيق المحادثات</h1>
        <div id="loading-message" class="loading-message">جاري تهيئة التطبيق...</div>
        <div class="loading-progress">
            <div id="loading-progress-bar" class="loading-progress-bar"></div>
        </div>
        <div id="loading-details" class="loading-details">تحميل الوحدات الأساسية</div>
    </div>

    <!-- شاشة الخطأ -->
    <div id="error-screen" class="error-screen">
        <div class="error-content">
            <div class="error-icon">
                <svg viewBox="0 0 24 24">
                    <path d="M13,14H11V10H13M13,18H11V16H13M1,21H23L12,2L1,21Z"/>
                </svg>
            </div>
            <h2 class="error-title">خطأ في تشغيل التطبيق</h2>
            <p id="error-message" class="error-message">
                عذراً، حدث خطأ أثناء تشغيل التطبيق. يرجى المحاولة مرة أخرى.
            </p>
            <div class="error-actions">
                <button id="retry-btn" class="btn btn-primary">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M17.65,6.35C16.2,4.9 14.21,4 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20C15.73,20 18.84,17.45 19.73,14H17.65C16.83,16.33 14.61,18 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6C13.66,6 15.14,6.69 16.22,7.78L13,11H20V4L17.65,6.35Z"/>
                    </svg>
                    إعادة المحاولة
                </button>
                <button id="reload-btn" class="btn btn-secondary">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M17.65,6.35C16.2,4.9 14.21,4 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20C15.73,20 18.84,17.45 19.73,14H17.65C16.83,16.33 14.61,18 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6C13.66,6 15.14,6.69 16.22,7.78L13,11H20V4L17.65,6.35Z"/>
                    </svg>
                    تحديث الصفحة
                </button>
            </div>
        </div>
    </div>

    <!-- حاوي التطبيق الرئيسي -->
    <div id="app-container" class="app-container">
        <!-- سيتم تحميل مكونات التطبيق هنا -->
        <div id="chat-sidebar" class="chat-sidebar">
            <!-- قائمة المحادثات -->
        </div>
        
        <div id="chat-main" class="chat-main">
            <!-- منطقة الرسائل -->
        </div>
        
        <div id="chat-details" class="chat-details">
            <!-- تفاصيل المحادثة -->
        </div>
    </div>

    <!-- إشعارات النظام -->
    <div id="notifications-container" class="notifications-container"></div>

    <!-- نافذة منبثقة للأخطاء -->
    <div id="error-modal" class="error-modal" style="display: none;"></div>

    <!-- سكريبت التحميل والتهيئة -->
    <script>
        // متغيرات التحميل
        let loadingProgress = 0;
        let loadingSteps = [
            'تحميل الوحدات الأساسية',
            'تهيئة قاعدة البيانات',
            'التحقق من المصادقة',
            'تحميل مكونات الواجهة',
            'تهيئة الاتصال المباشر',
            'إنهاء التهيئة'
        ];
        let currentStep = 0;

        // عناصر DOM
        const loadingScreen = document.getElementById('loading-screen');
        const errorScreen = document.getElementById('error-screen');
        const appContainer = document.getElementById('app-container');
        const loadingMessage = document.getElementById('loading-message');
        const loadingProgressBar = document.getElementById('loading-progress-bar');
        const loadingDetails = document.getElementById('loading-details');
        const errorMessage = document.getElementById('error-message');
        const retryBtn = document.getElementById('retry-btn');
        const reloadBtn = document.getElementById('reload-btn');

        // تحديث شريط التقدم
        function updateProgress(progress, message, details) {
            loadingProgress = Math.min(100, Math.max(0, progress));
            loadingProgressBar.style.width = loadingProgress + '%';
            
            if (message) {
                loadingMessage.textContent = message;
            }
            
            if (details) {
                loadingDetails.textContent = details;
            }
        }

        // إظهار شاشة الخطأ
        function showError(message, canRetry = true) {
            loadingScreen.style.display = 'none';
            errorScreen.style.display = 'flex';
            errorMessage.textContent = message;
            
            retryBtn.style.display = canRetry ? 'inline-flex' : 'none';
        }

        // إظهار التطبيق
        function showApp() {
            loadingScreen.style.display = 'none';
            errorScreen.style.display = 'none';
            appContainer.classList.add('loaded');
        }

        // معالجات الأحداث
        retryBtn.addEventListener('click', () => {
            errorScreen.style.display = 'none';
            loadingScreen.style.display = 'flex';
            updateProgress(0, 'جاري إعادة المحاولة...', 'إعادة تهيئة التطبيق');
            
            // إعادة تحميل التطبيق
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        });

        reloadBtn.addEventListener('click', () => {
            window.location.reload();
        });

        // مستمعي أحداث التطبيق
        document.addEventListener('DOMContentLoaded', () => {
            console.log('📄 تم تحميل DOM، بدء تحميل التطبيق...');
            
            // محاكاة تقدم التحميل
            let progress = 0;
            const progressInterval = setInterval(() => {
                progress += Math.random() * 15;
                
                if (progress >= 90) {
                    clearInterval(progressInterval);
                    progress = 90;
                }
                
                const stepIndex = Math.floor((progress / 100) * loadingSteps.length);
                const currentStepText = loadingSteps[Math.min(stepIndex, loadingSteps.length - 1)];
                
                updateProgress(progress, 'جاري تهيئة التطبيق...', currentStepText);
            }, 200);
        });

        // مستمعي أحداث التطبيق المخصصة
        if (typeof window !== 'undefined') {
            // مستمع لبداية التهيئة
            document.addEventListener('app:initialization:started', () => {
                updateProgress(10, 'بدء تهيئة التطبيق...', 'تحميل الوحدات الأساسية');
            });

            // مستمع لتقدم المراحل
            document.addEventListener('app:phase:started', (event) => {
                const phase = event.detail.phase;
                const phaseMessages = {
                    'core_services': { progress: 20, message: 'تهيئة الخدمات الأساسية...' },
                    'database': { progress: 40, message: 'تهيئة قاعدة البيانات...' },
                    'authentication': { progress: 60, message: 'التحقق من المصادقة...' },
                    'ui_components': { progress: 80, message: 'تحميل مكونات الواجهة...' },
                    'real_time': { progress: 90, message: 'تهيئة الاتصال المباشر...' },
                    'finalization': { progress: 95, message: 'إنهاء التهيئة...' }
                };
                
                const phaseInfo = phaseMessages[phase];
                if (phaseInfo) {
                    updateProgress(phaseInfo.progress, phaseInfo.message, '');
                }
            });

            // مستمع لإكمال التهيئة
            document.addEventListener('app:initialization:completed', (event) => {
                updateProgress(100, 'تم تحميل التطبيق بنجاح!', '');
                
                setTimeout(() => {
                    showApp();
                }, 500);
            });

            // مستمع لفشل التهيئة
            document.addEventListener('app:initialization:failed', (event) => {
                const error = event.detail.error;
                showError(
                    error?.message || 'حدث خطأ غير متوقع أثناء تهيئة التطبيق',
                    true
                );
            });

            // مستمع للأخطاء الحرجة
            document.addEventListener('app:critical:error', (event) => {
                const error = event.detail.error;
                showError(
                    'حدث خطأ حرج في التطبيق. يرجى تحديث الصفحة.',
                    false
                );
            });
        }

        // معالجة الأخطاء العامة
        window.addEventListener('error', (event) => {
            console.error('خطأ JavaScript:', event.error);
            
            if (!appContainer.classList.contains('loaded')) {
                showError(
                    'حدث خطأ أثناء تحميل التطبيق. يرجى تحديث الصفحة والمحاولة مرة أخرى.',
                    true
                );
            }
        });

        window.addEventListener('unhandledrejection', (event) => {
            console.error('وعد مرفوض:', event.reason);
            
            if (!appContainer.classList.contains('loaded')) {
                showError(
                    'حدث خطأ في تحميل البيانات. يرجى التحقق من اتصال الإنترنت والمحاولة مرة أخرى.',
                    true
                );
            }
        });
    </script>

    <!-- تحميل التطبيق الرئيسي -->
    <script type="module" src="./main.js"></script>
</body>
</html>
