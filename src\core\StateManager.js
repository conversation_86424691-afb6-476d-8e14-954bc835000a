/**
 * مدير الحالة المركزي - StateManager
 * يدير حالة التطبيق بشكل مركزي مع دعم للتتبع والتحديثات التفاعلية
 * يستخدم نمط Redux المبسط مع دعم للأحداث
 */

import eventBus from './EventBus.js';

class StateManager {
  constructor() {
    // الحالة الرئيسية للتطبيق
    this.state = {
      // معلومات المستخدم الحالي
      currentUser: null,
      
      // قائمة المحادثات
      chats: [],
      activeChatId: null,
      
      // الرسائل
      messages: new Map(), // chatId -> messages[]
      
      // جهات الاتصال والمستخدمين
      contacts: [],
      users: [],
      teams: [],
      
      // حالة الاتصال
      connectionStatus: {
        isOnline: navigator.onLine,
        signalRConnected: false,
        lastSeen: new Date()
      },
      
      // حالة واجهة المستخدم
      ui: {
        isLoading: false,
        activeView: 'chat-list',
        sidebarOpen: true,
        theme: 'light',
        language: 'ar'
      },
      
      // الإعدادات
      settings: {
        notifications: true,
        soundEnabled: true,
        autoDownload: true
      },
      
      // الأخطاء والتنبيهات
      errors: [],
      notifications: []
    };

    // تاريخ التغييرات للتراجع والإعادة
    this.history = [];
    this.maxHistorySize = 50;
    this.currentHistoryIndex = -1;

    // المستمعين للتغييرات
    this.listeners = new Map();
    this.listenerIdCounter = 0;

    // تهيئة المستمعين للأحداث الخارجية
    this._initializeEventListeners();
  }

  /**
   * الحصول على الحالة الكاملة أو جزء منها
   * @param {string} path - مسار الخاصية (اختياري)
   * @returns {*} الحالة أو قيمة الخاصية
   */
  getState(path = null) {
    if (!path) {
      return { ...this.state };
    }

    return this._getNestedValue(this.state, path);
  }

  /**
   * تحديث الحالة
   * @param {string} path - مسار الخاصية
   * @param {*} value - القيمة الجديدة
   * @param {Object} options - خيارات التحديث
   */
  setState(path, value, options = {}) {
    try {
      // حفظ الحالة السابقة للتاريخ
      if (!options.skipHistory) {
        this._saveToHistory();
      }

      // الحالة السابقة للمقارنة
      const previousState = { ...this.state };
      const previousValue = this._getNestedValue(this.state, path);

      // تحديث الحالة
      this._setNestedValue(this.state, path, value);

      // إطلاق أحداث التغيير
      if (!options.silent) {
        this._notifyListeners(path, value, previousValue);
        
        // إطلاق حدث عام للتغيير
        eventBus.emit('state:changed', {
          path,
          value,
          previousValue,
          state: this.getState()
        });

        // إطلاق حدث محدد للمسار
        eventBus.emit(`state:${path}:changed`, {
          value,
          previousValue,
          state: this.getState()
        });
      }

      return true;
    } catch (error) {
      console.error('خطأ في تحديث الحالة:', error);
      return false;
    }
  }

  /**
   * تحديث متعدد للحالة
   * @param {Object} updates - كائن التحديثات
   * @param {Object} options - خيارات التحديث
   */
  setMultiple(updates, options = {}) {
    try {
      if (!options.skipHistory) {
        this._saveToHistory();
      }

      const previousState = { ...this.state };
      const changes = [];

      // تطبيق جميع التحديثات
      Object.entries(updates).forEach(([path, value]) => {
        const previousValue = this._getNestedValue(this.state, path);
        this._setNestedValue(this.state, path, value);
        
        changes.push({
          path,
          value,
          previousValue
        });
      });

      // إطلاق الأحداث
      if (!options.silent) {
        changes.forEach(({ path, value, previousValue }) => {
          this._notifyListeners(path, value, previousValue);
          eventBus.emit(`state:${path}:changed`, {
            value,
            previousValue,
            state: this.getState()
          });
        });

        // حدث عام للتحديث المتعدد
        eventBus.emit('state:multiple:changed', {
          changes,
          state: this.getState()
        });
      }

      return true;
    } catch (error) {
      console.error('خطأ في التحديث المتعدد للحالة:', error);
      return false;
    }
  }

  /**
   * الاشتراك في تغييرات الحالة
   * @param {string} path - مسار الخاصية
   * @param {Function} callback - دالة المعالجة
   * @returns {number} معرف المستمع
   */
  subscribe(path, callback) {
    const listenerId = ++this.listenerIdCounter;
    
    if (!this.listeners.has(path)) {
      this.listeners.set(path, []);
    }
    
    this.listeners.get(path).push({
      id: listenerId,
      callback
    });

    return listenerId;
  }

  /**
   * إلغاء الاشتراك في تغييرات الحالة
   * @param {string} path - مسار الخاصية
   * @param {number} listenerId - معرف المستمع
   */
  unsubscribe(path, listenerId) {
    if (!this.listeners.has(path)) {
      return false;
    }

    const pathListeners = this.listeners.get(path);
    const index = pathListeners.findIndex(listener => listener.id === listenerId);
    
    if (index !== -1) {
      pathListeners.splice(index, 1);
      
      if (pathListeners.length === 0) {
        this.listeners.delete(path);
      }
      
      return true;
    }
    
    return false;
  }

  /**
   * إضافة عنصر إلى مصفوفة في الحالة
   * @param {string} path - مسار المصفوفة
   * @param {*} item - العنصر المراد إضافته
   * @param {Object} options - خيارات الإضافة
   */
  addToArray(path, item, options = {}) {
    const currentArray = this._getNestedValue(this.state, path);
    
    if (!Array.isArray(currentArray)) {
      throw new Error(`المسار ${path} لا يشير إلى مصفوفة`);
    }

    const newArray = [...currentArray];
    
    if (options.prepend) {
      newArray.unshift(item);
    } else {
      newArray.push(item);
    }

    this.setState(path, newArray, options);
  }

  /**
   * إزالة عنصر من مصفوفة في الحالة
   * @param {string} path - مسار المصفوفة
   * @param {Function|*} predicate - دالة البحث أو القيمة
   * @param {Object} options - خيارات الإزالة
   */
  removeFromArray(path, predicate, options = {}) {
    const currentArray = this._getNestedValue(this.state, path);
    
    if (!Array.isArray(currentArray)) {
      throw new Error(`المسار ${path} لا يشير إلى مصفوفة`);
    }

    let newArray;
    
    if (typeof predicate === 'function') {
      newArray = currentArray.filter(item => !predicate(item));
    } else {
      newArray = currentArray.filter(item => item !== predicate);
    }

    this.setState(path, newArray, options);
  }

  /**
   * تحديث عنصر في مصفوفة
   * @param {string} path - مسار المصفوفة
   * @param {Function} predicate - دالة البحث
   * @param {*} updates - التحديثات
   * @param {Object} options - خيارات التحديث
   */
  updateInArray(path, predicate, updates, options = {}) {
    const currentArray = this._getNestedValue(this.state, path);
    
    if (!Array.isArray(currentArray)) {
      throw new Error(`المسار ${path} لا يشير إلى مصفوفة`);
    }

    const newArray = currentArray.map(item => {
      if (predicate(item)) {
        return typeof updates === 'function' ? updates(item) : { ...item, ...updates };
      }
      return item;
    });

    this.setState(path, newArray, options);
  }

  /**
   * التراجع عن آخر تغيير
   */
  undo() {
    if (this.currentHistoryIndex > 0) {
      this.currentHistoryIndex--;
      this.state = { ...this.history[this.currentHistoryIndex] };
      
      eventBus.emit('state:undo', {
        state: this.getState(),
        historyIndex: this.currentHistoryIndex
      });
      
      return true;
    }
    
    return false;
  }

  /**
   * الإعادة بعد التراجع
   */
  redo() {
    if (this.currentHistoryIndex < this.history.length - 1) {
      this.currentHistoryIndex++;
      this.state = { ...this.history[this.currentHistoryIndex] };
      
      eventBus.emit('state:redo', {
        state: this.getState(),
        historyIndex: this.currentHistoryIndex
      });
      
      return true;
    }
    
    return false;
  }

  /**
   * مسح التاريخ
   */
  clearHistory() {
    this.history = [];
    this.currentHistoryIndex = -1;
  }

  /**
   * إعادة تعيين الحالة إلى القيم الافتراضية
   */
  reset() {
    this._saveToHistory();
    
    this.state = {
      currentUser: null,
      chats: [],
      activeChatId: null,
      messages: new Map(),
      contacts: [],
      users: [],
      teams: [],
      connectionStatus: {
        isOnline: navigator.onLine,
        signalRConnected: false,
        lastSeen: new Date()
      },
      ui: {
        isLoading: false,
        activeView: 'chat-list',
        sidebarOpen: true,
        theme: 'light',
        language: 'ar'
      },
      settings: {
        notifications: true,
        soundEnabled: true,
        autoDownload: true
      },
      errors: [],
      notifications: []
    };

    eventBus.emit('state:reset', {
      state: this.getState()
    });
  }

  /**
   * الحصول على قيمة متداخلة من كائن
   * @private
   */
  _getNestedValue(obj, path) {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }

  /**
   * تعيين قيمة متداخلة في كائن
   * @private
   */
  _setNestedValue(obj, path, value) {
    const keys = path.split('.');
    const lastKey = keys.pop();
    
    const target = keys.reduce((current, key) => {
      if (!current[key] || typeof current[key] !== 'object') {
        current[key] = {};
      }
      return current[key];
    }, obj);
    
    target[lastKey] = value;
  }

  /**
   * حفظ الحالة الحالية في التاريخ
   * @private
   */
  _saveToHistory() {
    // إزالة العناصر التالية إذا كنا في وسط التاريخ
    if (this.currentHistoryIndex < this.history.length - 1) {
      this.history = this.history.slice(0, this.currentHistoryIndex + 1);
    }

    // إضافة الحالة الحالية
    this.history.push({ ...this.state });
    this.currentHistoryIndex++;

    // الحفاظ على حجم التاريخ
    if (this.history.length > this.maxHistorySize) {
      this.history = this.history.slice(-this.maxHistorySize);
      this.currentHistoryIndex = this.history.length - 1;
    }
  }

  /**
   * إشعار المستمعين بالتغييرات
   * @private
   */
  _notifyListeners(path, value, previousValue) {
    if (this.listeners.has(path)) {
      this.listeners.get(path).forEach(listener => {
        try {
          listener.callback(value, previousValue, path);
        } catch (error) {
          console.error('خطأ في مستمع تغيير الحالة:', error);
        }
      });
    }
  }

  /**
   * تهيئة مستمعي الأحداث الخارجية
   * @private
   */
  _initializeEventListeners() {
    // مراقبة حالة الاتصال بالإنترنت
    window.addEventListener('online', () => {
      this.setState('connectionStatus.isOnline', true);
    });

    window.addEventListener('offline', () => {
      this.setState('connectionStatus.isOnline', false);
    });

    // مراقبة تغييرات حجم النافذة للواجهة المتجاوبة
    window.addEventListener('resize', () => {
      const isMobile = window.innerWidth < 768;
      this.setState('ui.isMobile', isMobile);
    });
  }
}

// إنشاء مثيل عام للاستخدام في التطبيق
const stateManager = new StateManager();

// تصدير الكلاس والمثيل
export { StateManager, stateManager };
export default stateManager;
