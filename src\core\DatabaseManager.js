/**
 * مدير قاعدة البيانات - DatabaseManager
 * نظام إدارة قاعدة البيانات المحلية باستخدام IndexedDB
 * يوفر واجهة موحدة للتعامل مع البيانات المحلية
 */

import { DATABASE } from '../config/constants.js';
import errorHandler from './ErrorHandler.js';
import eventBus from './EventBus.js';

/**
 * كلاس مدير قاعدة البيانات
 */
class DatabaseManager {
  constructor() {
    // معلومات قاعدة البيانات
    this.dbName = DATABASE.NAME;
    this.dbVersion = DATABASE.VERSION;
    this.db = null;
    
    // حالة الاتصال
    this.isConnected = false;
    this.isInitializing = false;
    
    // مخازن البيانات
    this.stores = DATABASE.STORES;
    
    // إعدادات التشخيص
    this.debug = false;
    
    // إحصائيات
    this.stats = {
      operations: 0,
      reads: 0,
      writes: 0,
      deletes: 0,
      errors: 0,
      lastOperation: null
    };
    
    console.log('🗄️ تم تهيئة مدير قاعدة البيانات');
  }

  /**
   * تهيئة قاعدة البيانات
   * @returns {Promise<boolean>} نجحت التهيئة أم لا
   */
  async initialize() {
    if (this.isConnected || this.isInitializing) {
      return this.isConnected;
    }

    this.isInitializing = true;

    try {
      console.log('🚀 تهيئة قاعدة البيانات...');

      // التحقق من دعم IndexedDB
      if (!this._isIndexedDBSupported()) {
        throw new Error('IndexedDB غير مدعوم في هذا المتصفح');
      }

      // فتح قاعدة البيانات
      this.db = await this._openDatabase();
      this.isConnected = true;

      // إطلاق حدث الاتصال
      eventBus.emit('database:connected', {
        dbName: this.dbName,
        version: this.dbVersion
      });

      console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

      return true;

    } catch (error) {
      console.error('❌ فشل في تهيئة قاعدة البيانات:', error);
      
      errorHandler.handleError(error, {
        type: 'database',
        operation: 'initialize'
      });

      return false;

    } finally {
      this.isInitializing = false;
    }
  }

  /**
   * إضافة عنصر إلى مخزن البيانات
   * @param {string} storeName - اسم المخزن
   * @param {Object} data - البيانات
   * @param {string} key - المفتاح (اختياري)
   * @returns {Promise<string>} معرف العنصر المضاف
   */
  async add(storeName, data, key = null) {
    await this._ensureConnection();
    
    try {
      const transaction = this.db.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      
      const request = key ? store.add(data, key) : store.add(data);
      const result = await this._promisifyRequest(request);
      
      this._updateStats('writes');
      
      if (this.debug) {
        console.log(`➕ تم إضافة عنصر إلى ${storeName}:`, result);
      }

      eventBus.emit('database:item:added', {
        storeName,
        key: result,
        data
      });

      return result;

    } catch (error) {
      this._handleError(error, 'add', { storeName, data });
      throw error;
    }
  }

  /**
   * الحصول على عنصر من مخزن البيانات
   * @param {string} storeName - اسم المخزن
   * @param {string} key - المفتاح
   * @returns {Promise<Object|null>} البيانات أو null
   */
  async get(storeName, key) {
    await this._ensureConnection();
    
    try {
      const transaction = this.db.transaction([storeName], 'readonly');
      const store = transaction.objectStore(storeName);
      
      const request = store.get(key);
      const result = await this._promisifyRequest(request);
      
      this._updateStats('reads');
      
      if (this.debug) {
        console.log(`📖 تم جلب عنصر من ${storeName}:`, key, result);
      }

      return result || null;

    } catch (error) {
      this._handleError(error, 'get', { storeName, key });
      throw error;
    }
  }

  /**
   * الحصول على جميع العناصر من مخزن البيانات
   * @param {string} storeName - اسم المخزن
   * @param {Object} options - خيارات الاستعلام
   * @returns {Promise<Array>} قائمة البيانات
   */
  async getAll(storeName, options = {}) {
    await this._ensureConnection();
    
    try {
      const transaction = this.db.transaction([storeName], 'readonly');
      const store = transaction.objectStore(storeName);
      
      let request;
      
      if (options.index) {
        const index = store.index(options.index);
        request = options.query ? index.getAll(options.query) : index.getAll();
      } else {
        request = options.query ? store.getAll(options.query) : store.getAll();
      }
      
      let results = await this._promisifyRequest(request);
      
      // تطبيق الفلاتر والترتيب
      if (options.filter) {
        results = results.filter(options.filter);
      }
      
      if (options.sort) {
        results.sort(options.sort);
      }
      
      if (options.limit) {
        results = results.slice(0, options.limit);
      }
      
      this._updateStats('reads');
      
      if (this.debug) {
        console.log(`📚 تم جلب ${results.length} عنصر من ${storeName}`);
      }

      return results;

    } catch (error) {
      this._handleError(error, 'getAll', { storeName, options });
      throw error;
    }
  }

  /**
   * تحديث عنصر في مخزن البيانات
   * @param {string} storeName - اسم المخزن
   * @param {string} key - المفتاح
   * @param {Object} data - البيانات الجديدة
   * @param {boolean} merge - دمج مع البيانات الموجودة
   * @returns {Promise<boolean>} نجح التحديث أم لا
   */
  async update(storeName, key, data, merge = false) {
    await this._ensureConnection();
    
    try {
      const transaction = this.db.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      
      let finalData = data;
      
      if (merge) {
        const existing = await this._promisifyRequest(store.get(key));
        if (existing) {
          finalData = { ...existing, ...data };
        }
      }
      
      const request = store.put(finalData, key);
      await this._promisifyRequest(request);
      
      this._updateStats('writes');
      
      if (this.debug) {
        console.log(`✏️ تم تحديث عنصر في ${storeName}:`, key);
      }

      eventBus.emit('database:item:updated', {
        storeName,
        key,
        data: finalData
      });

      return true;

    } catch (error) {
      this._handleError(error, 'update', { storeName, key, data });
      throw error;
    }
  }

  /**
   * حذف عنصر من مخزن البيانات
   * @param {string} storeName - اسم المخزن
   * @param {string} key - المفتاح
   * @returns {Promise<boolean>} نجح الحذف أم لا
   */
  async delete(storeName, key) {
    await this._ensureConnection();
    
    try {
      const transaction = this.db.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      
      const request = store.delete(key);
      await this._promisifyRequest(request);
      
      this._updateStats('deletes');
      
      if (this.debug) {
        console.log(`🗑️ تم حذف عنصر من ${storeName}:`, key);
      }

      eventBus.emit('database:item:deleted', {
        storeName,
        key
      });

      return true;

    } catch (error) {
      this._handleError(error, 'delete', { storeName, key });
      throw error;
    }
  }

  /**
   * مسح جميع البيانات من مخزن
   * @param {string} storeName - اسم المخزن
   * @returns {Promise<boolean>} نجح المسح أم لا
   */
  async clear(storeName) {
    await this._ensureConnection();
    
    try {
      const transaction = this.db.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      
      const request = store.clear();
      await this._promisifyRequest(request);
      
      if (this.debug) {
        console.log(`🧹 تم مسح جميع البيانات من ${storeName}`);
      }

      eventBus.emit('database:store:cleared', { storeName });

      return true;

    } catch (error) {
      this._handleError(error, 'clear', { storeName });
      throw error;
    }
  }

  /**
   * البحث في مخزن البيانات
   * @param {string} storeName - اسم المخزن
   * @param {Object} criteria - معايير البحث
   * @returns {Promise<Array>} نتائج البحث
   */
  async search(storeName, criteria) {
    await this._ensureConnection();
    
    try {
      const allItems = await this.getAll(storeName);
      
      const results = allItems.filter(item => {
        return Object.entries(criteria).every(([key, value]) => {
          if (typeof value === 'string') {
            return item[key] && item[key].toString().toLowerCase().includes(value.toLowerCase());
          }
          return item[key] === value;
        });
      });
      
      this._updateStats('reads');
      
      if (this.debug) {
        console.log(`🔍 تم العثور على ${results.length} نتيجة في ${storeName}`);
      }

      return results;

    } catch (error) {
      this._handleError(error, 'search', { storeName, criteria });
      throw error;
    }
  }

  /**
   * عد العناصر في مخزن البيانات
   * @param {string} storeName - اسم المخزن
   * @param {Object} query - استعلام العد (اختياري)
   * @returns {Promise<number>} عدد العناصر
   */
  async count(storeName, query = null) {
    await this._ensureConnection();
    
    try {
      const transaction = this.db.transaction([storeName], 'readonly');
      const store = transaction.objectStore(storeName);
      
      const request = query ? store.count(query) : store.count();
      const result = await this._promisifyRequest(request);
      
      this._updateStats('reads');
      
      return result;

    } catch (error) {
      this._handleError(error, 'count', { storeName, query });
      throw error;
    }
  }

  /**
   * تنفيذ معاملة مخصصة
   * @param {Array} storeNames - أسماء المخازن
   * @param {string} mode - نمط المعاملة
   * @param {Function} callback - دالة المعاملة
   * @returns {Promise<*>} نتيجة المعاملة
   */
  async transaction(storeNames, mode, callback) {
    await this._ensureConnection();
    
    try {
      const transaction = this.db.transaction(storeNames, mode);
      const stores = {};
      
      // إنشاء كائن المخازن
      storeNames.forEach(storeName => {
        stores[storeName] = transaction.objectStore(storeName);
      });
      
      // تنفيذ المعاملة
      const result = await callback(stores, transaction);
      
      // انتظار اكتمال المعاملة
      await this._promisifyTransaction(transaction);
      
      this._updateStats('operations');
      
      return result;

    } catch (error) {
      this._handleError(error, 'transaction', { storeNames, mode });
      throw error;
    }
  }

  /**
   * نسخ احتياطي من قاعدة البيانات
   * @returns {Promise<Object>} البيانات المنسوخة
   */
  async backup() {
    await this._ensureConnection();
    
    try {
      const backup = {
        version: this.dbVersion,
        timestamp: new Date().toISOString(),
        data: {}
      };
      
      // نسخ جميع المخازن
      for (const storeName of Object.values(this.stores)) {
        backup.data[storeName] = await this.getAll(storeName);
      }
      
      console.log('💾 تم إنشاء نسخة احتياطية من قاعدة البيانات');
      
      return backup;

    } catch (error) {
      this._handleError(error, 'backup');
      throw error;
    }
  }

  /**
   * استعادة من نسخة احتياطية
   * @param {Object} backupData - بيانات النسخة الاحتياطية
   * @param {boolean} clearFirst - مسح البيانات الموجودة أولاً
   * @returns {Promise<boolean>} نجحت الاستعادة أم لا
   */
  async restore(backupData, clearFirst = true) {
    await this._ensureConnection();
    
    try {
      if (clearFirst) {
        // مسح جميع المخازن
        for (const storeName of Object.values(this.stores)) {
          await this.clear(storeName);
        }
      }
      
      // استعادة البيانات
      for (const [storeName, items] of Object.entries(backupData.data)) {
        if (Object.values(this.stores).includes(storeName)) {
          for (const item of items) {
            await this.add(storeName, item);
          }
        }
      }
      
      console.log('📥 تم استعادة قاعدة البيانات من النسخة الاحتياطية');
      
      eventBus.emit('database:restored', { backupData });
      
      return true;

    } catch (error) {
      this._handleError(error, 'restore', { backupData });
      throw error;
    }
  }

  /**
   * الحصول على إحصائيات قاعدة البيانات
   * @returns {Promise<Object>} الإحصائيات
   */
  async getStats() {
    const dbStats = { ...this.stats };
    
    if (this.isConnected) {
      // إحصائيات المخازن
      dbStats.stores = {};
      
      for (const storeName of Object.values(this.stores)) {
        try {
          dbStats.stores[storeName] = await this.count(storeName);
        } catch (error) {
          dbStats.stores[storeName] = 0;
        }
      }
      
      // حجم قاعدة البيانات (تقديري)
      if (navigator.storage && navigator.storage.estimate) {
        const estimate = await navigator.storage.estimate();
        dbStats.storageUsed = estimate.usage;
        dbStats.storageQuota = estimate.quota;
      }
    }
    
    return dbStats;
  }

  /**
   * إغلاق الاتصال بقاعدة البيانات
   */
  close() {
    if (this.db) {
      this.db.close();
      this.db = null;
      this.isConnected = false;
      
      eventBus.emit('database:disconnected');
      
      console.log('🔌 تم إغلاق الاتصال بقاعدة البيانات');
    }
  }

  /**
   * حذف قاعدة البيانات بالكامل
   * @returns {Promise<boolean>} نجح الحذف أم لا
   */
  async deleteDatabase() {
    try {
      this.close();
      
      const deleteRequest = indexedDB.deleteDatabase(this.dbName);
      await this._promisifyRequest(deleteRequest);
      
      console.log('🗑️ تم حذف قاعدة البيانات بالكامل');
      
      eventBus.emit('database:deleted');
      
      return true;

    } catch (error) {
      this._handleError(error, 'deleteDatabase');
      throw error;
    }
  }

  /**
   * تفعيل/إلغاء وضع التشخيص
   * @param {boolean} enabled - تفعيل التشخيص
   */
  setDebug(enabled) {
    this.debug = enabled;
    console.log(`🔍 وضع التشخيص لقاعدة البيانات: ${enabled ? 'مفعل' : 'معطل'}`);
  }

  // دوال خاصة

  /**
   * التحقق من دعم IndexedDB
   * @private
   */
  _isIndexedDBSupported() {
    return 'indexedDB' in window;
  }

  /**
   * فتح قاعدة البيانات
   * @private
   */
  _openDatabase() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.dbVersion);
      
      request.onerror = () => {
        reject(new Error(`فشل في فتح قاعدة البيانات: ${request.error}`));
      };
      
      request.onsuccess = () => {
        resolve(request.result);
      };
      
      request.onupgradeneeded = (event) => {
        const db = event.target.result;
        this._upgradeDatabase(db, event.oldVersion, event.newVersion);
      };
    });
  }

  /**
   * ترقية قاعدة البيانات
   * @private
   */
  _upgradeDatabase(db, oldVersion, newVersion) {
    console.log(`🔄 ترقية قاعدة البيانات من الإصدار ${oldVersion} إلى ${newVersion}`);
    
    // إنشاء المخازن المطلوبة
    Object.entries(this.stores).forEach(([key, storeName]) => {
      if (!db.objectStoreNames.contains(storeName)) {
        const store = db.createObjectStore(storeName, { 
          keyPath: 'id', 
          autoIncrement: true 
        });
        
        // إضافة فهارس حسب الحاجة
        this._createIndexes(store, storeName);
        
        console.log(`📦 تم إنشاء مخزن البيانات: ${storeName}`);
      }
    });
  }

  /**
   * إنشاء الفهارس
   * @private
   */
  _createIndexes(store, storeName) {
    switch (storeName) {
      case this.stores.MESSAGES:
        store.createIndex('chatID', 'chatID', { unique: false });
        store.createIndex('senderID', 'senderID', { unique: false });
        store.createIndex('createdDate', 'createdDate', { unique: false });
        break;
        
      case this.stores.CHATS:
        store.createIndex('lastActivity', 'lastActivity', { unique: false });
        store.createIndex('isArchived', 'isArchived', { unique: false });
        break;
        
      case this.stores.USERS:
        store.createIndex('email', 'email', { unique: true });
        store.createIndex('userName', 'userName', { unique: true });
        break;
        
      case this.stores.CONTACTS:
        store.createIndex('userId', 'userId', { unique: false });
        break;
    }
  }

  /**
   * التأكد من الاتصال
   * @private
   */
  async _ensureConnection() {
    if (!this.isConnected) {
      const connected = await this.initialize();
      if (!connected) {
        throw new Error('فشل في الاتصال بقاعدة البيانات');
      }
    }
  }

  /**
   * تحويل طلب إلى Promise
   * @private
   */
  _promisifyRequest(request) {
    return new Promise((resolve, reject) => {
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }

  /**
   * تحويل معاملة إلى Promise
   * @private
   */
  _promisifyTransaction(transaction) {
    return new Promise((resolve, reject) => {
      transaction.oncomplete = () => resolve();
      transaction.onerror = () => reject(transaction.error);
      transaction.onabort = () => reject(new Error('تم إلغاء المعاملة'));
    });
  }

  /**
   * تحديث الإحصائيات
   * @private
   */
  _updateStats(operation) {
    this.stats.operations++;
    this.stats[operation]++;
    this.stats.lastOperation = {
      type: operation,
      timestamp: new Date()
    };
  }

  /**
   * معالجة الأخطاء
   * @private
   */
  _handleError(error, operation, context = {}) {
    this.stats.errors++;
    
    errorHandler.handleError(error, {
      type: 'database',
      operation,
      ...context
    });
  }
}

// إنشاء مثيل واحد من مدير قاعدة البيانات
const databaseManager = new DatabaseManager();

// تصدير المثيل والكلاس
export default databaseManager;
export { DatabaseManager };
