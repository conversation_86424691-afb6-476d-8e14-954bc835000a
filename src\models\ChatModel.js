/**
 * نموذج المحادثة - ChatModel
 * يمثل بنية بيانات المحادثة مع دوال التحقق والتحويل
 * يدعم المحادثات الفردية والجماعية مع إدارة الأعضاء
 */

import { formatChatListDate, parseDate } from '../utils/DateUtils.js';
import { sanitizeInput, isValidLength } from '../utils/ValidationUtils.js';

// أنواع المحادثات
export const CHAT_TYPES = {
  PRIVATE: 'Private',
  GROUP: 'Group',
  TEAM: 'Team',
  CHANNEL: 'Channel'
};

// حالات المحادثة
export const CHAT_STATUS = {
  ACTIVE: 'Active',
  ARCHIVED: 'Archived',
  DELETED: 'Deleted',
  BLOCKED: 'Blocked'
};

// أدوار الأعضاء
export const MEMBER_ROLES = {
  OWNER: 'Owner',
  ADMIN: 'Admin',
  MODERATOR: 'Moderator',
  MEMBER: 'Member'
};

/**
 * كلاس نموذج المحادثة
 */
export class ChatModel {
  constructor(data = {}) {
    // الخصائص الأساسية
    this.id = data.id || null;
    this.name = data.name || '';
    this.description = data.description || '';
    this.type = data.type || CHAT_TYPES.PRIVATE;
    this.status = data.status || CHAT_STATUS.ACTIVE;
    
    // الصورة والأيقونة
    this.image = data.image || '';
    this.icon = data.icon || '';
    this.color = data.color || '#007bff';
    
    // معلومات آخر رسالة
    this.lastMessage = data.lastMessage || null;
    this.lastMessageDate = data.lastMessageDate ? parseDate(data.lastMessageDate) : null;
    this.lastMessageSender = data.lastMessageSender || null;
    
    // إحصائيات
    this.unreadCount = data.unreadCount || 0;
    this.totalMessages = data.totalMessages || 0;
    this.memberCount = data.memberCount || 0;
    
    // الأعضاء
    this.members = data.members || [];
    this.admins = data.admins || [];
    this.owner = data.owner || null;
    
    // إعدادات المحادثة
    this.settings = {
      muteNotifications: data.settings?.muteNotifications || false,
      allowInvites: data.settings?.allowInvites || true,
      allowFileSharing: data.settings?.allowFileSharing || true,
      allowVoiceMessages: data.settings?.allowVoiceMessages || true,
      messageRetention: data.settings?.messageRetention || 0, // 0 = لا نهاية
      ...data.settings
    };
    
    // معلومات التوقيت
    this.createdDate = data.createdDate ? parseDate(data.createdDate) : new Date();
    this.updatedDate = data.updatedDate ? parseDate(data.updatedDate) : new Date();
    this.lastActivity = data.lastActivity ? parseDate(data.lastActivity) : new Date();
    
    // معلومات إضافية
    this.metadata = data.metadata || {};
    this.tags = data.tags || [];
    this.isArchived = data.isArchived || false;
    this.isPinned = data.isPinned || false;
    this.isBlocked = data.isBlocked || false;
    
    // معلومات المزامنة
    this._version = data._version || 1;
    this._lastSync = data._lastSync ? parseDate(data._lastSync) : null;
    this._isLocal = data._isLocal || false;
  }

  /**
   * التحقق من صحة بيانات المحادثة
   * @returns {Object} نتيجة التحقق
   */
  validate() {
    const errors = [];
    
    // التحقق من الاسم
    if (!isValidLength(this.name, 1, 100)) {
      errors.push('اسم المحادثة يجب أن يكون بين 1 و 100 حرف');
    }
    
    // التحقق من النوع
    if (!Object.values(CHAT_TYPES).includes(this.type)) {
      errors.push('نوع المحادثة غير صحيح');
    }
    
    // التحقق من الحالة
    if (!Object.values(CHAT_STATUS).includes(this.status)) {
      errors.push('حالة المحادثة غير صحيحة');
    }
    
    // التحقق من الوصف
    if (this.description && !isValidLength(this.description, 0, 500)) {
      errors.push('وصف المحادثة يجب أن يكون أقل من 500 حرف');
    }
    
    // التحقق من عدد الرسائل غير المقروءة
    if (this.unreadCount < 0) {
      errors.push('عدد الرسائل غير المقروءة لا يمكن أن يكون سالب');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * تنظيف وتطهير البيانات
   * @returns {ChatModel} نسخة منظفة من المحادثة
   */
  sanitize() {
    const sanitized = new ChatModel({
      ...this.toObject(),
      name: sanitizeInput(this.name, { maxLength: 100 }),
      description: sanitizeInput(this.description, { maxLength: 500 })
    });
    
    return sanitized;
  }

  /**
   * تحديث آخر رسالة
   * @param {Object} messageData - بيانات الرسالة
   */
  updateLastMessage(messageData) {
    this.lastMessage = messageData.messageText || messageData.content || '';
    this.lastMessageDate = messageData.createdDate ? parseDate(messageData.createdDate) : new Date();
    this.lastMessageSender = messageData.senderName || messageData.sender?.name || '';
    this.lastActivity = new Date();
    this.updatedDate = new Date();
    this._version++;
  }

  /**
   * زيادة عدد الرسائل غير المقروءة
   * @param {number} count - العدد المراد إضافته
   */
  incrementUnreadCount(count = 1) {
    this.unreadCount = Math.max(0, this.unreadCount + count);
    this.updatedDate = new Date();
    this._version++;
  }

  /**
   * مسح عدد الرسائل غير المقروءة
   */
  clearUnreadCount() {
    this.unreadCount = 0;
    this.updatedDate = new Date();
    this._version++;
  }

  /**
   * إضافة عضو جديد
   * @param {Object} member - بيانات العضو
   * @param {string} role - دور العضو
   */
  addMember(member, role = MEMBER_ROLES.MEMBER) {
    // التحقق من عدم وجود العضو مسبقاً
    if (!this.members.find(m => m.id === member.id)) {
      this.members.push({
        ...member,
        role,
        joinedDate: new Date(),
        isActive: true
      });
      
      this.memberCount = this.members.length;
      this.updatedDate = new Date();
      this._version++;
    }
  }

  /**
   * إزالة عضو
   * @param {string} memberId - معرف العضو
   */
  removeMember(memberId) {
    const index = this.members.findIndex(m => m.id === memberId);
    if (index !== -1) {
      this.members.splice(index, 1);
      this.memberCount = this.members.length;
      this.updatedDate = new Date();
      this._version++;
    }
  }

  /**
   * تحديث دور عضو
   * @param {string} memberId - معرف العضو
   * @param {string} newRole - الدور الجديد
   */
  updateMemberRole(memberId, newRole) {
    const member = this.members.find(m => m.id === memberId);
    if (member && Object.values(MEMBER_ROLES).includes(newRole)) {
      member.role = newRole;
      this.updatedDate = new Date();
      this._version++;
    }
  }

  /**
   * التحقق من كون المستخدم عضو
   * @param {string} userId - معرف المستخدم
   * @returns {boolean} هل هو عضو
   */
  isMember(userId) {
    return this.members.some(m => m.id === userId && m.isActive);
  }

  /**
   * التحقق من كون المستخدم مدير
   * @param {string} userId - معرف المستخدم
   * @returns {boolean} هل هو مدير
   */
  isAdmin(userId) {
    const member = this.members.find(m => m.id === userId);
    return member && [MEMBER_ROLES.ADMIN, MEMBER_ROLES.OWNER].includes(member.role);
  }

  /**
   * التحقق من كون المستخدم مالك المحادثة
   * @param {string} userId - معرف المستخدم
   * @returns {boolean} هل هو المالك
   */
  isOwner(userId) {
    return this.owner?.id === userId;
  }

  /**
   * أرشفة المحادثة
   */
  archive() {
    this.isArchived = true;
    this.status = CHAT_STATUS.ARCHIVED;
    this.updatedDate = new Date();
    this._version++;
  }

  /**
   * إلغاء أرشفة المحادثة
   */
  unarchive() {
    this.isArchived = false;
    this.status = CHAT_STATUS.ACTIVE;
    this.updatedDate = new Date();
    this._version++;
  }

  /**
   * تثبيت المحادثة
   */
  pin() {
    this.isPinned = true;
    this.updatedDate = new Date();
    this._version++;
  }

  /**
   * إلغاء تثبيت المحادثة
   */
  unpin() {
    this.isPinned = false;
    this.updatedDate = new Date();
    this._version++;
  }

  /**
   * حظر المحادثة
   */
  block() {
    this.isBlocked = true;
    this.status = CHAT_STATUS.BLOCKED;
    this.updatedDate = new Date();
    this._version++;
  }

  /**
   * إلغاء حظر المحادثة
   */
  unblock() {
    this.isBlocked = false;
    this.status = CHAT_STATUS.ACTIVE;
    this.updatedDate = new Date();
    this._version++;
  }

  /**
   * تحديث إعدادات المحادثة
   * @param {Object} newSettings - الإعدادات الجديدة
   */
  updateSettings(newSettings) {
    this.settings = { ...this.settings, ...newSettings };
    this.updatedDate = new Date();
    this._version++;
  }

  /**
   * إضافة علامة
   * @param {string} tag - العلامة
   */
  addTag(tag) {
    if (typeof tag === 'string' && !this.tags.includes(tag)) {
      this.tags.push(tag);
      this.updatedDate = new Date();
      this._version++;
    }
  }

  /**
   * إزالة علامة
   * @param {string} tag - العلامة
   */
  removeTag(tag) {
    const index = this.tags.indexOf(tag);
    if (index !== -1) {
      this.tags.splice(index, 1);
      this.updatedDate = new Date();
      this._version++;
    }
  }

  /**
   * الحصول على اسم المحادثة للعرض
   * @param {Object} currentUser - المستخدم الحالي
   * @returns {string} اسم المحادثة
   */
  getDisplayName(currentUser = null) {
    if (this.name) {
      return this.name;
    }
    
    // للمحادثات الخاصة، استخدم اسم الطرف الآخر
    if (this.type === CHAT_TYPES.PRIVATE && this.members.length === 2 && currentUser) {
      const otherMember = this.members.find(m => m.id !== currentUser.id);
      return otherMember ? otherMember.name : 'محادثة خاصة';
    }
    
    return 'محادثة جديدة';
  }

  /**
   * الحصول على صورة المحادثة للعرض
   * @param {Object} currentUser - المستخدم الحالي
   * @returns {string} رابط الصورة
   */
  getDisplayImage(currentUser = null) {
    if (this.image) {
      return this.image;
    }
    
    // للمحادثات الخاصة، استخدم صورة الطرف الآخر
    if (this.type === CHAT_TYPES.PRIVATE && this.members.length === 2 && currentUser) {
      const otherMember = this.members.find(m => m.id !== currentUser.id);
      return otherMember?.image || '';
    }
    
    return '';
  }

  /**
   * الحصول على نص آخر رسالة للعرض
   * @returns {string} نص آخر رسالة
   */
  getLastMessageText() {
    if (!this.lastMessage) {
      return 'لا توجد رسائل';
    }
    
    // تقصير النص إذا كان طويل
    const maxLength = 50;
    if (this.lastMessage.length > maxLength) {
      return this.lastMessage.substring(0, maxLength) + '...';
    }
    
    return this.lastMessage;
  }

  /**
   * الحصول على تاريخ آخر رسالة منسق
   * @returns {string} التاريخ المنسق
   */
  getFormattedLastMessageDate() {
    return formatChatListDate(this.lastMessageDate);
  }

  /**
   * التحقق من كون المحادثة نشطة
   * @returns {boolean} هل المحادثة نشطة
   */
  isActive() {
    return this.status === CHAT_STATUS.ACTIVE && !this.isBlocked;
  }

  /**
   * التحقق من وجود رسائل غير مقروءة
   * @returns {boolean} هل توجد رسائل غير مقروءة
   */
  hasUnreadMessages() {
    return this.unreadCount > 0;
  }

  /**
   * الحصول على نص عدد الرسائل غير المقروءة
   * @returns {string} نص العدد
   */
  getUnreadCountText() {
    if (this.unreadCount === 0) return '';
    if (this.unreadCount > 99) return '99+';
    return this.unreadCount.toString();
  }

  /**
   * تحديث وقت آخر نشاط
   */
  updateLastActivity() {
    this.lastActivity = new Date();
    this.updatedDate = new Date();
    this._version++;
  }

  /**
   * تحديث وقت المزامنة
   */
  updateSyncTime() {
    this._lastSync = new Date();
  }

  /**
   * تحويل إلى كائن عادي
   * @returns {Object} كائن البيانات
   */
  toObject() {
    return {
      id: this.id,
      name: this.name,
      description: this.description,
      type: this.type,
      status: this.status,
      image: this.image,
      icon: this.icon,
      color: this.color,
      lastMessage: this.lastMessage,
      lastMessageDate: this.lastMessageDate?.toISOString(),
      lastMessageSender: this.lastMessageSender,
      unreadCount: this.unreadCount,
      totalMessages: this.totalMessages,
      memberCount: this.memberCount,
      members: this.members,
      admins: this.admins,
      owner: this.owner,
      settings: this.settings,
      createdDate: this.createdDate?.toISOString(),
      updatedDate: this.updatedDate?.toISOString(),
      lastActivity: this.lastActivity?.toISOString(),
      metadata: this.metadata,
      tags: this.tags,
      isArchived: this.isArchived,
      isPinned: this.isPinned,
      isBlocked: this.isBlocked,
      _version: this._version,
      _lastSync: this._lastSync?.toISOString(),
      _isLocal: this._isLocal
    };
  }

  /**
   * تحويل إلى JSON
   * @returns {string} نص JSON
   */
  toJSON() {
    return JSON.stringify(this.toObject());
  }

  /**
   * إنشاء نسخة من المحادثة
   * @returns {ChatModel} نسخة جديدة
   */
  clone() {
    return new ChatModel(this.toObject());
  }

  /**
   * دمج بيانات محادثة أخرى
   * @param {ChatModel|Object} otherChat - المحادثة الأخرى
   * @param {boolean} overwrite - استبدال البيانات الموجودة
   */
  merge(otherChat, overwrite = false) {
    const otherData = otherChat instanceof ChatModel ? otherChat.toObject() : otherChat;
    
    Object.entries(otherData).forEach(([key, value]) => {
      if (value !== null && value !== undefined && (overwrite || this[key] === null || this[key] === undefined)) {
        if (key.includes('Date') && typeof value === 'string') {
          this[key] = parseDate(value);
        } else {
          this[key] = value;
        }
      }
    });
    
    this.updatedDate = new Date();
    this._version++;
  }

  /**
   * إنشاء محادثة من كائن البيانات
   * @static
   * @param {Object} data - بيانات المحادثة
   * @returns {ChatModel} نموذج المحادثة
   */
  static fromObject(data) {
    return new ChatModel(data);
  }

  /**
   * إنشاء محادثة من JSON
   * @static
   * @param {string} jsonString - نص JSON
   * @returns {ChatModel} نموذج المحادثة
   */
  static fromJSON(jsonString) {
    try {
      const data = JSON.parse(jsonString);
      return new ChatModel(data);
    } catch (error) {
      throw new Error('فشل في تحليل JSON للمحادثة');
    }
  }

  /**
   * إنشاء محادثة خاصة جديدة
   * @static
   * @param {Object} user1 - المستخدم الأول
   * @param {Object} user2 - المستخدم الثاني
   * @returns {ChatModel} محادثة خاصة جديدة
   */
  static createPrivateChat(user1, user2) {
    return new ChatModel({
      type: CHAT_TYPES.PRIVATE,
      members: [
        { ...user1, role: MEMBER_ROLES.MEMBER, joinedDate: new Date(), isActive: true },
        { ...user2, role: MEMBER_ROLES.MEMBER, joinedDate: new Date(), isActive: true }
      ],
      memberCount: 2,
      createdDate: new Date(),
      updatedDate: new Date(),
      lastActivity: new Date()
    });
  }

  /**
   * إنشاء مجموعة جديدة
   * @static
   * @param {string} name - اسم المجموعة
   * @param {Object} creator - منشئ المجموعة
   * @param {Array} members - الأعضاء الأوليين
   * @returns {ChatModel} مجموعة جديدة
   */
  static createGroup(name, creator, members = []) {
    const groupMembers = [
      { ...creator, role: MEMBER_ROLES.OWNER, joinedDate: new Date(), isActive: true },
      ...members.map(member => ({
        ...member,
        role: MEMBER_ROLES.MEMBER,
        joinedDate: new Date(),
        isActive: true
      }))
    ];

    return new ChatModel({
      name,
      type: CHAT_TYPES.GROUP,
      owner: creator,
      members: groupMembers,
      memberCount: groupMembers.length,
      createdDate: new Date(),
      updatedDate: new Date(),
      lastActivity: new Date()
    });
  }
}

// تصدير النموذج والثوابت
export default ChatModel;
export { CHAT_TYPES, CHAT_STATUS, MEMBER_ROLES };
