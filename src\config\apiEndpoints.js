/**
 * نقاط نهاية API - API Endpoints
 * يحتوي على جميع نقاط النهاية المستخدمة في التطبيق
 * منظم حسب الوحدات لسهولة الوصول والصيانة
 */

// نقاط نهاية المصادقة
export const AUTH_ENDPOINTS = {
  // تسجيل الدخول والخروج
  LOGIN: 'Authentication/Login',
  LOGOUT: 'Authentication/Logout',
  REFRESH_TOKEN: 'Authentication/RefreshToken',
  
  // الملف الشخصي
  PROFILE: 'Authentication/profile',
  UPDATE_PROFILE: 'Authentication/UpdateProfile',
  CHANGE_PASSWORD: 'Authentication/ChangePassword',
  
  // التحقق
  VERIFY_EMAIL: 'Authentication/VerifyEmail',
  VERIFY_PHONE: 'Authentication/VerifyPhone',
  SEND_VERIFICATION: 'Authentication/SendVerification',
  
  // إعادة تعيين كلمة المرور
  FORGOT_PASSWORD: 'Authentication/ForgotPassword',
  RESET_PASSWORD: 'Authentication/ResetPassword',
  VALIDATE_RESET_TOKEN: 'Authentication/ValidateResetToken',
  
  // المصادقة الثنائية
  ENABLE_2FA: 'Authentication/Enable2FA',
  DISABLE_2FA: 'Authentication/Disable2FA',
  VERIFY_2FA: 'Authentication/Verify2FA',
  GENERATE_2FA_SECRET: 'Authentication/Generate2FASecret'
};

// نقاط نهاية المحادثات
export const CHAT_ENDPOINTS = {
  // العمليات الأساسية
  GET_ALL: 'Chat/GetAllChats',
  GET_BY_ID: (chatId) => `Chat/GetChat/${chatId}`,
  CREATE: 'Chat/CreateChat',
  UPDATE: (chatId) => `Chat/UpdateChat/${chatId}`,
  DELETE: (chatId) => `Chat/DeleteChat/${chatId}`,
  
  // إدارة الأعضاء
  GET_MEMBERS: (chatId) => `Chat/GetChatMembers/${chatId}`,
  ADD_MEMBER: (chatId) => `Chat/AddMember/${chatId}`,
  REMOVE_MEMBER: (chatId) => `Chat/RemoveMember/${chatId}`,
  UPDATE_MEMBER_ROLE: (chatId, memberId) => `Chat/UpdateMemberRole/${chatId}/${memberId}`,
  
  // إعدادات المحادثة
  UPDATE_SETTINGS: (chatId) => `Chat/UpdateSettings/${chatId}`,
  UPDATE_PERMISSIONS: (chatId) => `Chat/UpdatePermissions/${chatId}`,
  
  // الحالة والنشاط
  MARK_READ: (chatId) => `Chat/MakeRead/${chatId}`,
  UPDATE_LAST_SEEN: (chatId) => `Chat/UpdateLastSeen/${chatId}`,
  SET_TYPING: (chatId) => `Chat/SetTyping/${chatId}`,
  
  // الأرشفة والتثبيت
  ARCHIVE: (chatId) => `Chat/Archive/${chatId}`,
  UNARCHIVE: (chatId) => `Chat/Unarchive/${chatId}`,
  PIN: (chatId) => `Chat/Pin/${chatId}`,
  UNPIN: (chatId) => `Chat/Unpin/${chatId}`,
  
  // البحث والفلترة
  SEARCH: 'Chat/SearchChats',
  FILTER: 'Chat/FilterChats',
  GET_ARCHIVED: 'Chat/GetArchivedChats',
  GET_PINNED: 'Chat/GetPinnedChats',
  
  // الإحصائيات
  GET_STATS: (chatId) => `Chat/GetStats/${chatId}`,
  GET_ACTIVITY: (chatId) => `Chat/GetActivity/${chatId}`
};

// نقاط نهاية الرسائل
export const MESSAGE_ENDPOINTS = {
  // إرسال الرسائل
  SEND_TEXT: 'Message/CreateTextMessage',
  SEND_FILE: 'Message/CreateFileMessage',
  SEND_IMAGE: 'Message/CreateImageMessage',
  SEND_VIDEO: 'Message/CreateVideoMessage',
  SEND_AUDIO: 'Message/CreateAudioMessage',
  SEND_VOICE: 'Message/CreateVoiceMessage',
  SEND_LOCATION: 'Message/CreateLocationMessage',
  SEND_CONTACT: 'Message/CreateContactMessage',
  SEND_POLL: 'Message/CreatePollMessage',
  
  // إدارة الرسائل
  GET_BY_CHAT: (chatId) => `Message/GetMessagesByChat/${chatId}`,
  GET_BY_ID: (messageId) => `Message/GetMessage/${messageId}`,
  UPDATE: (messageId) => `Message/UpdateMessage/${messageId}`,
  DELETE: (messageId) => `Message/DeleteMessage/${messageId}`,
  
  // حالة الرسائل
  MARK_READ: (messageId) => `Message/MarkAsRead/${messageId}`,
  MARK_DELIVERED: (messageId) => `Message/MarkAsDelivered/${messageId}`,
  UPDATE_STATUS: (messageId) => `Message/UpdateStatus/${messageId}`,
  
  // التفاعلات
  ADD_REACTION: (messageId) => `Message/AddReaction/${messageId}`,
  REMOVE_REACTION: (messageId) => `Message/RemoveReaction/${messageId}`,
  GET_REACTIONS: (messageId) => `Message/GetReactions/${messageId}`,
  
  // الرد والإعادة توجيه
  REPLY: (messageId) => `Message/Reply/${messageId}`,
  FORWARD: (messageId) => `Message/Forward/${messageId}`,
  QUOTE: (messageId) => `Message/Quote/${messageId}`,
  
  // البحث والفلترة
  SEARCH: 'Message/SearchMessages',
  SEARCH_IN_CHAT: (chatId) => `Message/SearchInChat/${chatId}`,
  GET_MEDIA: (chatId) => `Message/GetMediaMessages/${chatId}`,
  GET_FILES: (chatId) => `Message/GetFileMessages/${chatId}`,
  
  // الاستطلاعات
  VOTE_POLL: (messageId) => `Message/VotePoll/${messageId}`,
  GET_POLL_RESULTS: (messageId) => `Message/GetPollResults/${messageId}`,
  CLOSE_POLL: (messageId) => `Message/ClosePoll/${messageId}`,
  
  // التقارير والإحصائيات
  REPORT: (messageId) => `Message/Report/${messageId}`,
  GET_STATS: (chatId) => `Message/GetStats/${chatId}`,
  
  // التصدير والنسخ الاحتياطي
  EXPORT: (chatId) => `Message/Export/${chatId}`,
  BACKUP: (chatId) => `Message/Backup/${chatId}`
};

// نقاط نهاية جهات الاتصال
export const CONTACT_ENDPOINTS = {
  // العمليات الأساسية
  GET_ALL: 'Contact/GetAllContacts',
  GET_BY_ID: (contactId) => `Contact/GetContact/${contactId}`,
  ADD: 'Contact/AddContact',
  UPDATE: (contactId) => `Contact/UpdateContact/${contactId}`,
  DELETE: (contactId) => `Contact/DeleteContact/${contactId}`,
  
  // البحث والاستيراد
  SEARCH: 'Contact/SearchContacts',
  SEARCH_BY_PHONE: 'Contact/SearchByPhone',
  SEARCH_BY_EMAIL: 'Contact/SearchByEmail',
  IMPORT: 'Contact/ImportContacts',
  EXPORT: 'Contact/ExportContacts',
  
  // المجموعات والتصنيفات
  GET_GROUPS: 'Contact/GetContactGroups',
  CREATE_GROUP: 'Contact/CreateContactGroup',
  ADD_TO_GROUP: (contactId, groupId) => `Contact/AddToGroup/${contactId}/${groupId}`,
  REMOVE_FROM_GROUP: (contactId, groupId) => `Contact/RemoveFromGroup/${contactId}/${groupId}`,
  
  // الحظر والإلغاء
  BLOCK: (contactId) => `Contact/Block/${contactId}`,
  UNBLOCK: (contactId) => `Contact/Unblock/${contactId}`,
  GET_BLOCKED: 'Contact/GetBlockedContacts',
  
  // المفضلة
  ADD_TO_FAVORITES: (contactId) => `Contact/AddToFavorites/${contactId}`,
  REMOVE_FROM_FAVORITES: (contactId) => `Contact/RemoveFromFavorites/${contactId}`,
  GET_FAVORITES: 'Contact/GetFavoriteContacts',
  
  // المزامنة
  SYNC: 'Contact/SyncContacts',
  GET_SYNC_STATUS: 'Contact/GetSyncStatus'
};

// نقاط نهاية المستخدمين
export const USER_ENDPOINTS = {
  // العمليات الأساسية
  GET_ALL: 'User/GetAllUsers',
  GET_BY_ID: (userId) => `User/GetUser/${userId}`,
  UPDATE_PROFILE: 'User/UpdateProfile',
  DELETE_ACCOUNT: 'User/DeleteAccount',
  
  // الحالة والنشاط
  UPDATE_STATUS: 'User/UpdateStatus',
  UPDATE_LAST_SEEN: 'User/UpdateLastSeen',
  GET_ONLINE_USERS: 'User/GetOnlineUsers',
  
  // البحث والاكتشاف
  SEARCH: 'User/SearchUsers',
  SEARCH_BY_USERNAME: 'User/SearchByUsername',
  SEARCH_BY_PHONE: 'User/SearchByPhone',
  GET_SUGGESTIONS: 'User/GetUserSuggestions',
  
  // الخصوصية والأمان
  UPDATE_PRIVACY_SETTINGS: 'User/UpdatePrivacySettings',
  UPDATE_SECURITY_SETTINGS: 'User/UpdateSecuritySettings',
  GET_PRIVACY_SETTINGS: 'User/GetPrivacySettings',
  GET_SECURITY_SETTINGS: 'User/GetSecuritySettings',
  
  // الجلسات والأجهزة
  GET_ACTIVE_SESSIONS: 'User/GetActiveSessions',
  TERMINATE_SESSION: (sessionId) => `User/TerminateSession/${sessionId}`,
  TERMINATE_ALL_SESSIONS: 'User/TerminateAllSessions',
  
  // الإحصائيات والتقارير
  GET_STATS: 'User/GetUserStats',
  GET_ACTIVITY_LOG: 'User/GetActivityLog',
  
  // التحقق والشارات
  REQUEST_VERIFICATION: 'User/RequestVerification',
  GET_VERIFICATION_STATUS: 'User/GetVerificationStatus'
};

// نقاط نهاية الفرق
export const TEAM_ENDPOINTS = {
  // العمليات الأساسية
  GET_ALL: 'Team/GetAllTeams',
  GET_BY_ID: (teamId) => `Team/GetTeam/${teamId}`,
  CREATE: 'Team/CreateTeam',
  UPDATE: (teamId) => `Team/UpdateTeam/${teamId}`,
  DELETE: (teamId) => `Team/DeleteTeam/${teamId}`,
  
  // إدارة الأعضاء
  GET_MEMBERS: (teamId) => `Team/GetTeamMembers/${teamId}`,
  ADD_MEMBER: (teamId) => `Team/AddMember/${teamId}`,
  REMOVE_MEMBER: (teamId) => `Team/RemoveMember/${teamId}`,
  UPDATE_MEMBER_ROLE: (teamId, memberId) => `Team/UpdateMemberRole/${teamId}/${memberId}`,
  
  // الدعوات
  SEND_INVITATION: (teamId) => `Team/SendInvitation/${teamId}`,
  ACCEPT_INVITATION: (invitationId) => `Team/AcceptInvitation/${invitationId}`,
  DECLINE_INVITATION: (invitationId) => `Team/DeclineInvitation/${invitationId}`,
  GET_INVITATIONS: 'Team/GetInvitations',
  
  // الإعدادات والأذونات
  UPDATE_SETTINGS: (teamId) => `Team/UpdateSettings/${teamId}`,
  UPDATE_PERMISSIONS: (teamId) => `Team/UpdatePermissions/${teamId}`,
  
  // الإحصائيات
  GET_STATS: (teamId) => `Team/GetStats/${teamId}`,
  GET_ACTIVITY: (teamId) => `Team/GetActivity/${teamId}`
};

// نقاط نهاية الملفات
export const FILE_ENDPOINTS = {
  // رفع وتحميل
  UPLOAD: 'File/Upload',
  UPLOAD_MULTIPLE: 'File/UploadMultiple',
  DOWNLOAD: (fileId) => `File/Download/${fileId}`,
  DOWNLOAD_THUMBNAIL: (fileId) => `File/DownloadThumbnail/${fileId}`,
  
  // إدارة الملفات
  GET_INFO: (fileId) => `File/GetInfo/${fileId}`,
  UPDATE_INFO: (fileId) => `File/UpdateInfo/${fileId}`,
  DELETE: (fileId) => `File/Delete/${fileId}`,
  
  // البحث والفلترة
  SEARCH: 'File/SearchFiles',
  GET_BY_TYPE: (type) => `File/GetByType/${type}`,
  GET_BY_CHAT: (chatId) => `File/GetByChat/${chatId}`,
  GET_RECENT: 'File/GetRecentFiles',
  
  // المعاينة والتحويل
  GENERATE_THUMBNAIL: (fileId) => `File/GenerateThumbnail/${fileId}`,
  CONVERT: (fileId) => `File/Convert/${fileId}`,
  COMPRESS: (fileId) => `File/Compress/${fileId}`,
  
  // المشاركة والأذونات
  SHARE: (fileId) => `File/Share/${fileId}`,
  UPDATE_PERMISSIONS: (fileId) => `File/UpdatePermissions/${fileId}`,
  GET_SHARED_FILES: 'File/GetSharedFiles',
  
  // الإحصائيات والتخزين
  GET_STORAGE_STATS: 'File/GetStorageStats',
  CLEANUP_TEMP_FILES: 'File/CleanupTempFiles'
};

// نقاط نهاية الإشعارات
export const NOTIFICATION_ENDPOINTS = {
  // العمليات الأساسية
  GET_ALL: 'Notification/GetAllNotifications',
  GET_UNREAD: 'Notification/GetUnreadNotifications',
  MARK_READ: (notificationId) => `Notification/MarkAsRead/${notificationId}`,
  MARK_ALL_READ: 'Notification/MarkAllAsRead',
  DELETE: (notificationId) => `Notification/Delete/${notificationId}`,
  
  // الإعدادات
  GET_SETTINGS: 'Notification/GetSettings',
  UPDATE_SETTINGS: 'Notification/UpdateSettings',
  
  // الاشتراكات
  SUBSCRIBE: 'Notification/Subscribe',
  UNSUBSCRIBE: 'Notification/Unsubscribe',
  UPDATE_SUBSCRIPTION: 'Notification/UpdateSubscription',
  
  // Push Notifications
  REGISTER_DEVICE: 'Notification/RegisterDevice',
  UNREGISTER_DEVICE: 'Notification/UnregisterDevice',
  UPDATE_DEVICE_TOKEN: 'Notification/UpdateDeviceToken'
};

// نقاط نهاية الإعدادات
export const SETTINGS_ENDPOINTS = {
  // إعدادات التطبيق
  GET_APP_SETTINGS: 'Settings/GetAppSettings',
  UPDATE_APP_SETTINGS: 'Settings/UpdateAppSettings',
  RESET_APP_SETTINGS: 'Settings/ResetAppSettings',
  
  // إعدادات المستخدم
  GET_USER_SETTINGS: 'Settings/GetUserSettings',
  UPDATE_USER_SETTINGS: 'Settings/UpdateUserSettings',
  
  // إعدادات الخصوصية
  GET_PRIVACY_SETTINGS: 'Settings/GetPrivacySettings',
  UPDATE_PRIVACY_SETTINGS: 'Settings/UpdatePrivacySettings',
  
  // إعدادات الإشعارات
  GET_NOTIFICATION_SETTINGS: 'Settings/GetNotificationSettings',
  UPDATE_NOTIFICATION_SETTINGS: 'Settings/UpdateNotificationSettings',
  
  // إعدادات الأمان
  GET_SECURITY_SETTINGS: 'Settings/GetSecuritySettings',
  UPDATE_SECURITY_SETTINGS: 'Settings/UpdateSecuritySettings',
  
  // النسخ الاحتياطي والاستعادة
  BACKUP_SETTINGS: 'Settings/BackupSettings',
  RESTORE_SETTINGS: 'Settings/RestoreSettings',
  
  // التصدير والاستيراد
  EXPORT_SETTINGS: 'Settings/ExportSettings',
  IMPORT_SETTINGS: 'Settings/ImportSettings'
};

// نقاط نهاية الإحصائيات والتقارير
export const ANALYTICS_ENDPOINTS = {
  // إحصائيات عامة
  GET_DASHBOARD_STATS: 'Analytics/GetDashboardStats',
  GET_USER_STATS: 'Analytics/GetUserStats',
  GET_CHAT_STATS: 'Analytics/GetChatStats',
  GET_MESSAGE_STATS: 'Analytics/GetMessageStats',
  
  // التقارير
  GENERATE_REPORT: 'Analytics/GenerateReport',
  GET_REPORT: (reportId) => `Analytics/GetReport/${reportId}`,
  DOWNLOAD_REPORT: (reportId) => `Analytics/DownloadReport/${reportId}`,
  
  // الأنشطة
  LOG_ACTIVITY: 'Analytics/LogActivity',
  GET_ACTIVITY_LOG: 'Analytics/GetActivityLog',
  
  // الأداء
  GET_PERFORMANCE_METRICS: 'Analytics/GetPerformanceMetrics',
  LOG_PERFORMANCE: 'Analytics/LogPerformance'
};

// نقاط نهاية الإدارة
export const ADMIN_ENDPOINTS = {
  // إدارة المستخدمين
  GET_ALL_USERS: 'Admin/GetAllUsers',
  BAN_USER: (userId) => `Admin/BanUser/${userId}`,
  UNBAN_USER: (userId) => `Admin/UnbanUser/${userId}`,
  DELETE_USER: (userId) => `Admin/DeleteUser/${userId}`,
  
  // إدارة المحتوى
  GET_REPORTED_CONTENT: 'Admin/GetReportedContent',
  MODERATE_CONTENT: (contentId) => `Admin/ModerateContent/${contentId}`,
  DELETE_CONTENT: (contentId) => `Admin/DeleteContent/${contentId}`,
  
  // إدارة النظام
  GET_SYSTEM_STATS: 'Admin/GetSystemStats',
  GET_SYSTEM_LOGS: 'Admin/GetSystemLogs',
  CLEAR_CACHE: 'Admin/ClearCache',
  RESTART_SERVICE: (serviceName) => `Admin/RestartService/${serviceName}`,
  
  // النسخ الاحتياطي
  CREATE_BACKUP: 'Admin/CreateBackup',
  RESTORE_BACKUP: (backupId) => `Admin/RestoreBackup/${backupId}`,
  GET_BACKUPS: 'Admin/GetBackups',
  
  // التحديثات
  CHECK_UPDATES: 'Admin/CheckUpdates',
  INSTALL_UPDATE: 'Admin/InstallUpdate',
  GET_UPDATE_STATUS: 'Admin/GetUpdateStatus'
};

// دالة مساعدة لبناء URL كامل
export function buildEndpointUrl(baseUrl, endpoint, params = {}) {
  let url = `${baseUrl.replace(/\/$/, '')}/${endpoint}`;
  
  // إضافة معاملات الاستعلام
  if (Object.keys(params).length > 0) {
    const searchParams = new URLSearchParams(params);
    url += `?${searchParams.toString()}`;
  }
  
  return url;
}

// دالة للحصول على جميع نقاط النهاية
export function getAllEndpoints() {
  return {
    AUTH: AUTH_ENDPOINTS,
    CHAT: CHAT_ENDPOINTS,
    MESSAGE: MESSAGE_ENDPOINTS,
    CONTACT: CONTACT_ENDPOINTS,
    USER: USER_ENDPOINTS,
    TEAM: TEAM_ENDPOINTS,
    FILE: FILE_ENDPOINTS,
    NOTIFICATION: NOTIFICATION_ENDPOINTS,
    SETTINGS: SETTINGS_ENDPOINTS,
    ANALYTICS: ANALYTICS_ENDPOINTS,
    ADMIN: ADMIN_ENDPOINTS
  };
}

// تصدير جميع نقاط النهاية كافتراضي
export default {
  AUTH: AUTH_ENDPOINTS,
  CHAT: CHAT_ENDPOINTS,
  MESSAGE: MESSAGE_ENDPOINTS,
  CONTACT: CONTACT_ENDPOINTS,
  USER: USER_ENDPOINTS,
  TEAM: TEAM_ENDPOINTS,
  FILE: FILE_ENDPOINTS,
  NOTIFICATION: NOTIFICATION_ENDPOINTS,
  SETTINGS: SETTINGS_ENDPOINTS,
  ANALYTICS: ANALYTICS_ENDPOINTS,
  ADMIN: ADMIN_ENDPOINTS,
  buildEndpointUrl,
  getAllEndpoints
};
