/**
 * مكون التحميل - LoadingComponent
 * يعرض مؤشرات تحميل مختلفة مع رسائل قابلة للتخصيص
 * يدعم أنواع مختلفة من الرسوم المتحركة والأحجام
 */

import BaseComponent from './BaseComponent.js';

// أنواع مؤشرات التحميل
export const LOADING_TYPES = {
  SPINNER: 'spinner',
  DOTS: 'dots',
  PULSE: 'pulse',
  BARS: 'bars',
  CIRCLE: 'circle',
  SKELETON: 'skeleton'
};

// أحجام مؤشرات التحميل
export const LOADING_SIZES = {
  SMALL: 'small',
  MEDIUM: 'medium',
  LARGE: 'large',
  EXTRA_LARGE: 'extra-large'
};

/**
 * كلاس مكون التحميل
 */
export class LoadingComponent extends BaseComponent {
  constructor(options = {}) {
    super(options);
    
    // إعدادات التحميل
    this.type = options.type || LOADING_TYPES.SPINNER;
    this.size = options.size || LOADING_SIZES.MEDIUM;
    this.message = options.message || 'جاري التحميل...';
    this.showMessage = options.showMessage !== false;
    this.overlay = options.overlay || false;
    this.color = options.color || '#007bff';
    this.backgroundColor = options.backgroundColor || 'rgba(255, 255, 255, 0.9)';
    
    // حالة التحميل
    this.isLoading = false;
    this.progress = options.progress || 0; // 0-100
    this.showProgress = options.showProgress || false;
    
    // إعدادات الرسوم المتحركة
    this.animationDuration = options.animationDuration || 1000;
    this.animationDelay = options.animationDelay || 0;
  }

  /**
   * الحصول على قالب HTML
   * @returns {string} قالب HTML
   */
  getTemplate() {
    const overlayClass = this.overlay ? 'loading-overlay' : '';
    const sizeClass = `loading-${this.size}`;
    const typeClass = `loading-${this.type}`;
    
    return `
      <div class="loading-component ${overlayClass} ${sizeClass} ${typeClass}" 
           data-type="${this.type}" 
           data-size="${this.size}">
        ${this._getLoadingIndicator()}
        ${this.showMessage ? this._getMessageElement() : ''}
        ${this.showProgress ? this._getProgressElement() : ''}
      </div>
    `;
  }

  /**
   * عرض مخصص
   */
  async onRender() {
    this._applyStyles();
    this._startAnimation();
    
    if (this.overlay) {
      this._setupOverlay();
    }
  }

  /**
   * تحديث مخصص
   */
  async onUpdate(newData) {
    if (newData.message !== undefined) {
      this.message = newData.message;
      this._updateMessage();
    }
    
    if (newData.progress !== undefined) {
      this.progress = Math.max(0, Math.min(100, newData.progress));
      this._updateProgress();
    }
    
    if (newData.type !== undefined && newData.type !== this.type) {
      this.type = newData.type;
      await this.render(); // إعادة عرض كاملة للنوع الجديد
    }
  }

  /**
   * بدء التحميل
   * @param {string} message - رسالة التحميل
   */
  start(message = null) {
    if (message) {
      this.message = message;
    }
    
    this.isLoading = true;
    this.show();
    this._startAnimation();
    
    if (this.showMessage) {
      this._updateMessage();
    }
    
    this.emit('loading:started', { message: this.message });
  }

  /**
   * إيقاف التحميل
   */
  stop() {
    this.isLoading = false;
    this._stopAnimation();
    this.hide();
    
    this.emit('loading:stopped');
  }

  /**
   * تحديث رسالة التحميل
   * @param {string} message - الرسالة الجديدة
   */
  updateMessage(message) {
    this.message = message;
    this._updateMessage();
    
    this.emit('loading:message:updated', { message });
  }

  /**
   * تحديث التقدم
   * @param {number} progress - نسبة التقدم (0-100)
   */
  updateProgress(progress) {
    this.progress = Math.max(0, Math.min(100, progress));
    this._updateProgress();
    
    this.emit('loading:progress:updated', { progress: this.progress });
  }

  /**
   * تغيير نوع مؤشر التحميل
   * @param {string} type - النوع الجديد
   */
  async changeType(type) {
    if (Object.values(LOADING_TYPES).includes(type)) {
      this.type = type;
      await this.render();
      
      this.emit('loading:type:changed', { type });
    }
  }

  /**
   * تغيير حجم مؤشر التحميل
   * @param {string} size - الحجم الجديد
   */
  changeSize(size) {
    if (Object.values(LOADING_SIZES).includes(size)) {
      this.size = size;
      
      if (this.element) {
        // إزالة الكلاس القديم
        Object.values(LOADING_SIZES).forEach(s => {
          this.element.classList.remove(`loading-${s}`);
        });
        
        // إضافة الكلاس الجديد
        this.element.classList.add(`loading-${size}`);
      }
      
      this.emit('loading:size:changed', { size });
    }
  }

  /**
   * الحصول على مؤشر التحميل حسب النوع
   * @private
   */
  _getLoadingIndicator() {
    switch (this.type) {
      case LOADING_TYPES.SPINNER:
        return this._getSpinnerIndicator();
      case LOADING_TYPES.DOTS:
        return this._getDotsIndicator();
      case LOADING_TYPES.PULSE:
        return this._getPulseIndicator();
      case LOADING_TYPES.BARS:
        return this._getBarsIndicator();
      case LOADING_TYPES.CIRCLE:
        return this._getCircleIndicator();
      case LOADING_TYPES.SKELETON:
        return this._getSkeletonIndicator();
      default:
        return this._getSpinnerIndicator();
    }
  }

  /**
   * مؤشر دوار
   * @private
   */
  _getSpinnerIndicator() {
    return `
      <div class="loading-spinner">
        <div class="spinner-border" role="status">
          <span class="sr-only">جاري التحميل...</span>
        </div>
      </div>
    `;
  }

  /**
   * مؤشر نقاط
   * @private
   */
  _getDotsIndicator() {
    return `
      <div class="loading-dots">
        <div class="dot dot-1"></div>
        <div class="dot dot-2"></div>
        <div class="dot dot-3"></div>
      </div>
    `;
  }

  /**
   * مؤشر نبضة
   * @private
   */
  _getPulseIndicator() {
    return `
      <div class="loading-pulse">
        <div class="pulse-circle"></div>
      </div>
    `;
  }

  /**
   * مؤشر أشرطة
   * @private
   */
  _getBarsIndicator() {
    return `
      <div class="loading-bars">
        <div class="bar bar-1"></div>
        <div class="bar bar-2"></div>
        <div class="bar bar-3"></div>
        <div class="bar bar-4"></div>
        <div class="bar bar-5"></div>
      </div>
    `;
  }

  /**
   * مؤشر دائري
   * @private
   */
  _getCircleIndicator() {
    return `
      <div class="loading-circle">
        <svg class="circular" viewBox="25 25 50 50">
          <circle class="path" cx="50" cy="50" r="20" fill="none" stroke="${this.color}" stroke-width="2" stroke-miterlimit="10"/>
        </svg>
      </div>
    `;
  }

  /**
   * مؤشر هيكلي
   * @private
   */
  _getSkeletonIndicator() {
    return `
      <div class="loading-skeleton">
        <div class="skeleton-line skeleton-line-1"></div>
        <div class="skeleton-line skeleton-line-2"></div>
        <div class="skeleton-line skeleton-line-3"></div>
      </div>
    `;
  }

  /**
   * عنصر الرسالة
   * @private
   */
  _getMessageElement() {
    return `
      <div class="loading-message">
        <span class="message-text">${this.message}</span>
      </div>
    `;
  }

  /**
   * عنصر التقدم
   * @private
   */
  _getProgressElement() {
    return `
      <div class="loading-progress">
        <div class="progress-bar">
          <div class="progress-fill" style="width: ${this.progress}%"></div>
        </div>
        <div class="progress-text">${this.progress}%</div>
      </div>
    `;
  }

  /**
   * تطبيق الأنماط
   * @private
   */
  _applyStyles() {
    if (!this.element) return;

    // إضافة الأنماط الأساسية
    const style = document.createElement('style');
    style.textContent = this._getStyles();
    document.head.appendChild(style);

    // تطبيق الألوان المخصصة
    this.element.style.setProperty('--loading-color', this.color);
    this.element.style.setProperty('--loading-bg-color', this.backgroundColor);
    this.element.style.setProperty('--animation-duration', `${this.animationDuration}ms`);
    this.element.style.setProperty('--animation-delay', `${this.animationDelay}ms`);
  }

  /**
   * الحصول على أنماط CSS
   * @private
   */
  _getStyles() {
    return `
      .loading-component {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 1rem;
        padding: 1rem;
        color: var(--loading-color, #007bff);
      }

      .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: var(--loading-bg-color, rgba(255, 255, 255, 0.9));
        z-index: 9999;
        backdrop-filter: blur(2px);
      }

      /* أحجام مختلفة */
      .loading-small { font-size: 0.8rem; }
      .loading-medium { font-size: 1rem; }
      .loading-large { font-size: 1.2rem; }
      .loading-extra-large { font-size: 1.5rem; }

      /* مؤشر دوار */
      .loading-spinner .spinner-border {
        width: 2em;
        height: 2em;
        border: 0.2em solid currentColor;
        border-right-color: transparent;
        border-radius: 50%;
        animation: spinner-rotate var(--animation-duration, 1000ms) linear infinite;
      }

      @keyframes spinner-rotate {
        to { transform: rotate(360deg); }
      }

      /* مؤشر نقاط */
      .loading-dots {
        display: flex;
        gap: 0.3em;
      }

      .loading-dots .dot {
        width: 0.5em;
        height: 0.5em;
        background-color: currentColor;
        border-radius: 50%;
        animation: dots-bounce var(--animation-duration, 1000ms) ease-in-out infinite;
      }

      .loading-dots .dot-2 { animation-delay: 0.2s; }
      .loading-dots .dot-3 { animation-delay: 0.4s; }

      @keyframes dots-bounce {
        0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
        40% { transform: scale(1.2); opacity: 1; }
      }

      /* مؤشر نبضة */
      .loading-pulse .pulse-circle {
        width: 2em;
        height: 2em;
        background-color: currentColor;
        border-radius: 50%;
        animation: pulse-scale var(--animation-duration, 1000ms) ease-in-out infinite;
      }

      @keyframes pulse-scale {
        0%, 100% { transform: scale(0.8); opacity: 0.5; }
        50% { transform: scale(1.2); opacity: 1; }
      }

      /* مؤشر أشرطة */
      .loading-bars {
        display: flex;
        gap: 0.2em;
        align-items: end;
      }

      .loading-bars .bar {
        width: 0.3em;
        height: 1.5em;
        background-color: currentColor;
        animation: bars-scale var(--animation-duration, 1000ms) ease-in-out infinite;
      }

      .loading-bars .bar-2 { animation-delay: 0.1s; }
      .loading-bars .bar-3 { animation-delay: 0.2s; }
      .loading-bars .bar-4 { animation-delay: 0.3s; }
      .loading-bars .bar-5 { animation-delay: 0.4s; }

      @keyframes bars-scale {
        0%, 40%, 100% { transform: scaleY(0.4); }
        20% { transform: scaleY(1); }
      }

      /* مؤشر دائري */
      .loading-circle .circular {
        width: 2em;
        height: 2em;
        animation: circle-rotate var(--animation-duration, 1000ms) linear infinite;
      }

      .loading-circle .path {
        stroke-dasharray: 90, 150;
        stroke-dashoffset: 0;
        stroke-linecap: round;
        animation: circle-dash calc(var(--animation-duration, 1000ms) * 1.5) ease-in-out infinite;
      }

      @keyframes circle-rotate {
        100% { transform: rotate(360deg); }
      }

      @keyframes circle-dash {
        0% { stroke-dasharray: 1, 150; stroke-dashoffset: 0; }
        50% { stroke-dasharray: 90, 150; stroke-dashoffset: -35; }
        100% { stroke-dasharray: 90, 150; stroke-dashoffset: -124; }
      }

      /* مؤشر هيكلي */
      .loading-skeleton {
        width: 100%;
        max-width: 200px;
      }

      .loading-skeleton .skeleton-line {
        height: 0.8em;
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        border-radius: 0.4em;
        margin-bottom: 0.5em;
        animation: skeleton-loading var(--animation-duration, 1000ms) ease-in-out infinite;
      }

      .loading-skeleton .skeleton-line-2 { width: 80%; }
      .loading-skeleton .skeleton-line-3 { width: 60%; }

      @keyframes skeleton-loading {
        0% { background-position: 200% 0; }
        100% { background-position: -200% 0; }
      }

      /* رسالة التحميل */
      .loading-message {
        text-align: center;
        font-weight: 500;
        opacity: 0.8;
      }

      /* شريط التقدم */
      .loading-progress {
        width: 100%;
        max-width: 200px;
        text-align: center;
      }

      .loading-progress .progress-bar {
        width: 100%;
        height: 0.5em;
        background-color: rgba(0, 0, 0, 0.1);
        border-radius: 0.25em;
        overflow: hidden;
        margin-bottom: 0.5em;
      }

      .loading-progress .progress-fill {
        height: 100%;
        background-color: currentColor;
        border-radius: 0.25em;
        transition: width 0.3s ease;
      }

      .loading-progress .progress-text {
        font-size: 0.8em;
        opacity: 0.7;
      }

      /* إخفاء النص المساعد */
      .sr-only {
        position: absolute;
        width: 1px;
        height: 1px;
        padding: 0;
        margin: -1px;
        overflow: hidden;
        clip: rect(0, 0, 0, 0);
        white-space: nowrap;
        border: 0;
      }
    `;
  }

  /**
   * بدء الرسوم المتحركة
   * @private
   */
  _startAnimation() {
    if (this.element) {
      this.element.style.animationPlayState = 'running';
    }
  }

  /**
   * إيقاف الرسوم المتحركة
   * @private
   */
  _stopAnimation() {
    if (this.element) {
      this.element.style.animationPlayState = 'paused';
    }
  }

  /**
   * إعداد الطبقة العلوية
   * @private
   */
  _setupOverlay() {
    if (this.element) {
      // منع التمرير في الخلفية
      document.body.style.overflow = 'hidden';
      
      // إضافة مستمع للنقر خارج المحتوى
      this.addEventListener(this.element, 'click', (e) => {
        if (e.target === this.element) {
          this.emit('loading:overlay:clicked');
        }
      });
    }
  }

  /**
   * تحديث الرسالة
   * @private
   */
  _updateMessage() {
    const messageElement = this.element?.querySelector('.message-text');
    if (messageElement) {
      messageElement.textContent = this.message;
    }
  }

  /**
   * تحديث التقدم
   * @private
   */
  _updateProgress() {
    const progressFill = this.element?.querySelector('.progress-fill');
    const progressText = this.element?.querySelector('.progress-text');
    
    if (progressFill) {
      progressFill.style.width = `${this.progress}%`;
    }
    
    if (progressText) {
      progressText.textContent = `${this.progress}%`;
    }
  }

  /**
   * تدمير مخصص
   */
  onDestroy() {
    // استعادة التمرير
    if (this.overlay) {
      document.body.style.overflow = '';
    }
    
    // إزالة الأنماط المضافة
    const styles = document.querySelectorAll('style');
    styles.forEach(style => {
      if (style.textContent.includes('.loading-component')) {
        style.remove();
      }
    });
  }

  /**
   * إنشاء مؤشر تحميل سريع
   * @static
   * @param {Object} options - خيارات المؤشر
   * @returns {LoadingComponent} مؤشر التحميل
   */
  static create(options = {}) {
    const loading = new LoadingComponent(options);
    loading.initialize();
    return loading;
  }

  /**
   * إظهار مؤشر تحميل مؤقت
   * @static
   * @param {string} message - رسالة التحميل
   * @param {number} duration - مدة العرض (مللي ثانية)
   * @param {Object} options - خيارات إضافية
   * @returns {Promise<LoadingComponent>} مؤشر التحميل
   */
  static async showTemporary(message = 'جاري التحميل...', duration = 3000, options = {}) {
    const loading = LoadingComponent.create({
      message,
      overlay: true,
      ...options
    });
    
    await loading.render();
    loading.start();
    
    setTimeout(() => {
      loading.stop();
      loading.destroy();
    }, duration);
    
    return loading;
  }
}

export default LoadingComponent;
export { LOADING_TYPES, LOADING_SIZES };
