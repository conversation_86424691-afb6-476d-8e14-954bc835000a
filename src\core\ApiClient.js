/**
 * عميل API - ApiClient
 * نظام شامل للتعامل مع طلبات API
 * يوفر آليات للمصادقة وإعادة المحاولة والتخزين المؤقت
 */

import { API, HTTP_STATUS } from '../config/constants.js';
import ApiResponseModel from '../models/ApiResponseModel.js';
import errorHandler from './ErrorHandler.js';
import eventBus from './EventBus.js';

/**
 * كلاس عميل API
 */
class ApiClient {
  constructor(baseURL = '') {
    // إعدادات الأساسية
    this.baseURL = baseURL.replace(/\/$/, '');
    this.timeout = API.TIMEOUT;
    this.retryAttempts = API.RETRY_ATTEMPTS;
    this.retryDelay = API.RETRY_DELAY;
    
    // رؤوس افتراضية
    this.defaultHeaders = { ...API.HEADERS };
    
    // معلومات المصادقة
    this.authToken = null;
    this.refreshToken = null;
    this.tokenType = 'Bearer';
    
    // إعدادات التخزين المؤقت
    this.cache = new Map();
    this.cacheEnabled = true;
    this.defaultCacheTTL = 5 * 60 * 1000; // 5 دقائق
    
    // طلبات نشطة
    this.activeRequests = new Map();
    this.maxConcurrentRequests = API.MAX_CONCURRENT_REQUESTS;
    
    // إعدادات التشخيص
    this.debug = false;
    
    // إحصائيات
    this.stats = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      cachedRequests: 0,
      retryAttempts: 0,
      averageResponseTime: 0,
      lastRequestTime: null
    };
    
    // معالجات الطلبات
    this.requestInterceptors = [];
    this.responseInterceptors = [];
    
    console.log('🌐 تم تهيئة عميل API');
  }

  /**
   * تعيين رمز المصادقة
   * @param {string} token - رمز المصادقة
   * @param {string} type - نوع الرمز
   */
  setAuthToken(token, type = 'Bearer') {
    this.authToken = token;
    this.tokenType = type;
    
    if (token) {
      this.defaultHeaders['Authorization'] = `${type} ${token}`;
    } else {
      delete this.defaultHeaders['Authorization'];
    }
    
    if (this.debug) {
      console.log('🔐 تم تعيين رمز المصادقة');
    }
  }

  /**
   * تعيين رمز التحديث
   * @param {string} refreshToken - رمز التحديث
   */
  setRefreshToken(refreshToken) {
    this.refreshToken = refreshToken;
  }

  /**
   * إضافة رأس افتراضي
   * @param {string} name - اسم الرأس
   * @param {string} value - قيمة الرأس
   */
  setHeader(name, value) {
    this.defaultHeaders[name] = value;
  }

  /**
   * إزالة رأس افتراضي
   * @param {string} name - اسم الرأس
   */
  removeHeader(name) {
    delete this.defaultHeaders[name];
  }

  /**
   * إضافة معالج طلب
   * @param {Function} interceptor - معالج الطلب
   */
  addRequestInterceptor(interceptor) {
    this.requestInterceptors.push(interceptor);
  }

  /**
   * إضافة معالج استجابة
   * @param {Function} interceptor - معالج الاستجابة
   */
  addResponseInterceptor(interceptor) {
    this.responseInterceptors.push(interceptor);
  }

  /**
   * طلب GET
   * @param {string} endpoint - نقطة النهاية
   * @param {Object} options - خيارات الطلب
   * @returns {Promise<ApiResponseModel>} الاستجابة
   */
  async get(endpoint, options = {}) {
    return this.request('GET', endpoint, null, options);
  }

  /**
   * طلب POST
   * @param {string} endpoint - نقطة النهاية
   * @param {Object} data - البيانات
   * @param {Object} options - خيارات الطلب
   * @returns {Promise<ApiResponseModel>} الاستجابة
   */
  async post(endpoint, data = null, options = {}) {
    return this.request('POST', endpoint, data, options);
  }

  /**
   * طلب PUT
   * @param {string} endpoint - نقطة النهاية
   * @param {Object} data - البيانات
   * @param {Object} options - خيارات الطلب
   * @returns {Promise<ApiResponseModel>} الاستجابة
   */
  async put(endpoint, data = null, options = {}) {
    return this.request('PUT', endpoint, data, options);
  }

  /**
   * طلب PATCH
   * @param {string} endpoint - نقطة النهاية
   * @param {Object} data - البيانات
   * @param {Object} options - خيارات الطلب
   * @returns {Promise<ApiResponseModel>} الاستجابة
   */
  async patch(endpoint, data = null, options = {}) {
    return this.request('PATCH', endpoint, data, options);
  }

  /**
   * طلب DELETE
   * @param {string} endpoint - نقطة النهاية
   * @param {Object} options - خيارات الطلب
   * @returns {Promise<ApiResponseModel>} الاستجابة
   */
  async delete(endpoint, options = {}) {
    return this.request('DELETE', endpoint, null, options);
  }

  /**
   * طلب عام
   * @param {string} method - طريقة HTTP
   * @param {string} endpoint - نقطة النهاية
   * @param {Object} data - البيانات
   * @param {Object} options - خيارات الطلب
   * @returns {Promise<ApiResponseModel>} الاستجابة
   */
  async request(method, endpoint, data = null, options = {}) {
    const startTime = performance.now();
    const requestId = this._generateRequestId();
    
    try {
      // التحقق من الحد الأقصى للطلبات المتزامنة
      if (this.activeRequests.size >= this.maxConcurrentRequests) {
        throw new Error('تم تجاوز الحد الأقصى للطلبات المتزامنة');
      }

      // بناء URL الكامل
      const url = this._buildURL(endpoint, options.params);
      
      // التحقق من التخزين المؤقت
      if (method === 'GET' && this.cacheEnabled && !options.skipCache) {
        const cachedResponse = this._getCachedResponse(url);
        if (cachedResponse) {
          this.stats.cachedRequests++;
          return cachedResponse;
        }
      }

      // إعداد الطلب
      const requestConfig = await this._buildRequestConfig(method, url, data, options);
      
      // تشغيل معالجات الطلب
      await this._runRequestInterceptors(requestConfig);
      
      // تسجيل الطلب النشط
      this.activeRequests.set(requestId, {
        method,
        url,
        startTime,
        abortController: requestConfig.abortController
      });

      // تنفيذ الطلب مع إعادة المحاولة
      const response = await this._executeWithRetry(requestConfig, options);
      
      // معالجة الاستجابة
      const apiResponse = await this._processResponse(response, requestConfig);
      
      // تشغيل معالجات الاستجابة
      await this._runResponseInterceptors(apiResponse, requestConfig);
      
      // حفظ في التخزين المؤقت
      if (method === 'GET' && this.cacheEnabled && apiResponse.isSuccess()) {
        this._cacheResponse(url, apiResponse, options.cacheTTL);
      }
      
      // تحديث الإحصائيات
      this._updateStats(true, performance.now() - startTime);
      
      if (this.debug) {
        console.log(`✅ طلب ناجح: ${method} ${url}`, apiResponse);
      }

      return apiResponse;

    } catch (error) {
      this._updateStats(false, performance.now() - startTime);
      
      const apiError = this._handleRequestError(error, method, endpoint, options);
      
      if (this.debug) {
        console.error(`❌ فشل الطلب: ${method} ${endpoint}`, error);
      }

      throw apiError;

    } finally {
      // إزالة الطلب من القائمة النشطة
      this.activeRequests.delete(requestId);
    }
  }

  /**
   * رفع ملف
   * @param {string} endpoint - نقطة النهاية
   * @param {File|FormData} fileOrFormData - الملف أو FormData
   * @param {Object} options - خيارات الرفع
   * @returns {Promise<ApiResponseModel>} الاستجابة
   */
  async uploadFile(endpoint, fileOrFormData, options = {}) {
    let formData;
    
    if (fileOrFormData instanceof FormData) {
      formData = fileOrFormData;
    } else {
      formData = new FormData();
      formData.append('file', fileOrFormData);
      
      // إضافة بيانات إضافية
      if (options.data) {
        Object.entries(options.data).forEach(([key, value]) => {
          formData.append(key, value);
        });
      }
    }

    const uploadOptions = {
      ...options,
      headers: {
        // إزالة Content-Type ليتم تعيينه تلقائياً
        ...options.headers
      },
      onUploadProgress: options.onProgress
    };

    // إزالة Content-Type من الرؤوس
    delete uploadOptions.headers['Content-Type'];

    return this.post(endpoint, formData, uploadOptions);
  }

  /**
   * تحميل ملف
   * @param {string} endpoint - نقطة النهاية
   * @param {Object} options - خيارات التحميل
   * @returns {Promise<Blob>} الملف المحمل
   */
  async downloadFile(endpoint, options = {}) {
    const response = await this.request('GET', endpoint, null, {
      ...options,
      responseType: 'blob'
    });

    if (response.isSuccess()) {
      return response.getData();
    } else {
      throw new Error('فشل في تحميل الملف');
    }
  }

  /**
   * إلغاء جميع الطلبات النشطة
   */
  cancelAllRequests() {
    for (const [requestId, request] of this.activeRequests) {
      if (request.abortController) {
        request.abortController.abort();
      }
    }
    
    this.activeRequests.clear();
    
    console.log('🚫 تم إلغاء جميع الطلبات النشطة');
  }

  /**
   * إلغاء طلب محدد
   * @param {string} requestId - معرف الطلب
   */
  cancelRequest(requestId) {
    const request = this.activeRequests.get(requestId);
    if (request && request.abortController) {
      request.abortController.abort();
      this.activeRequests.delete(requestId);
    }
  }

  /**
   * مسح التخزين المؤقت
   * @param {string} pattern - نمط URL للمسح (اختياري)
   */
  clearCache(pattern = null) {
    if (pattern) {
      for (const [url] of this.cache) {
        if (url.includes(pattern)) {
          this.cache.delete(url);
        }
      }
    } else {
      this.cache.clear();
    }
    
    console.log('🧹 تم مسح التخزين المؤقت');
  }

  /**
   * تحديث رمز المصادقة
   * @returns {Promise<boolean>} نجح التحديث أم لا
   */
  async refreshAuthToken() {
    if (!this.refreshToken) {
      throw new Error('رمز التحديث غير متوفر');
    }

    try {
      const response = await this.post('auth/refresh', {
        refreshToken: this.refreshToken
      }, { skipAuth: true });

      if (response.isSuccess()) {
        const data = response.getData();
        this.setAuthToken(data.accessToken, data.tokenType);
        
        if (data.refreshToken) {
          this.setRefreshToken(data.refreshToken);
        }

        eventBus.emit('auth:token:refreshed', data);
        
        return true;
      }

      return false;

    } catch (error) {
      eventBus.emit('auth:token:refresh:failed', error);
      throw error;
    }
  }

  /**
   * الحصول على إحصائيات العميل
   * @returns {Object} الإحصائيات
   */
  getStats() {
    return {
      ...this.stats,
      activeRequests: this.activeRequests.size,
      cacheSize: this.cache.size,
      hasAuthToken: !!this.authToken
    };
  }

  /**
   * تفعيل/إلغاء وضع التشخيص
   * @param {boolean} enabled - تفعيل التشخيص
   */
  setDebug(enabled) {
    this.debug = enabled;
    console.log(`🔍 وضع التشخيص لعميل API: ${enabled ? 'مفعل' : 'معطل'}`);
  }

  // دوال خاصة

  /**
   * بناء URL الكامل
   * @private
   */
  _buildURL(endpoint, params = {}) {
    let url = endpoint.startsWith('http') 
      ? endpoint 
      : `${this.baseURL}/${endpoint.replace(/^\//, '')}`;

    // إضافة معاملات الاستعلام
    if (params && Object.keys(params).length > 0) {
      const searchParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        if (value !== null && value !== undefined) {
          searchParams.append(key, value.toString());
        }
      });
      
      const paramString = searchParams.toString();
      if (paramString) {
        url += (url.includes('?') ? '&' : '?') + paramString;
      }
    }

    return url;
  }

  /**
   * بناء إعدادات الطلب
   * @private
   */
  async _buildRequestConfig(method, url, data, options) {
    const abortController = new AbortController();
    
    const config = {
      method,
      url,
      headers: { ...this.defaultHeaders, ...options.headers },
      signal: abortController.signal,
      abortController
    };

    // إضافة البيانات
    if (data !== null) {
      if (data instanceof FormData) {
        config.body = data;
        // إزالة Content-Type للـ FormData
        delete config.headers['Content-Type'];
      } else if (typeof data === 'object') {
        config.body = JSON.stringify(data);
        config.headers['Content-Type'] = 'application/json';
      } else {
        config.body = data;
      }
    }

    // إضافة المصادقة
    if (!options.skipAuth && this.authToken) {
      config.headers['Authorization'] = `${this.tokenType} ${this.authToken}`;
    }

    // إعداد المهلة الزمنية
    if (options.timeout || this.timeout) {
      setTimeout(() => {
        abortController.abort();
      }, options.timeout || this.timeout);
    }

    return config;
  }

  /**
   * تنفيذ الطلب مع إعادة المحاولة
   * @private
   */
  async _executeWithRetry(config, options) {
    let lastError;
    const maxAttempts = options.retryAttempts ?? this.retryAttempts;

    for (let attempt = 0; attempt <= maxAttempts; attempt++) {
      try {
        if (attempt > 0) {
          // انتظار قبل إعادة المحاولة
          await this._delay(options.retryDelay ?? this.retryDelay);
          this.stats.retryAttempts++;
          
          if (this.debug) {
            console.log(`🔄 إعادة محاولة ${attempt}/${maxAttempts} للطلب: ${config.url}`);
          }
        }

        const response = await fetch(config.url, config);
        
        // التحقق من حالة الاستجابة
        if (response.status === HTTP_STATUS.UNAUTHORIZED && this.refreshToken && !options.skipAuth) {
          // محاولة تحديث الرمز
          await this.refreshAuthToken();
          
          // إعادة تعيين رأس المصادقة
          config.headers['Authorization'] = `${this.tokenType} ${this.authToken}`;
          
          // إعادة المحاولة
          return fetch(config.url, config);
        }

        return response;

      } catch (error) {
        lastError = error;
        
        // عدم إعادة المحاولة في حالات معينة
        if (error.name === 'AbortError' || attempt === maxAttempts) {
          break;
        }
      }
    }

    throw lastError;
  }

  /**
   * معالجة الاستجابة
   * @private
   */
  async _processResponse(response, config) {
    const responseData = {
      statusCode: response.status,
      timestamp: new Date(),
      requestId: this._generateRequestId()
    };

    try {
      // قراءة محتوى الاستجابة
      const contentType = response.headers.get('content-type');
      
      if (contentType && contentType.includes('application/json')) {
        const jsonData = await response.json();
        Object.assign(responseData, jsonData);
      } else if (config.responseType === 'blob') {
        responseData.data = await response.blob();
      } else {
        responseData.data = await response.text();
      }

      // إنشاء نموذج الاستجابة
      const apiResponse = new ApiResponseModel(responseData);

      // تحديد حالة النجاح/الفشل
      if (response.ok) {
        apiResponse.status = 'success';
      } else {
        apiResponse.status = 'error';
        apiResponse.addError(responseData.message || `HTTP ${response.status}`);
      }

      return apiResponse;

    } catch (error) {
      // خطأ في معالجة الاستجابة
      return ApiResponseModel.error(
        'خطأ في معالجة الاستجابة',
        'RESPONSE_PROCESSING_ERROR',
        response.status
      );
    }
  }

  /**
   * تشغيل معالجات الطلب
   * @private
   */
  async _runRequestInterceptors(config) {
    for (const interceptor of this.requestInterceptors) {
      try {
        await interceptor(config);
      } catch (error) {
        console.error('❌ خطأ في معالج الطلب:', error);
      }
    }
  }

  /**
   * تشغيل معالجات الاستجابة
   * @private
   */
  async _runResponseInterceptors(response, config) {
    for (const interceptor of this.responseInterceptors) {
      try {
        await interceptor(response, config);
      } catch (error) {
        console.error('❌ خطأ في معالج الاستجابة:', error);
      }
    }
  }

  /**
   * الحصول من التخزين المؤقت
   * @private
   */
  _getCachedResponse(url) {
    const cached = this.cache.get(url);
    if (cached && cached.expiresAt > Date.now()) {
      return cached.response.clone();
    } else if (cached) {
      this.cache.delete(url);
    }
    return null;
  }

  /**
   * حفظ في التخزين المؤقت
   * @private
   */
  _cacheResponse(url, response, ttl = null) {
    const cacheTTL = ttl || this.defaultCacheTTL;
    this.cache.set(url, {
      response: response.clone(),
      expiresAt: Date.now() + cacheTTL
    });
  }

  /**
   * معالجة خطأ الطلب
   * @private
   */
  _handleRequestError(error, method, endpoint, options) {
    const errorContext = {
      method,
      endpoint,
      options,
      timestamp: new Date()
    };

    if (error.name === 'AbortError') {
      return ApiResponseModel.error('تم إلغاء الطلب', 'REQUEST_CANCELLED', 0, errorContext);
    } else if (error.name === 'TypeError' && error.message.includes('fetch')) {
      return ApiResponseModel.error('خطأ في الشبكة', 'NETWORK_ERROR', 0, errorContext);
    } else {
      return ApiResponseModel.error(error.message, 'REQUEST_ERROR', 0, errorContext);
    }
  }

  /**
   * تحديث الإحصائيات
   * @private
   */
  _updateStats(success, responseTime) {
    this.stats.totalRequests++;
    this.stats.lastRequestTime = new Date();
    
    if (success) {
      this.stats.successfulRequests++;
    } else {
      this.stats.failedRequests++;
    }

    // حساب متوسط وقت الاستجابة
    const totalResponseTime = this.stats.averageResponseTime * (this.stats.totalRequests - 1) + responseTime;
    this.stats.averageResponseTime = totalResponseTime / this.stats.totalRequests;
  }

  /**
   * إنشاء معرف طلب فريد
   * @private
   */
  _generateRequestId() {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * تأخير لفترة محددة
   * @private
   */
  _delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// إنشاء مثيل افتراضي من عميل API
const apiClient = new ApiClient();

// تصدير المثيل والكلاس
export default apiClient;
export { ApiClient };
