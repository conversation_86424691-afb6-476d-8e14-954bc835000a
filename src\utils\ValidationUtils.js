/**
 * أدوات التحقق من صحة البيانات - ValidationUtils
 * يوفر دوال شاملة للتحقق من صحة البيانات المختلفة
 * يدعم التحقق من النصوص والأرقام والتواريخ والملفات
 */

/**
 * التحقق من كون النص غير فارغ
 * @param {string} value - النص المراد فحصه
 * @param {boolean} allowWhitespace - السماح بالمسافات البيضاء فقط
 * @returns {boolean} صحة النص
 */
export function isNotEmpty(value, allowWhitespace = false) {
  if (typeof value !== 'string') return false;
  
  return allowWhitespace ? value.length > 0 : value.trim().length > 0;
}

/**
 * التحقق من طول النص
 * @param {string} value - النص
 * @param {number} minLength - الحد الأدنى للطول
 * @param {number} maxLength - الحد الأقصى للطول
 * @returns {boolean} صحة الطول
 */
export function isValidLength(value, minLength = 0, maxLength = Infinity) {
  if (typeof value !== 'string') return false;
  
  const length = value.trim().length;
  return length >= minLength && length <= maxLength;
}

/**
 * التحقق من صحة البريد الإلكتروني
 * @param {string} email - البريد الإلكتروني
 * @returns {boolean} صحة البريد الإلكتروني
 */
export function isValidEmail(email) {
  if (typeof email !== 'string') return false;
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email.trim());
}

/**
 * التحقق من صحة رقم الهاتف (صيغة عربية/دولية)
 * @param {string} phone - رقم الهاتف
 * @returns {boolean} صحة رقم الهاتف
 */
export function isValidPhone(phone) {
  if (typeof phone !== 'string') return false;
  
  // إزالة المسافات والرموز
  const cleanPhone = phone.replace(/[\s\-\(\)]/g, '');
  
  // التحقق من الأرقام السعودية والدولية
  const phoneRegex = /^(\+?966|0)?[5][0-9]{8}$|^(\+?[1-9]\d{1,14})$/;
  return phoneRegex.test(cleanPhone);
}

/**
 * التحقق من صحة كلمة المرور
 * @param {string} password - كلمة المرور
 * @param {Object} requirements - متطلبات كلمة المرور
 * @returns {Object} نتيجة التحقق مع التفاصيل
 */
export function isValidPassword(password, requirements = {}) {
  const defaultRequirements = {
    minLength: 8,
    maxLength: 128,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: true,
    allowSpaces: false
  };
  
  const reqs = { ...defaultRequirements, ...requirements };
  const result = {
    isValid: true,
    errors: [],
    strength: 0
  };
  
  if (typeof password !== 'string') {
    result.isValid = false;
    result.errors.push('كلمة المرور يجب أن تكون نص');
    return result;
  }
  
  // التحقق من الطول
  if (password.length < reqs.minLength) {
    result.isValid = false;
    result.errors.push(`كلمة المرور يجب أن تكون ${reqs.minLength} أحرف على الأقل`);
  }
  
  if (password.length > reqs.maxLength) {
    result.isValid = false;
    result.errors.push(`كلمة المرور يجب أن تكون ${reqs.maxLength} حرف كحد أقصى`);
  }
  
  // التحقق من الأحرف الكبيرة
  if (reqs.requireUppercase && !/[A-Z]/.test(password)) {
    result.isValid = false;
    result.errors.push('كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل');
  } else if (/[A-Z]/.test(password)) {
    result.strength += 1;
  }
  
  // التحقق من الأحرف الصغيرة
  if (reqs.requireLowercase && !/[a-z]/.test(password)) {
    result.isValid = false;
    result.errors.push('كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل');
  } else if (/[a-z]/.test(password)) {
    result.strength += 1;
  }
  
  // التحقق من الأرقام
  if (reqs.requireNumbers && !/[0-9]/.test(password)) {
    result.isValid = false;
    result.errors.push('كلمة المرور يجب أن تحتوي على رقم واحد على الأقل');
  } else if (/[0-9]/.test(password)) {
    result.strength += 1;
  }
  
  // التحقق من الرموز الخاصة
  if (reqs.requireSpecialChars && !/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    result.isValid = false;
    result.errors.push('كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل');
  } else if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    result.strength += 1;
  }
  
  // التحقق من المسافات
  if (!reqs.allowSpaces && /\s/.test(password)) {
    result.isValid = false;
    result.errors.push('كلمة المرور لا يجب أن تحتوي على مسافات');
  }
  
  // حساب قوة كلمة المرور
  if (password.length >= 12) result.strength += 1;
  if (password.length >= 16) result.strength += 1;
  
  return result;
}

/**
 * التحقق من صحة الرقم
 * @param {*} value - القيمة
 * @param {number} min - الحد الأدنى
 * @param {number} max - الحد الأقصى
 * @returns {boolean} صحة الرقم
 */
export function isValidNumber(value, min = -Infinity, max = Infinity) {
  const num = Number(value);
  return !isNaN(num) && isFinite(num) && num >= min && num <= max;
}

/**
 * التحقق من صحة الرقم الصحيح
 * @param {*} value - القيمة
 * @param {number} min - الحد الأدنى
 * @param {number} max - الحد الأقصى
 * @returns {boolean} صحة الرقم الصحيح
 */
export function isValidInteger(value, min = -Infinity, max = Infinity) {
  const num = Number(value);
  return Number.isInteger(num) && num >= min && num <= max;
}

/**
 * التحقق من صحة التاريخ
 * @param {*} date - التاريخ
 * @param {Date} minDate - أقل تاريخ مسموح
 * @param {Date} maxDate - أكبر تاريخ مسموح
 * @returns {boolean} صحة التاريخ
 */
export function isValidDate(date, minDate = null, maxDate = null) {
  let dateObj;
  
  if (date instanceof Date) {
    dateObj = date;
  } else if (typeof date === 'string' || typeof date === 'number') {
    dateObj = new Date(date);
  } else {
    return false;
  }
  
  // التحقق من صحة التاريخ
  if (isNaN(dateObj.getTime())) return false;
  
  // التحقق من الحدود
  if (minDate && dateObj < minDate) return false;
  if (maxDate && dateObj > maxDate) return false;
  
  return true;
}

/**
 * التحقق من صحة URL
 * @param {string} url - الرابط
 * @param {Array} allowedProtocols - البروتوكولات المسموحة
 * @returns {boolean} صحة الرابط
 */
export function isValidURL(url, allowedProtocols = ['http', 'https']) {
  if (typeof url !== 'string') return false;
  
  try {
    const urlObj = new URL(url);
    return allowedProtocols.includes(urlObj.protocol.slice(0, -1));
  } catch {
    return false;
  }
}

/**
 * التحقق من صحة الملف
 * @param {File} file - الملف
 * @param {Object} options - خيارات التحقق
 * @returns {Object} نتيجة التحقق
 */
export function isValidFile(file, options = {}) {
  const defaultOptions = {
    maxSize: 10 * 1024 * 1024, // 10 ميجابايت
    allowedTypes: [],
    allowedExtensions: [],
    requireExtension: false
  };
  
  const opts = { ...defaultOptions, ...options };
  const result = {
    isValid: true,
    errors: []
  };
  
  if (!(file instanceof File)) {
    result.isValid = false;
    result.errors.push('الملف غير صحيح');
    return result;
  }
  
  // التحقق من الحجم
  if (file.size > opts.maxSize) {
    result.isValid = false;
    result.errors.push(`حجم الملف يجب أن يكون أقل من ${formatFileSize(opts.maxSize)}`);
  }
  
  // التحقق من نوع الملف
  if (opts.allowedTypes.length > 0 && !opts.allowedTypes.includes(file.type)) {
    result.isValid = false;
    result.errors.push('نوع الملف غير مدعوم');
  }
  
  // التحقق من امتداد الملف
  if (opts.allowedExtensions.length > 0) {
    const extension = getFileExtension(file.name);
    if (!opts.allowedExtensions.includes(extension)) {
      result.isValid = false;
      result.errors.push('امتداد الملف غير مدعوم');
    }
  }
  
  // التحقق من وجود امتداد
  if (opts.requireExtension && !getFileExtension(file.name)) {
    result.isValid = false;
    result.errors.push('الملف يجب أن يحتوي على امتداد');
  }
  
  return result;
}

/**
 * التحقق من صحة JSON
 * @param {string} jsonString - نص JSON
 * @returns {Object} نتيجة التحقق مع البيانات المحللة
 */
export function isValidJSON(jsonString) {
  const result = {
    isValid: true,
    data: null,
    error: null
  };
  
  if (typeof jsonString !== 'string') {
    result.isValid = false;
    result.error = 'البيانات يجب أن تكون نص';
    return result;
  }
  
  try {
    result.data = JSON.parse(jsonString);
  } catch (error) {
    result.isValid = false;
    result.error = 'تنسيق JSON غير صحيح';
  }
  
  return result;
}

/**
 * التحقق من صحة الرقم القومي السعودي
 * @param {string} nationalId - الرقم القومي
 * @returns {boolean} صحة الرقم القومي
 */
export function isValidSaudiNationalId(nationalId) {
  if (typeof nationalId !== 'string') return false;
  
  // إزالة المسافات
  const cleanId = nationalId.replace(/\s/g, '');
  
  // التحقق من الطول والأرقام
  if (!/^\d{10}$/.test(cleanId)) return false;
  
  // خوارزمية التحقق من الرقم القومي السعودي
  const digits = cleanId.split('').map(Number);
  let sum = 0;
  
  for (let i = 0; i < 9; i++) {
    if (i % 2 === 0) {
      const doubled = digits[i] * 2;
      sum += doubled > 9 ? doubled - 9 : doubled;
    } else {
      sum += digits[i];
    }
  }
  
  const checkDigit = (10 - (sum % 10)) % 10;
  return checkDigit === digits[9];
}

/**
 * التحقق من صحة اسم المستخدم
 * @param {string} username - اسم المستخدم
 * @param {Object} options - خيارات التحقق
 * @returns {Object} نتيجة التحقق
 */
export function isValidUsername(username, options = {}) {
  const defaultOptions = {
    minLength: 3,
    maxLength: 30,
    allowNumbers: true,
    allowUnderscore: true,
    allowDash: true,
    allowDots: false,
    requireLetters: true,
    caseSensitive: false
  };
  
  const opts = { ...defaultOptions, ...options };
  const result = {
    isValid: true,
    errors: []
  };
  
  if (typeof username !== 'string') {
    result.isValid = false;
    result.errors.push('اسم المستخدم يجب أن يكون نص');
    return result;
  }
  
  const cleanUsername = opts.caseSensitive ? username : username.toLowerCase();
  
  // التحقق من الطول
  if (cleanUsername.length < opts.minLength) {
    result.isValid = false;
    result.errors.push(`اسم المستخدم يجب أن يكون ${opts.minLength} أحرف على الأقل`);
  }
  
  if (cleanUsername.length > opts.maxLength) {
    result.isValid = false;
    result.errors.push(`اسم المستخدم يجب أن يكون ${opts.maxLength} حرف كحد أقصى`);
  }
  
  // بناء نمط التحقق
  let pattern = '^[a-zA-Z';
  if (opts.allowNumbers) pattern += '0-9';
  if (opts.allowUnderscore) pattern += '_';
  if (opts.allowDash) pattern += '-';
  if (opts.allowDots) pattern += '.';
  pattern += ']+$';
  
  const regex = new RegExp(pattern);
  if (!regex.test(cleanUsername)) {
    result.isValid = false;
    result.errors.push('اسم المستخدم يحتوي على أحرف غير مسموحة');
  }
  
  // التحقق من وجود أحرف
  if (opts.requireLetters && !/[a-zA-Z]/.test(cleanUsername)) {
    result.isValid = false;
    result.errors.push('اسم المستخدم يجب أن يحتوي على أحرف');
  }
  
  return result;
}

/**
 * تنظيف وتطهير النص من الأحرف الضارة
 * @param {string} input - النص المدخل
 * @param {Object} options - خيارات التنظيف
 * @returns {string} النص المنظف
 */
export function sanitizeInput(input, options = {}) {
  if (typeof input !== 'string') return '';
  
  const defaultOptions = {
    removeHTML: true,
    removeScripts: true,
    trimWhitespace: true,
    removeExtraSpaces: true,
    maxLength: null
  };
  
  const opts = { ...defaultOptions, ...options };
  let cleaned = input;
  
  // إزالة HTML
  if (opts.removeHTML) {
    cleaned = cleaned.replace(/<[^>]*>/g, '');
  }
  
  // إزالة السكريبتات
  if (opts.removeScripts) {
    cleaned = cleaned.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
  }
  
  // تنظيف المسافات
  if (opts.trimWhitespace) {
    cleaned = cleaned.trim();
  }
  
  if (opts.removeExtraSpaces) {
    cleaned = cleaned.replace(/\s+/g, ' ');
  }
  
  // تحديد الطول
  if (opts.maxLength && cleaned.length > opts.maxLength) {
    cleaned = cleaned.substring(0, opts.maxLength);
  }
  
  return cleaned;
}

/**
 * التحقق من صحة كائن حسب مخطط معين
 * @param {Object} obj - الكائن المراد فحصه
 * @param {Object} schema - مخطط التحقق
 * @returns {Object} نتيجة التحقق
 */
export function validateObject(obj, schema) {
  const result = {
    isValid: true,
    errors: {},
    hasErrors: false
  };
  
  if (typeof obj !== 'object' || obj === null) {
    result.isValid = false;
    result.errors._root = ['الكائن غير صحيح'];
    result.hasErrors = true;
    return result;
  }
  
  // التحقق من كل حقل في المخطط
  Object.entries(schema).forEach(([field, rules]) => {
    const value = obj[field];
    const fieldErrors = [];
    
    // التحقق من الحقول المطلوبة
    if (rules.required && (value === undefined || value === null || value === '')) {
      fieldErrors.push(`${field} مطلوب`);
    }
    
    // إذا كان الحقل فارغ وغير مطلوب، تخطي باقي التحققات
    if (!rules.required && (value === undefined || value === null || value === '')) {
      return;
    }
    
    // التحقق من النوع
    if (rules.type && typeof value !== rules.type) {
      fieldErrors.push(`${field} يجب أن يكون من نوع ${rules.type}`);
    }
    
    // التحقق من الطول (للنصوص والمصفوفات)
    if (rules.minLength && value.length < rules.minLength) {
      fieldErrors.push(`${field} يجب أن يكون ${rules.minLength} أحرف على الأقل`);
    }
    
    if (rules.maxLength && value.length > rules.maxLength) {
      fieldErrors.push(`${field} يجب أن يكون ${rules.maxLength} حرف كحد أقصى`);
    }
    
    // التحقق من القيم (للأرقام)
    if (rules.min && value < rules.min) {
      fieldErrors.push(`${field} يجب أن يكون ${rules.min} أو أكثر`);
    }
    
    if (rules.max && value > rules.max) {
      fieldErrors.push(`${field} يجب أن يكون ${rules.max} أو أقل`);
    }
    
    // التحقق من النمط
    if (rules.pattern && !rules.pattern.test(value)) {
      fieldErrors.push(`${field} لا يطابق النمط المطلوب`);
    }
    
    // التحقق المخصص
    if (rules.validator && typeof rules.validator === 'function') {
      const customResult = rules.validator(value);
      if (customResult !== true) {
        fieldErrors.push(customResult || `${field} غير صحيح`);
      }
    }
    
    // إضافة الأخطاء إذا وجدت
    if (fieldErrors.length > 0) {
      result.errors[field] = fieldErrors;
      result.hasErrors = true;
      result.isValid = false;
    }
  });
  
  return result;
}

// دوال مساعدة

/**
 * تنسيق حجم الملف
 * @param {number} bytes - الحجم بالبايت
 * @returns {string} الحجم المنسق
 */
function formatFileSize(bytes) {
  if (bytes === 0) return '0 بايت';
  
  const k = 1024;
  const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * الحصول على امتداد الملف
 * @param {string} filename - اسم الملف
 * @returns {string} امتداد الملف
 */
function getFileExtension(filename) {
  if (typeof filename !== 'string') return '';
  
  const lastDot = filename.lastIndexOf('.');
  return lastDot === -1 ? '' : filename.substring(lastDot + 1).toLowerCase();
}

// تصدير جميع الدوال
export default {
  isNotEmpty,
  isValidLength,
  isValidEmail,
  isValidPhone,
  isValidPassword,
  isValidNumber,
  isValidInteger,
  isValidDate,
  isValidURL,
  isValidFile,
  isValidJSON,
  isValidSaudiNationalId,
  isValidUsername,
  sanitizeInput,
  validateObject
};
